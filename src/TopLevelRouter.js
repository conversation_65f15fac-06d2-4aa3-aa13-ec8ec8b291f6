import { BrowserRouter, Routes, Route } from "react-router-dom";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import Spinner from 'react-bootstrap/Spinner';
import "./TopLevelRouter.css";
import React from "react";


import App from './App';
import ELogbook from './Pages/ELogbook';
import LoginPanel from './Pages/LoginPanel';
import AuditForm from './Pages/AuditForm';
import LearningContract from './Pages/LearningContract';
import UnderConstruction from "./Pages/UnderConstruction";
import ExtraActivities from "./Pages/ExtraActivities";
import AuditorChangePassword from "./Pages/AuditorChangePassword";
import OnlineApplication from "./Pages/OnlineApplication";
import UserChangePassword from "./Pages/UserChangePassword";



export default class TopLevelRouter extends React.Component {
    state = {
        loading: true,
        user: null
    }

    componentDidMount() {
        const auth = getAuth();
        onAuthStateChanged(auth, (user) => {
            if (user) {
                // User is signed in, see docs for a list of available properties
                // https://firebase.google.com/docs/reference/js/firebase.User
                this.setState({ user: user, loading: false })
                // ...
            } else {
                // User is signed out
                // ...
                this.setState({ loading: false })
            }
        });
    }

    render() {
        return (
            this.state.loading ? (
                <div id="SpinnerDiv">
                    <Spinner animation="grow" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                    <Spinner animation="grow" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                    <Spinner animation="grow" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                </div>
            ) : (
                <BrowserRouter>
                    <Routes>
                        <Route path="/" element={<App />} />
                        <Route path="/e-logbook" element={this.state.user ? <ELogbook /> : <LoginPanel />} />
                        <Route path="/audit_form/:formId" element={this.state.user ? <AuditForm /> : <LoginPanel />} />
                        <Route path="/learning_contract/:formId" element={this.state.user ? <LearningContract /> : <LoginPanel />} />
                        <Route path="/extra_activities" element={this.state.user ? <ExtraActivities /> : <LoginPanel />} />
                        <Route path='/auditor_passwordchange' element={<AuditorChangePassword />} />
                        <Route path='/online_application' element={<OnlineApplication />} />
                        <Route path='/user_passwordchange' element={<UserChangePassword />} />
                        <Route path='*' exact={true} element={<UnderConstruction />} />
                    </Routes>
                </BrowserRouter >
            )
        )
    }
}