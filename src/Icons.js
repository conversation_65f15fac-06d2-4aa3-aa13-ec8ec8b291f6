/* eslint-disable camelcase */
import FontAwesome_ttf from "react-native-vector-icons/Fonts/FontAwesome.ttf";
import FontAwesome5_ttf from "react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf";
import Feather_ttf from "react-native-vector-icons/Fonts/Feather.ttf";
import AntDesign_ttf from "react-native-vector-icons/Fonts/AntDesign.ttf";
import MaterialCommunityIcons_ttf from "react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf";

const IconsCSS = `
@font-face {
  src: url(${FontAwesome_ttf});
  font-family: FontAwesome;
}
@font-face {
  src: url(${FontAwesome5_ttf});
  font-family: FontAwesome5;
}
@font-face {
  src: url(${Feather_ttf});
  font-family: Feather;
}
@font-face {
  src: url(${AntDesign_ttf});
  font-family: AntDesign;
}
@font-fact{
  src: url(${MaterialCommunityIcons_ttf});
  font-family: MaterialCommunityIcons;
}
`;

const style = document.createElement("style");
style.type = "text/css";
if (style.styleSheet) style.styleSheet.cssText = IconsCSS;
else style.appendChild(document.createTextNode(IconsCSS));

document.head.appendChild(style);
