import { useState, useEffect } from 'react';
import { doc, setDoc, getDoc } from "firebase/firestore";
import { db } from "../index";
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import "./LearningContract.css"
import SignFormModal from '../Components/SignFormModal';
import AntDesign from 'react-native-vector-icons/dist/AntDesign';
import { learningContracts } from "../Pages/ELogbook";

const learningContractNumber = window.location.pathname.split("/")[2];


export default function LearningContract() {
    const [staffInfo, setStaffInfo] = useState(null);
    const [fetchedDraft, setFetchedDraft] = useState(false);
    const [contractData, setContractData] = useState({
        startPeriodOfPlacement: "",
        endPeriodOfPlacement: "",
        learningObjectives: "",
        strategiesAndResources: "",
        targetDateOfCompletion: "",
        outcomeCriteria: "",
        dateArchieved: "",
        remarks: "",
        dateOfSettingContract: "",
        evaluationDate: "",
        auditor: ""
    })

    useEffect(() => {
        const loadSessionStorage = async () => {
            const tmpStaffInfo = sessionStorage.getItem("StaffInfo");
            setStaffInfo(JSON.parse(tmpStaffInfo));
        }
        loadSessionStorage();
    }, [])

    useEffect(() => {
        if (staffInfo && staffInfo.docID) {
            fetchDraft();
        }
    }, [staffInfo])

    function handleChange(key, value) {
        let tmpContractData = contractData;
        tmpContractData[key] = value;
        console.log("Updating", tmpContractData);
        setContractData(tmpContractData);
    }

    async function uploadContract() {
        await setDoc(doc(db, `LearningContracts${learningContractNumber}`, staffInfo.docID), {
            ...contractData
        }).then(() => {
            alert("Draft Saved")
        });
    }

    async function fetchDraft() {
        console.log("Ref", `LearningContracts${learningContractNumber}`)
        const docRef = doc(db, `LearningContracts${learningContractNumber}`, staffInfo.docID);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            console.log("Date", `${new Date(docSnap.data().startPeriodOfPlacement).getFullYear()}/${new Date(docSnap.data().startPeriodOfPlacement).getMonth() < 10 ? '0' + new Date(docSnap.data().startPeriodOfPlacement).getMonth() : new Date(docSnap.data().startPeriodOfPlacement).getMonth()}/${new Date(docSnap.data().startPeriodOfPlacement).getDate() < 10 ? '0' + new Date(docSnap.data().startPeriodOfPlacement).getDate() : new Date(docSnap.data().startPeriodOfPlacement).getDate()}`)
            setContractData({ ...contractData, ...docSnap.data() });
        } else {
            // doc.data() will be undefined in this case
            console.log("No such document!");
        }
        setFetchedDraft(true);
    }

    async function submitResponse(auditor) {
        await setDoc(doc(db, `LearningContracts${learningContractNumber}`, staffInfo.docID), {
            ...contractData, auditor: auditor
        }).then(() => {
            alert("Learning Contract signed!");
            window.location.replace("/e-logbook");
        });
    }

    function openModal() {
        document.getElementById("signatureModal").style.display = "block";
        document.getElementById("signatureModal").classList.add("show");
    }

    return (
        <div id="LearningContractDiv">
            {staffInfo && fetchedDraft ? (
                <Paper elevation={3} id="LearningContractPaper">
                    <SignFormModal onSubmit={submitResponse} />
                    <h1>{learningContracts[learningContractNumber - 1]}</h1>
                    <h5><b>Preceptee Name:</b> {staffInfo.staff_name}</h5>
                    <h5><b>Department/Specialty:</b> {staffInfo.department}</h5>
                    <h5><b>Ward:</b> {staffInfo.ward}</h5>
                    <h5><b>Status:</b> {contractData.auditor.length > 0 ? "Signed by " + contractData.auditor : "Draft"}</h5>
                    <Button variant="outlined" className="ForgetPasswordButton" id="ForgetPasswordButtonInApp" onClick={() => { window.location.assign("/e-logbook") }}> <AntDesign name="caretleft" size={14} color="blue" />Back</Button>
                    <hr />
                    <div className="flexRow">
                        <label for="PeriodOfPlacement">Period Of Placement</label>
                        <input className="DateSelectionInRow" type="date" defaultValue={new Date(contractData.startPeriodOfPlacement).toLocaleDateString("en-CA") || new Date().toLocaleDateString("en-CA")} onChange={(event) => { handleChange("startPeriodOfPlacement", new Date(event.target.value).getTime()) }} />
                        <p>to</p>
                        <input className="DateSelectionInRow" type="date" defaultValue={new Date(contractData.endPeriodOfPlacement).toLocaleDateString("en-CA") || new Date(contractData.startPeriodOfPlacement).toLocaleDateString("en-CA")} onChange={(event) => { handleChange("endPeriodOfPlacement", new Date(event.target.value).getTime()) }} />
                    </div>
                    <div className="flexRowSingleItem">
                        <label>Learning Objectives</label>
                        <textarea className="FreeTextBar" defaultValue={contractData.learningObjectives} onChange={(event) => { handleChange("learningObjectives", event.target.value) }}></textarea>
                    </div>
                    <div className="flexRowSingleItem">
                        <label>Strategies & Resources</label>
                        <textarea className="FreeTextBar" defaultValue={contractData.strategiesAndResources} onChange={(event) => { handleChange("strategiesAndResources", event.target.value) }}></textarea>
                    </div>
                    <div className="flexRowSingleItem">
                        <label>Target Date Of Completion</label>
                        <input className="DateSelection" type="date" defaultValue={new Date(contractData.targetDateOfCompletion).toLocaleDateString("en-CA")} onChange={(event) => { handleChange("targetDateOfCompletion", new Date(event.target.value).getTime()) }} />
                    </div>
                    <hr />
                    <h3>Evaluation</h3>
                    <div className="flexRowSingleItem">
                        <label>Outcome Criteria</label>
                        <textarea className="FreeTextBar" defaultValue={contractData.outcomeCriteria} onChange={(event) => { handleChange("outcomeCriteria", event.target.value) }}></textarea>
                    </div>
                    <div className="flexRowSingleItem">
                        <label>Date Archieved</label>
                        <input className="DateSelection" defaultValue={new Date(contractData.dateArchieved).toLocaleDateString("en-CA")} type="date" onChange={(event) => { handleChange("dateArchieved", new Date(event.target.value).getTime()) }} />
                    </div>
                    <div className="flexRowSingleItem">
                        <label>Remarks</label>
                        <textarea className="FreeTextBar" defaultValue={contractData.remarks} onChange={(event) => { handleChange("remarks", event.target.value) }}></textarea>
                    </div>
                    <div className="flexRowSingleItem">
                        <label>Date Of Setting Learning Contract</label>
                        <input className="DateSelection" type="date" defaultValue={new Date(contractData.dateOfSettingContract).toLocaleDateString("en-CA")} onChange={(event) => { handleChange("dateOfSettingContract", new Date(event.target.value).getTime()) }} />
                    </div>
                    <div className="flexRowSingleItem">
                        <label>Evaluation Date</label>
                        <input className="DateSelection" type="date" defaultValue={new Date(contractData.evaluationDate).toLocaleDateString("en-CA")} onChange={(event) => { handleChange("evaluationDate", new Date(event.target.value).getTime()) }} />
                    </div>
                    <Button className="LearningContractButtons" variant="contained" color="primary" onClick={() => { uploadContract() }}>Save Draft</Button>
                    <Button className="LearningContractButtons" variant="contained" color="success" onClick={() => { openModal() }}>Auditor E-Sign</Button>
                </Paper>
            ) : null}

        </div>
    )
}