#ELogbookBackgroundDiv {
    background: white;
    display: flex;
    height: 100%;
    width: 100%;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-image: url("../Assets/AppBackground.jpg");
    background-repeat: no-repeat;
    background-attachment: local;
    background-size: cover;
}

@media only screen and (hover: none) and (pointer: coarse) {
    #ELogbookBackgroundDiv {
        padding-bottom: 25%;
        margin-bottom: 5%;
    }

    /* Ensure the inner div has enough bottom padding for mobile */
    #ELogbookInnerDiv {
        padding-bottom: 120px; /* Add extra padding to prevent content overlap with bottom nav */
    }
}

#ELogbookInnerDiv {
    background-color: white;
    margin-bottom: 6%;
    padding: 2%;
    display: flex;
    height: 95%;
    width: 95%;
    flex-direction: column;
    align-items: center;
    border-radius: 15px;
    box-shadow: 0 0 40px rgba(8, 7, 16, 0.6);
    overflow-y: scroll;
    overflow-x: hidden;
}


@media (max-width:960px) {
    .table {
        font-size: 11px !important;
    }

    .table h5 {
        font-size: 11px !important;
    }

    #CardAndButtonRow {
        display: flex;
        flex-direction: column !important;
        width: 100%;
        height: 40%;
        margin-bottom: 18%;
    }

    .action-button {
        width: auto !important;
        margin-top: 0;
        text-align: center;
        font-size: 8px !important;
        padding: 6px 8px !important;
        min-height: 32px !important;
    }

}


@media (min-width:960px) {
    #CardAndButtonRow {
        display: flex;
        flex-direction: row;
        width: 100%;
    }
}

#CardAndButtonRow {
    display: flex;
    flex-direction: row;
    width: 100%;
}

#StaffCardDiv {
    min-width: 340px;
    min-height: 240px;
    width: 10vw;
    height: 6vw;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(8, 7, 16, 0.3);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    position: relative;
    overflow: hidden;
}

#StaffCardDiv::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

/* Staff Card Content */
.staff-card-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Staff Name Section */
.staff-name-section {
    margin-bottom: 25px;
}

.staff-name {
    color: white;
    font-weight: 700;
    font-size: 1.6rem;
    margin: 0 0 12px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.staff-rank-badge {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    padding: 6px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Staff Details */
.staff-details {
    margin-bottom: 25px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    text-align: right;
}

/* Card Footer */
.card-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: auto;
}

.card-logo {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@media(min-width:960px) {
    #StaffCardDiv {
        margin-left: 100px;
    }

    #btn_div .action-button {
        width: 25%;
        margin-top: 0;
        text-align: center;
        font-size: 12px;
    }
}

h4,
h5 {
    color: gray;
    font-weight: 300;
    letter-spacing: 0.5px;
}

#btn_div {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 15px;
    height: 100%;
    gap: 12px;
}

.account-settings-title {
    color: #333;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 10px;
    text-align: center;
}

.action-button {
    min-height: 40px;
    border-radius: 8px;
    font-weight: 500;
    text-transform: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.angel-program-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
}

.angel-program-button:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
}

@media (max-width:960px) {
    #btn_div {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 5%;
        gap: 8px;
        flex-wrap: wrap;
    }

    .account-settings-title {
        width: 100%;
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .action-button {
        flex: 1;
        min-width: 0;
        max-width: 120px;
        font-size: 10px !important;
        padding: 8px 4px !important;
        min-height: 36px;
    }
}


@media only screen and (hover: none) and (pointer: coarse) {
    #btn_div Button:active {
        opacity: 0.7;
    }
}

#AuditFormsContentDiv {
    height: 55%;
    width: 100%;
    margin-top: 10%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 10%;
}

/* Add bottom padding for mobile devices to prevent overlap with bottom navigation */
@media only screen and (hover: none) and (pointer: coarse) {
    #AuditFormsContentDiv {
        padding-bottom: 120px; /* Increased padding to account for bottom navigation */
    }
}

/* Assessment Overview */
.assessment-overview {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
    overflow: hidden;
}

.overview-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 15px 20px;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.overview-header h5 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.view-all-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.view-all-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.view-all-btn:active {
    transform: scale(0.95);
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0;
}

.stat-card {
    padding: 20px;
    text-align: center;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.3s ease;
}

.stat-card:last-child {
    border-right: none;
}

.stat-card:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-card.pass .stat-number {
    color: #4caf50;
}

.stat-card.fail .stat-number {
    color: #f44336;
}

.stat-card.pending .stat-number {
    color: #ff9800;
}

/* All Assessments View */
.all-assessments-view {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background: #f8f9fa;
}

.all-assessments-header {
    padding: 15px 20px;
    background: #f1f3f4;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.all-assessments-header h5 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.close-all-assessments-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-all-assessments-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.all-assessments-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px 0;
}

.assessment-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.3s ease;
}

.assessment-status-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.assessment-status-item:last-child {
    border-bottom: none;
}

.assessment-status-info {
    flex: 1;
    min-width: 0;
}

.assessment-status-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    word-wrap: break-word;
}

.assessment-status-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.category-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-badge.mandatory {
    background: rgba(244, 67, 54, 0.1);
    color: #d32f2f;
}

.category-badge.optional {
    background: rgba(25, 118, 210, 0.1);
    color: #1976d2;
}

.last-completed-text {
    font-size: 0.75rem;
    color: #7f8c8d;
    font-weight: 500;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
}

.status-indicator.pass {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.status-indicator.fail {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.status-indicator.pending {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

/* Mobile-Optimized Assessment Cards */
.assessment-cards-container-mobile {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 15px 0;
}

.assessment-card-mobile {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.assessment-card-mobile:active {
    transform: scale(0.98);
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.12);
}

.card-header-mobile {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-title-section {
    flex: 1;
    min-width: 0;
}

.assessment-title-mobile {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 6px 0;
    line-height: 1.3;
    word-wrap: break-word;
}

.assessment-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.last-completed {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
}

.status-badge-mobile {
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    flex-shrink: 0;
}

.status-badge-mobile.status-pass {
    background: #4caf50;
    color: white;
}

.status-badge-mobile.status-fail {
    background: #f44336;
    color: white;
}

.status-badge-mobile.status-pending {
    background: #ff9800;
    color: white;
}

.card-actions-mobile {
    padding: 12px 15px;
    display: flex;
    gap: 8px;
}

.action-btn-mobile {
    flex: 1;
    padding: 10px 12px;
    border: none;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    text-decoration: none;
    min-height: 36px;
}

.action-btn-mobile.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn-mobile.primary:active {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: scale(0.95);
}

.action-btn-mobile.secondary {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.action-btn-mobile.secondary:active {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(0.95);
}

.action-btn-mobile.full-width {
    flex: 1;
}

.no-assessments-message {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
}

.no-assessments-message h5 {
    margin: 15px 0 8px 0;
    font-size: 1.1rem;
    color: #95a5a6;
}

.no-assessments-message p {
    margin: 0;
    font-size: 0.9rem;
    color: #bdc3c7;
}

#AuditFormsContentHeader,
.AuditFormsContentHeader {
    width: 95%;
    border-radius: 5px;
    align-self: center;
    text-align: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right,
            #3a3b3c,
            #e4e6eb);
    padding: 2px;
    margin-bottom: 25px;
}

#LearningContractContentHeader {
    width: 95%;
    border-radius: 5px;
    align-self: center;
    text-align: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right,
            #3a3b3c,
            #e4e6eb);
    padding: 2px;
    margin-bottom: 25px;
}

#LearningContractContentHeader span {
    font-weight: 200;
    margin-left: 10px;
    color: white;
}

#AuditFormsContentHeader span,
.AuditFormsContentHeader span {
    font-weight: 200;
    margin-left: 10px;
    color: white;
}

/* Styling for mandatory vs optional assessment sections */
#AuditFormsContentHeader span:contains("I. Mandatory") {
    background: linear-gradient(to right, #d32f2f, #f44336);
}

#AuditFormsContentHeader span:contains("II. Optional") {
    background: linear-gradient(to right, #1976d2, #2196f3);
}

/* Add spacing between assessment sections */
.assessment-section {
    margin-bottom: 30px;
}

.assessment-section:last-child {
    margin-bottom: 0;
}

.AuditFormsContentColumn {
    width: 100%;
    border-radius: 5px;
    justify-content: space-around;
    display: flex;
    flex-direction: row;
    padding: 2px;
    margin-bottom: 15px;
}

.AuditFormsContentColumn h5 {
    font-size: 16px;
    width: 33%;
    text-align: left;
    margin-left: 15px;
    color: black;
}

@media (max-width:960px) {
    .AuditFormsContentColumn h5 {
        font-size: 12px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 9px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 9px;
    }
}

@media (max-width:960px) {
    .AuditFormsContentColumn h5 {
        font-size: 12px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 9px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 9px;
    }
}

@media (max-width:400px) {
    .AuditFormsContentColumn h5 {
        font-size: 10px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 8px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 8px;
    }

    #StaffCardDiv {
        min-width: 280px;
        min-height: 200px;
        width: 100%;
        height: auto;
        border-radius: 16px;
        box-shadow: 0 6px 24px rgba(8, 7, 16, 0.3);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 16px;
        margin-bottom: 20px;
    }

    .staff-name-section {
        margin-bottom: 20px;
    }

    .staff-name {
        font-size: 1.3rem;
    }

    .staff-rank-badge {
        font-size: 0.8rem;
        padding: 5px 12px;
    }

    .staff-details {
        margin-bottom: 20px;
    }

    .detail-row {
        margin-bottom: 10px;
        padding: 6px 0;
    }

    .detail-label {
        font-size: 0.8rem;
    }

    .detail-value {
        font-size: 0.9rem;
    }

    .card-logo {
        font-size: 1rem;
    }

    #btn_div {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 5%;
        margin-bottom: 25px;
        gap: 6px;
        flex-wrap: wrap;
    }

    /* Mobile Assessment Overview */
    .assessment-overview {
        margin-bottom: 20px;
        border-radius: 12px;
    }

    .overview-header {
        padding: 12px 15px;
    }

    .overview-header h5 {
        font-size: 1rem;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .stat-card {
        padding: 15px 10px;
    }

    .stat-number {
        font-size: 1.6rem;
        margin-bottom: 4px;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    /* Mobile Assessment Cards - Already optimized above */
    .assessment-cards-container-mobile {
        gap: 10px;
        padding: 12px 0;
    }

    .assessment-card-mobile {
        border-radius: 10px;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
    }

    .card-header-mobile {
        padding: 12px;
        gap: 10px;
    }

    .assessment-title-mobile {
        font-size: 0.9rem;
        margin-bottom: 4px;
    }

    .last-completed {
        font-size: 0.75rem;
    }

    .status-badge-mobile {
        padding: 3px 8px;
        font-size: 0.7rem;
    }

    .card-actions-mobile {
        padding: 10px 12px;
        gap: 6px;
    }

    .action-btn-mobile {
        padding: 8px 10px;
        font-size: 0.75rem;
        min-height: 32px;
        gap: 4px;
    }

    .no-assessments-message {
        padding: 30px 15px;
    }

    .no-assessments-message h5 {
        font-size: 1rem;
    }

    .no-assessments-message p {
        font-size: 0.85rem;
    }

    /* Mobile All Assessments View */
    .view-all-btn {
        padding: 6px 10px;
        font-size: 0.75rem;
        gap: 4px;
    }

    .all-assessments-header {
        padding: 12px 15px;
    }

    .all-assessments-header h5 {
        font-size: 0.9rem;
        gap: 8px;
    }

    .all-assessments-list {
        max-height: 300px;
        padding: 8px 0;
    }

    .assessment-status-item {
        padding: 10px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .assessment-status-info {
        width: 100%;
    }

    .assessment-status-title {
        font-size: 0.85rem;
        margin-bottom: 6px;
    }

    .assessment-status-meta {
        gap: 8px;
    }

    .category-badge {
        font-size: 0.65rem;
        padding: 2px 6px;
    }

    .last-completed-text {
        font-size: 0.7rem;
    }

    .status-indicator {
        align-self: flex-end;
        font-size: 0.75rem;
        padding: 4px 8px;
        gap: 4px;
    }
}

.AuditFormsContentColumn button {
    border: none;
    margin: 0;
    padding: 0;
    padding-top: 5px;
    height: 24px;
    width: 10%;
}

#IncompleteForms h5 {
    color: red;
}

#IncompleteForms a {
    color: red;
}

.modal-dialog {
    width: 95% !important;
    max-width: 95% !important;
}

.modal-header {
    justify-content: flex-end;
}

.modal-header .MuiChip-root {
    margin-right: 3vw;
}

#TkohLogo {
    width: 25%;
    height: auto;
    bottom: 120px;
    left: 75%;
    position: relative;
}

@media (min-width:960px) {
    #TkohLogo {
        width: 30%;
        height: auto;
        bottom: 80%;
        left: 75%;
        position: relative;
    }

    #ELogbookInnerDiv .table {
        width: 90%;
    }
}

#ViewHistoryButton {
    border: none;
    padding: 0;
    text-decoration: underline;
    color: blueviolet;
}