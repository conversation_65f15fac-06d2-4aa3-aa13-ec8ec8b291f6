#ELogbookBackgroundDiv {
    background: white;
    display: flex;
    height: 100%;
    width: 100%;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-image: url("../Assets/AppBackground.jpg");
    background-repeat: no-repeat;
    background-attachment: local;
    background-size: cover;
}

@media only screen and (hover: none) and (pointer: coarse) {
    #ELogbookBackgroundDiv {
        padding-bottom: 25%;
        margin-bottom: 5%;
    }

    /* Ensure the inner div has enough bottom padding for mobile */
    #ELogbookInnerDiv {
        padding-bottom: 120px; /* Add extra padding to prevent content overlap with bottom nav */
    }
}

#ELogbookInnerDiv {
    background-color: white;
    margin-bottom: 6%;
    padding: 2%;
    display: flex;
    height: 95%;
    width: 95%;
    flex-direction: column;
    align-items: center;
    border-radius: 15px;
    box-shadow: 0 0 40px rgba(8, 7, 16, 0.6);
    overflow-y: scroll;
    overflow-x: hidden;
}


@media (max-width:960px) {
    .table {
        font-size: 11px !important;
    }

    .table h5 {
        font-size: 11px !important;
    }

    #CardAndButtonRow {
        display: flex;
        flex-direction: column !important;
        width: 100%;
        height: 40%;
        margin-bottom: 18%;
    }

    #btn_div Button {
        width: auto;
        margin-top: 0;
        text-align: center;
        font-size: 8px;
    }

}


@media (min-width:960px) {
    #CardAndButtonRow {
        display: flex;
        flex-direction: row;
        width: 100%;
    }
}

#CardAndButtonRow {
    display: flex;
    flex-direction: row;
    width: 100%;
}

#StaffCardDiv {
    min-width: 340px;
    min-height: 220px;
    width: 10vw;
    height: 6vw;
    border-radius: 15px;
    box-shadow: 0 0 40px rgba(8, 7, 16, 0.6);
    background: linear-gradient(#1845ad,
            #23a2f6);
    padding: 15px;
}

@media(min-width:960px) {
    #StaffCardDiv {
        margin-left: 100px;
    }

    #btn_div Button {
        width: 25%;
        margin-top: 0;
        text-align: center;
        font-size: 12px;
    }
}

#StaffCardDiv h3 {
    color: white;
    font-weight: 200;
}

h4,
h5 {
    color: gray;
    font-weight: 300;
    letter-spacing: 0.5px;
}


#StaffCardDiv h5 {
    color: white;
    font-weight: 300;
    letter-spacing: 0.5px;
}

#Card_Logo {
    text-align: end;
    color: white;
    font-weight: 200;
}

#btn_div {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 15px;
    height: 100%;
}

@media (max-width:960px) {
    #btn_div {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 5%;
    }
}


@media only screen and (hover: none) and (pointer: coarse) {
    #btn_div Button:active {
        opacity: 0.7;
    }
}

#AuditFormsContentDiv {
    height: 55%;
    width: 100%;
    margin-top: 10%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 10%;
}

/* Add bottom padding for mobile devices to prevent overlap with bottom navigation */
@media only screen and (hover: none) and (pointer: coarse) {
    #AuditFormsContentDiv {
        padding-bottom: 120px; /* Increased padding to account for bottom navigation */
    }
}

#AuditFormsContentHeader,
.AuditFormsContentHeader {
    width: 95%;
    border-radius: 5px;
    align-self: center;
    text-align: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right,
            #3a3b3c,
            #e4e6eb);
    padding: 2px;
    margin-bottom: 25px;
}

#LearningContractContentHeader {
    width: 95%;
    border-radius: 5px;
    align-self: center;
    text-align: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right,
            #3a3b3c,
            #e4e6eb);
    padding: 2px;
    margin-bottom: 25px;
}

#LearningContractContentHeader span {
    font-weight: 200;
    margin-left: 10px;
    color: white;
}

#AuditFormsContentHeader span,
.AuditFormsContentHeader span {
    font-weight: 200;
    margin-left: 10px;
    color: white;
}

/* Styling for mandatory vs optional assessment sections */
#AuditFormsContentHeader span:contains("I. Mandatory") {
    background: linear-gradient(to right, #d32f2f, #f44336);
}

#AuditFormsContentHeader span:contains("II. Optional") {
    background: linear-gradient(to right, #1976d2, #2196f3);
}

/* Add spacing between assessment sections */
.assessment-section {
    margin-bottom: 30px;
}

.assessment-section:last-child {
    margin-bottom: 0;
}

.AuditFormsContentColumn {
    width: 100%;
    border-radius: 5px;
    justify-content: space-around;
    display: flex;
    flex-direction: row;
    padding: 2px;
    margin-bottom: 15px;
}

.AuditFormsContentColumn h5 {
    font-size: 16px;
    width: 33%;
    text-align: left;
    margin-left: 15px;
    color: black;
}

@media (max-width:960px) {
    .AuditFormsContentColumn h5 {
        font-size: 12px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 9px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 9px;
    }
}

@media (max-width:960px) {
    .AuditFormsContentColumn h5 {
        font-size: 12px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 9px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 9px;
    }
}

@media (max-width:400px) {
    .AuditFormsContentColumn h5 {
        font-size: 10px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 8px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 8px;
    }

    #StaffCardDiv {
        min-width: 280px;
        min-height: 220px;
        width: 100%;
        height: 60%;
        border-radius: 15px;
        box-shadow: 0 0 40px rgba(8, 7, 16, 0.6);
        background: linear-gradient(#1845ad,
                #23a2f6);
        padding: 15px;
    }

    #btn_div {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 5%;
        margin-bottom: 25px;
    }
}

.AuditFormsContentColumn button {
    border: none;
    margin: 0;
    padding: 0;
    padding-top: 5px;
    height: 24px;
    width: 10%;
}

#IncompleteForms h5 {
    color: red;
}

#IncompleteForms a {
    color: red;
}

.modal-dialog {
    width: 95% !important;
    max-width: 95% !important;
}

.modal-header {
    justify-content: flex-end;
}

.modal-header .MuiChip-root {
    margin-right: 3vw;
}

#TkohLogo {
    width: 25%;
    height: auto;
    bottom: 120px;
    left: 75%;
    position: relative;
}

@media (min-width:960px) {
    #TkohLogo {
        width: 30%;
        height: auto;
        bottom: 80%;
        left: 75%;
        position: relative;
    }

    #ELogbookInnerDiv .table {
        width: 90%;
    }
}

#ViewHistoryButton {
    border: none;
    padding: 0;
    text-decoration: underline;
    color: blueviolet;
}