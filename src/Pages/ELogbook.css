#ELogbookBackgroundDiv {
    background: white;
    display: flex;
    height: 100%;
    width: 100%;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-image: url("../Assets/AppBackground.jpg");
    background-repeat: no-repeat;
    background-attachment: local;
    background-size: cover;
}

@media only screen and (hover: none) and (pointer: coarse) {
    #ELogbookBackgroundDiv {
        padding-bottom: 25%;
        margin-bottom: 5%;
    }

    /* Ensure the inner div has enough bottom padding for mobile */
    #ELogbookInnerDiv {
        padding-bottom: 120px; /* Add extra padding to prevent content overlap with bottom nav */
    }
}

#ELogbookInnerDiv {
    background-color: white;
    margin-bottom: 6%;
    padding: 2%;
    display: flex;
    height: 95%;
    width: 95%;
    flex-direction: column;
    align-items: center;
    border-radius: 15px;
    box-shadow: 0 0 40px rgba(8, 7, 16, 0.6);
    overflow-y: scroll;
    overflow-x: hidden;
}


@media (max-width:960px) {
    .table {
        font-size: 11px !important;
    }

    .table h5 {
        font-size: 11px !important;
    }

    #CardAndButtonRow {
        display: flex;
        flex-direction: column !important;
        width: 100%;
        height: 40%;
        margin-bottom: 18%;
    }

    .action-button {
        width: auto !important;
        margin-top: 0;
        text-align: center;
        font-size: 8px !important;
        padding: 6px 8px !important;
        min-height: 32px !important;
    }

}


@media (min-width:960px) {
    #CardAndButtonRow {
        display: flex;
        flex-direction: row;
        width: 100%;
    }
}

#CardAndButtonRow {
    display: flex;
    flex-direction: row;
    width: 100%;
}

#StaffCardDiv {
    min-width: 340px;
    min-height: 240px;
    width: 10vw;
    height: 6vw;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(8, 7, 16, 0.3);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    position: relative;
    overflow: hidden;
}

#StaffCardDiv::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

/* Staff Card Header with Avatar */
.staff-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.staff-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.avatar-text {
    color: white;
    font-size: 20px;
    font-weight: 600;
}

.staff-info {
    flex: 1;
}

.staff-name {
    color: white;
    font-weight: 600;
    font-size: 1.4rem;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.staff-rank-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-block;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Staff Details */
.staff-details {
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: white;
    font-size: 0.95rem;
    font-weight: 400;
}

.detail-item span {
    margin-left: 10px;
    letter-spacing: 0.5px;
}

/* Card Footer */
.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.card-logo {
    color: white;
    font-weight: 300;
    font-size: 1rem;
    opacity: 0.9;
}

@media(min-width:960px) {
    #StaffCardDiv {
        margin-left: 100px;
    }

    #btn_div .action-button {
        width: 25%;
        margin-top: 0;
        text-align: center;
        font-size: 12px;
    }
}

h4,
h5 {
    color: gray;
    font-weight: 300;
    letter-spacing: 0.5px;
}

#btn_div {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 15px;
    height: 100%;
    gap: 12px;
}

.account-settings-title {
    color: #333;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 10px;
    text-align: center;
}

.action-button {
    min-height: 40px;
    border-radius: 8px;
    font-weight: 500;
    text-transform: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.angel-program-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
}

.angel-program-button:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
}

@media (max-width:960px) {
    #btn_div {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 5%;
        gap: 8px;
        flex-wrap: wrap;
    }

    .account-settings-title {
        width: 100%;
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .action-button {
        flex: 1;
        min-width: 0;
        max-width: 120px;
        font-size: 10px !important;
        padding: 8px 4px !important;
        min-height: 36px;
    }
}


@media only screen and (hover: none) and (pointer: coarse) {
    #btn_div Button:active {
        opacity: 0.7;
    }
}

#AuditFormsContentDiv {
    height: 55%;
    width: 100%;
    margin-top: 10%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 10%;
}

/* Add bottom padding for mobile devices to prevent overlap with bottom navigation */
@media only screen and (hover: none) and (pointer: coarse) {
    #AuditFormsContentDiv {
        padding-bottom: 120px; /* Increased padding to account for bottom navigation */
    }
}

#AuditFormsContentHeader,
.AuditFormsContentHeader {
    width: 95%;
    border-radius: 5px;
    align-self: center;
    text-align: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right,
            #3a3b3c,
            #e4e6eb);
    padding: 2px;
    margin-bottom: 25px;
}

#LearningContractContentHeader {
    width: 95%;
    border-radius: 5px;
    align-self: center;
    text-align: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right,
            #3a3b3c,
            #e4e6eb);
    padding: 2px;
    margin-bottom: 25px;
}

#LearningContractContentHeader span {
    font-weight: 200;
    margin-left: 10px;
    color: white;
}

#AuditFormsContentHeader span,
.AuditFormsContentHeader span {
    font-weight: 200;
    margin-left: 10px;
    color: white;
}

/* Styling for mandatory vs optional assessment sections */
#AuditFormsContentHeader span:contains("I. Mandatory") {
    background: linear-gradient(to right, #d32f2f, #f44336);
}

#AuditFormsContentHeader span:contains("II. Optional") {
    background: linear-gradient(to right, #1976d2, #2196f3);
}

/* Add spacing between assessment sections */
.assessment-section {
    margin-bottom: 30px;
}

.assessment-section:last-child {
    margin-bottom: 0;
}

.AuditFormsContentColumn {
    width: 100%;
    border-radius: 5px;
    justify-content: space-around;
    display: flex;
    flex-direction: row;
    padding: 2px;
    margin-bottom: 15px;
}

.AuditFormsContentColumn h5 {
    font-size: 16px;
    width: 33%;
    text-align: left;
    margin-left: 15px;
    color: black;
}

@media (max-width:960px) {
    .AuditFormsContentColumn h5 {
        font-size: 12px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 9px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 9px;
    }
}

@media (max-width:960px) {
    .AuditFormsContentColumn h5 {
        font-size: 12px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 9px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 9px;
    }
}

@media (max-width:400px) {
    .AuditFormsContentColumn h5 {
        font-size: 10px;
    }

    #FormTable th,
    .FormTable th {
        font-size: 8px;
    }

    #FormTable td,
    .FormTable td {
        font-size: 8px;
    }

    #StaffCardDiv {
        min-width: 280px;
        min-height: 200px;
        width: 100%;
        height: auto;
        border-radius: 16px;
        box-shadow: 0 6px 24px rgba(8, 7, 16, 0.3);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 16px;
        margin-bottom: 20px;
    }

    .staff-card-header {
        margin-bottom: 15px;
    }

    .staff-avatar {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }

    .avatar-text {
        font-size: 16px;
    }

    .staff-name {
        font-size: 1.2rem;
    }

    .staff-rank-badge {
        font-size: 0.75rem;
        padding: 3px 10px;
    }

    .detail-item {
        font-size: 0.85rem;
        margin-bottom: 6px;
    }

    #btn_div {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 5%;
        margin-bottom: 25px;
        gap: 6px;
        flex-wrap: wrap;
    }
}

.AuditFormsContentColumn button {
    border: none;
    margin: 0;
    padding: 0;
    padding-top: 5px;
    height: 24px;
    width: 10%;
}

#IncompleteForms h5 {
    color: red;
}

#IncompleteForms a {
    color: red;
}

.modal-dialog {
    width: 95% !important;
    max-width: 95% !important;
}

.modal-header {
    justify-content: flex-end;
}

.modal-header .MuiChip-root {
    margin-right: 3vw;
}

#TkohLogo {
    width: 25%;
    height: auto;
    bottom: 120px;
    left: 75%;
    position: relative;
}

@media (min-width:960px) {
    #TkohLogo {
        width: 30%;
        height: auto;
        bottom: 80%;
        left: 75%;
        position: relative;
    }

    #ELogbookInnerDiv .table {
        width: 90%;
    }
}

#ViewHistoryButton {
    border: none;
    padding: 0;
    text-decoration: underline;
    color: blueviolet;
}