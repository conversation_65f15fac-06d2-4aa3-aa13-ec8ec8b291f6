import React, { useState, useEffect } from 'react';
import { getAuth, updatePassword, reauthenticateWithCredential, EmailAuthProvider, } from "firebase/auth";
import "./UserChangePassword.css"


import Button from '@mui/material/Button';


import "./UserChangePassword.css";


export default function UserChangePassword() {
    const [oldPassword, setOldPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");

    useEffect(() => {
        openModal();
    }, [])

    function openModal() {
        document.getElementById("ChangePasswordModal").style.display = "block";
        document.getElementById("ChangePasswordModal").classList.add("show");
    }

    function closeModal() {
        document.getElementById("ChangePasswordModal").style.display = "none";
        document.getElementById("ChangePasswordModal").classList.remove("show");
    }

    async function reauthenticate() {
        const auth = getAuth();
        const user = auth.currentUser;
        const credential = EmailAuthProvider.credential(
            auth.currentUser.email,
            oldPassword
        );
        reauthenticateWithCredential(user, credential).then(() => {
            closeModal();
        }).catch((error) => {
            alert(error)
        });
    }

    async function resetPassword(event) {
        event.preventDefault();
        if (newPassword == confirmPassword) {
            const auth = getAuth();
            const user = auth.currentUser;
            updatePassword(user, confirmPassword).then(() => {
                window.location.replace("e-logbook")
            }).catch((error) => {
                console.log("Error", error)
            });
        } else {
            alert("Password & Confirm Password not match")
        }

    }

    return (
        <div id="LoginPanelWrapper">
            <div
                class="modal fade"
                id="ChangePasswordModal"
                tabindex="-1"
                role="dialog"
                aria-hidden="true"
            >
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title" id="exampleModalLabel">
                                Please Enter Old Password
                            </h3>
                        </div>
                        <div class="modal-body">
                            <input title="New Password" placeholder="Old Password" value={oldPassword} onChange={(event) => { setOldPassword(event.target.value) }} autocomplete='off' autoCapitalize='off' type="password" />
                        </div>
                        <div class="modal-footer">
                            <Button
                                variant="outlined"

                                onClick={() => {
                                    reauthenticate();
                                }}
                            >
                                Confirm
                            </Button>
                            <Button
                                variant="outlined"
                                onClick={() => {
                                    closeModal();
                                }}
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
            <form onSubmit={(event) => { resetPassword(event) }} id="PasswordChangeForm">
                <input title="New Password" placeholder="New Password" value={newPassword} onChange={(event) => { setNewPassword(event.target.value) }} autocomplete='off' autoCapitalize='off' type="password" />
                <input title="Confirm Password" placeholder="Confirm Password" value={confirmPassword} onChange={(event) => { setConfirmPassword(event.target.value) }} autocomplete='off' autoCapitalize='off' type="password" />
                <Button variant="outlined" type="submit">Reset Password</Button>
            </form>
        </div>
    )
}