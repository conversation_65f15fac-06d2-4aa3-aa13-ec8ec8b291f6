import { useState, useEffect } from "react";
import { collection, query, getDocs } from "firebase/firestore";
import { getStorage, ref, getDownloadURL, listAll, uploadBytes, deleteObject } from "firebase/storage";
import { db } from "../index";
import "./OnlineApplication.css";
import Paper from '@mui/material/Paper';
import LoyaltyIcon from '@mui/icons-material/Loyalty';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';

export default function OnlineApplication() {
    const [fetched, setFetched] = useState(false);
    const [eventPhotoUrls, setEventPhotoUrls] = useState([]);
    const [onlineApplication, setOnlineApplication] = useState([]);

    useEffect(() => {
        fetchOnlineApplcation();
        fetchEventPhotos();
    }, []);


    async function fetchOnlineApplcation() {
        const q = query(collection(db, "OnlineApplication"));
        const tmpOnlineApplication = [];
        const querySnapshot = await getDocs(q);
        querySnapshot.forEach((doc) => {
            tmpOnlineApplication.push(doc.data())
        })
        setOnlineApplication([...tmpOnlineApplication]);
    }

    async function fetchEventPhotos() {
        const storage = getStorage();
        const listRef = ref(storage, 'images/online_application');
        var tmp_eventPhotosURLs = [];
        var itemsProcessed = 0;

        await listAll(listRef)
            .then((res) => {
                res.items.forEach(async (itemRef, index, array) => {
                    // All the items under listRef.
                    await getDownloadURL(itemRef)
                        .then((url) => {
                            itemsProcessed++;
                            // `url` is the download URL for 'images/stars.jpg'
                            tmp_eventPhotosURLs.push(url);
                            if (itemsProcessed === array.length) {
                                callback();
                            }
                        })
                        .catch((error) => {
                            // Handle any errors
                        })
                });
            })
        function callback() {
            setEventPhotoUrls([...tmp_eventPhotosURLs]);
        }
        setFetched(true);
    }

    function mapURLsFromTitle(title) {
        if (eventPhotoUrls.length > 0) {
            const fileNameArray = eventPhotoUrls.map((url) => decodeImageURL(url));
            const index = fileNameArray.findIndex(image => image.startsWith(title.replaceAll(" ", "%20")));
            return eventPhotoUrls[index];
        }
    }

    function decodeImageURL(url) {
        const splitArray = url.split("%2F");
        const fileName = splitArray[splitArray.length - 1].substring(0, splitArray[splitArray.length - 1].indexOf("?alt"));
        return fileName;
    }


    return (
        <>
            {fetched && (
                <div id="OnlineApplication">
                    <Paper elevation={3}>
                        <h3><b><LoyaltyIcon color="action" />  Online Applicaiton 網上報名</b></h3>
                        <Button variant="outlined" className="ForgetPasswordButton" id="ForgetPasswordButtonInApp" onClick={() => { window.location.assign("/") }}>返回</Button>
                        {onlineApplication.filter(event => (event.postStartDate <= new Date().getTime() && event.postEndDate > new Date().getTime()))?.map(event => {
                            return (
                                <Accordion>
                                    <AccordionSummary
                                        expandIcon={<ExpandMoreIcon />}
                                        aria-controls="panel1a-content"
                                        id="panel1a-header"
                                    >
                                        <h5 className="mr-3">{event.title}-</h5>
                                        <Chip label={new Date(event.eventDate).toLocaleString()} color="primary" />
                                        {event.materialFile && <img className="ImageWithinAccordion" src={mapURLsFromTitle(event.title)} alt="Event Poster" />}
                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <div className="AccordionDetailsDiv">
                                            <p><span>活動簡介:</span> {event.materialContent}</p>
                                            <Button className="ButtonWithinAccordion" variant="contained" color="success" onClick={() => { window.open(event.materialURL, '_blank') }}>
                                                報名活動
                                            </Button>

                                        </div>
                                    </AccordionDetails>
                                </Accordion>
                            )
                        })}
                    </Paper>
                </div>
            )}
        </>
    )
}