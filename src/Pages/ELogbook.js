import React, { useState, useEffect, useRef } from "react";
import { db } from "../index";
import {
  collection,
  query,
  getDocs,
  where,
  orderBy,
  collectionGroup,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
} from "firebase/firestore";
import { getAuth } from "firebase/auth";
import Spinner from "react-bootstrap/Spinner";
import Button from "@mui/material/Button";
import Feather from "react-native-vector-icons/dist/Feather";
import Table from "react-bootstrap/Table";
import SwipeableDrawer from "../Components/SwipeableDrawer";
import NavigationIcon from "@mui/icons-material/Navigation";
import Fab from "@mui/material/Fab";
import Chip from "@mui/material/Chip";
import "./ELogbook.css";
import AlertDialog from "../Components/AlertDialog";

const tkohLogo = require("../Assets/tkoh2.png");
export const learningContracts = [
  "Learning Contract 1 (First Deployment)",
  "Learning Contract 2 (First Deployment)",
  "Learning Contract 1 (Second Deployment)",
  "Learning Contract 2 (Second Deployment)",
];

// Define mandatory and optional skill assessments - exact topic names from Firebase database
export const mandatorySkillAssessments = [
  "Medication Administration – Oral",
  "Medication Administration – Intravenous Injection",
  "Medication Administration – Intravenous Infusion",
  "Whole Blood/ Blood Components (Platelets, Red Cells, Plasma, Cryoprecipitate)Transfusion",
  "Nursing Documentation ",
  "Naso-Gastric Tube Feeding",
  "Wound Dressing",
  "Pressure Injury Prevention ",
  "Fall Prevention ",
  "Cardiopulmonary Resuscitation"
];

export const optionalSkillAssessments = [
  "Physical Restraint of Patient",
  "Naso-Gastric Tube Insertion",
  "Indwelling Urethral Catheter Care ",
  "Tracheostomy Care ",
  "Patient Identification ",
  // Note: Transport assessments from specification may not exist in database yet:
  // "Transport of Patient Undergoing Surgery",
  // "Intra-hospital Transport of Critically Ill Adult Patients"
];

// Helper function to check if a topic matches any category using flexible matching
function isTopicInCategory(topic, categoryArray) {
  // First try exact match
  if (categoryArray.includes(topic)) {
    return true;
  }

  // Then try partial matching (case insensitive)
  const topicLower = topic.toLowerCase();
  return categoryArray.some(categoryTopic => {
    const categoryLower = categoryTopic.toLowerCase();
    // Check if topic contains the category or vice versa
    return topicLower.includes(categoryLower) || categoryLower.includes(topicLower);
  });
}

export default function ELogbook() {
  const [isFetching, setIsFetching] = useState(true);
  const [staffInfo, setStaffInfo] = useState(null);
  const [auditForms, setAuditForms] = useState([]);
  const [fetchedCompletedForms, setFetchedCompletedForms] = useState([]);
  const [completedForms, setCompletedForms] = useState([]);
  const [incompleteForms, setIncompleteForms] = useState([]);
  const [mandatoryCompletedForms, setMandatoryCompletedForms] = useState([]);
  const [mandatoryIncompleteForms, setMandatoryIncompleteForms] = useState([]);
  const [optionalCompletedForms, setOptionalCompletedForms] = useState([]);
  const [optionalIncompleteForms, setOptionalIncompleteForms] = useState([]);
  const [uncategorizedCompletedForms, setUncategorizedCompletedForms] = useState([]);
  const [uncategorizedIncompleteForms, setUncategorizedIncompleteForms] = useState([]);
  const [viewForm, setViewForm] = useState([]);
  const [staffResponseOnSelectedForm, setStaffResponseOnSelectedForm] =
    useState([]);
  const [currentFormTopic, setCurrentFormTopic] = useState("");
  const [currentFormID, setCurrentFormID] = useState("");
  const [currentAttempt, setCurrentAttempt] = useState();
  const alreadyFetchStaffCompletedForms = useRef(false);
  const [open, setOpen] = React.useState(false);
  const [openDialog, setOpenDialog] = React.useState(false);

  const toggleDrawer = (newOpen) => () => {
    setOpen(newOpen);
  };

  useEffect(() => {
    fetchStaffInfo();
  }, []);

  useEffect(() => {
    if (staffInfo !== null) {
      fetchAuditForms();
      fetchCompletedFormsByStaff();
    }
  }, [staffInfo]);

  useEffect(() => {
    if (alreadyFetchStaffCompletedForms.current == true) {
      filterCompleteInComplete();
      storeFetchedInfo();
    }
  }, [fetchedCompletedForms]);

  async function fetchStaffInfo() {
    const auth = getAuth();
    const username = auth.currentUser.displayName;
    const q = query(
      collection(db, "StaffInfo"),
      where("staffID", "==", username)
    );
    const querySnapshot = await getDocs(q);
    if (querySnapshot.size > 0) {
      querySnapshot.forEach((doc) => {
        setStaffInfo(doc.data());
      });
    } else {
      console.log("Staff info not found");
    }
  }

  async function fetchAuditForms() {
    const q = query(
      collection(db, "AuditForms"),
      where("access_group", "==", "all"),
      orderBy("topic", "asc")
    );
    const querySnapshot = await getDocs(q);
    let tmpAuditForms = [];
    try {
      if (querySnapshot.size > 0) {
        querySnapshot.forEach((doc) => {
          tmpAuditForms.push(doc.data());
        });
      } else {
        console.log("Audit Forms not found");
      }
    } catch (error) {
    } finally {
      setAuditForms([...tmpAuditForms]);
    }
  }

  async function fetchCompletedFormsByStaff() {
    var tmpCompletedForms = [];
    const completedFormsByStaff = query(
      collectionGroup(db, "Completed_Forms"),
      where("staff_docID", "==", staffInfo.docID)
    );
    const querySnapshot = await getDocs(completedFormsByStaff);
    querySnapshot.forEach((doc) => {
      const dataObject = doc.data();
      const formID = { formID: doc.ref._key.path.segments[6] };
      const mergeObject = Object.assign(dataObject, formID);
      tmpCompletedForms.push(mergeObject);
    });
    setFetchedCompletedForms([...tmpCompletedForms]);
    alreadyFetchStaffCompletedForms.current = true;
  }

  function filterCompleteInComplete() {
    const tmp_auditForms = auditForms;
    const fetchedFormsID = fetchedCompletedForms.map((form) => {
      return form.formID;
    });
    const completedFormsList = tmp_auditForms.filter((form) =>
      fetchedFormsID.includes(form.form_id)
    );
    const incompleteFormsList = tmp_auditForms.filter(
      (form) => !fetchedFormsID.includes(form.form_id)
    );

    // Debug: Log all available topics to see exact names
    console.log("=== ALL AVAILABLE TOPICS ===");
    tmp_auditForms.forEach((form, index) => {
      console.log(`${index + 1}. "${form.topic}"`);
    });
    console.log("=== END TOPICS ===");

    // Categorize forms into mandatory and optional using flexible matching
    const mandatoryCompleted = completedFormsList.filter((form) =>
      isTopicInCategory(form.topic, mandatorySkillAssessments)
    );
    const mandatoryIncomplete = incompleteFormsList.filter((form) =>
      isTopicInCategory(form.topic, mandatorySkillAssessments)
    );
    const optionalCompleted = completedFormsList.filter((form) =>
      isTopicInCategory(form.topic, optionalSkillAssessments)
    );
    const optionalIncomplete = incompleteFormsList.filter((form) =>
      isTopicInCategory(form.topic, optionalSkillAssessments)
    );

    // Find uncategorized forms for debugging
    const allCategorized = [...mandatoryCompleted, ...mandatoryIncomplete, ...optionalCompleted, ...optionalIncomplete];
    const uncategorizedCompleted = completedFormsList.filter(form =>
      !allCategorized.some(catForm => catForm.form_id === form.form_id)
    );
    const uncategorizedIncomplete = incompleteFormsList.filter(form =>
      !allCategorized.some(catForm => catForm.form_id === form.form_id)
    );

    console.log("Mandatory Completed:", mandatoryCompleted.length);
    console.log("Mandatory Incomplete:", mandatoryIncomplete.length);
    console.log("Optional Completed:", optionalCompleted.length);
    console.log("Optional Incomplete:", optionalIncomplete.length);
    console.log("Uncategorized Completed:", uncategorizedCompleted.length);
    console.log("Uncategorized Incomplete:", uncategorizedIncomplete.length);

    if (uncategorizedCompleted.length > 0 || uncategorizedIncomplete.length > 0) {
      console.log("=== UNCATEGORIZED FORMS ===");
      [...uncategorizedCompleted, ...uncategorizedIncomplete].forEach(form => {
        console.log(`Uncategorized: "${form.topic}"`);
      });
      console.log("=== END UNCATEGORIZED ===");
    }

    setCompletedForms([...completedFormsList]);
    setIncompleteForms([...incompleteFormsList]);
    setMandatoryCompletedForms([...mandatoryCompleted]);
    setMandatoryIncompleteForms([...mandatoryIncomplete]);
    setOptionalCompletedForms([...optionalCompleted]);
    setOptionalIncompleteForms([...optionalIncomplete]);
    setUncategorizedCompletedForms([...uncategorizedCompleted]);
    setUncategorizedIncompleteForms([...uncategorizedIncomplete]);
    setIsFetching(false);
  }

  async function storeFetchedInfo() {
    sessionStorage.setItem("StaffInfo", JSON.stringify(staffInfo));
  }

  async function storeFetchedInfoAndRedirect(event, form) {
    event.preventDefault();
    sessionStorage.setItem("StaffInfo", JSON.stringify(staffInfo));
    sessionStorage.setItem("FormInfo", JSON.stringify(form));
    window.location = `/audit_form/${form.form_id}`;
  }

  async function fetchSelectedForm(docID) {
    const docRef = doc(db, `AuditForms/${docID}/Hide_Collection`, "Questions");
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      const tmpQuestionBank = docSnap.data().questions;
      const tmpStaffResponse = JSON.parse(
        fetchedCompletedForms.filter((form) => form.formID === docID)[0]
          .response
      );
      setViewForm([...tmpQuestionBank]);
      setStaffResponseOnSelectedForm([...tmpStaffResponse]);
    } else {
      // doc.data() will be undefined in this case
      console.log("No such document!");
    }
  }

  function openModal() {
    document.getElementById("ViewCompletedFormModal").style.display = "block";
    document.getElementById("ViewCompletedFormModal").classList.add("show");
  }

  function closeModal() {
    document.getElementById("ViewCompletedFormModal").style.display = "none";
    document.getElementById("ViewCompletedFormModal").classList.remove("show");
  }

  async function deleteSpecificAttempt() {
    const currentForm = fetchedCompletedForms.filter(
      (form) => form.formID == currentFormID
    )[0];
    const docRef = doc(
      db,
      `AuditForms/${currentFormID}/Completed_Forms`,
      currentForm.staff_docID
    );
    if (
      !currentAttempt ||
      currentAttempt == currentForm.past_response.length - 1
    ) {
      //Case deleting latest form
      if (currentForm.past_response.length > 1) {
        currentForm.past_response?.pop();
        currentForm.response = JSON.stringify(
          currentForm.past_response[currentForm.past_response.length - 1]
        );
        await updateDoc(docRef, currentForm);
      } else {
        await deleteDoc(docRef);
      }
    } else {
      currentForm.past_response.splice(currentAttempt, 1);
      await updateDoc(docRef, currentForm);
    }
    window.location.reload();
  }

  async function viewFormWithModal(event, form) {
    event.preventDefault();
    setCurrentFormID(form.form_id);
    setCurrentFormTopic(form.topic);
    await fetchSelectedForm(form.form_id);
    openModal();
  }

  function signout() {
    const auth = getAuth();
    auth.signOut();
  }

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  function failedOnCriticalItem(questionArr, responseArr) {
    if (questionArr?.length > 0 && responseArr?.length > 0) {
      // Find indexes of "NO" responses
      const noResponseIndexArr = responseArr
        .map((response, index) => (response.item_assessed === "NO" ? index : null))
        .filter((index) => index !== null);

      // Find indexes of critical questions (questions that start with "*")
      const criticalQuestionIndexArr = questionArr
        .map((question, index) => (question.startsWith("*") ? index : null))
        .filter((index) => index !== null);

      // Check if any critical question was answered "NO"
      const failedCriticalItem = noResponseIndexArr.some((r) =>
        criticalQuestionIndexArr.includes(r)
      );

      // Calculate compliance percentage (excluding "NA")
      const totalQuestions = responseArr.length;
      const naResponses = responseArr.filter((response) => response.item_assessed === "NA").length;
      const yesResponses = responseArr.filter((response) => response.item_assessed === "YES").length;

      const validResponses = totalQuestions - naResponses; // Total answerable questions
      const complianceRate = validResponses > 0 ? (yesResponses / validResponses) * 100 : 0;

      console.log("Critical Failure:", failedCriticalItem);
      console.log("Compliance Rate:", complianceRate);

      // Fails if any critical question was answered "NO" or if compliance rate is below 80%
      return failedCriticalItem || complianceRate < 80;
    }
  }
  function safeArray(input) {
    return Array.isArray(input) ? input : [];
  }

  // Helper function to render assessment cards
  function renderAssessmentCards(completedForms, incompleteForms) {
    const allCards = [];

    // Add completed forms
    completedForms.forEach((form) => {
      const completedForm = fetchedCompletedForms.find(
        (completed) => completed.formID === form.form_id
      );

      let status = "-";
      let statusClass = "status-pending";

      if (completedForm) {
        if (completedForm.status && completedForm.status !== 'Not Available') {
          status = completedForm.status;
        } else {
          try {
            const response = JSON.parse(completedForm?.response || "[]");
            const questions = completedForm?.questions || [];
            const yesCount = response.filter(res => res.item_assessed === 'YES').length;
            const totalValid = response.filter(res => res.item_assessed !== 'NA').length;
            const complianceRate = totalValid > 0 ? (yesCount / totalValid) * 100 : 0;

            const failedCritical = response.some((res, idx) =>
              res.item_assessed === 'NO' && (questions[idx]?.startsWith('*') ?? false)
            );

            status = complianceRate >= 80 && !failedCritical ? "Pass" : "Fail";
          } catch (error) {
            console.error(`Error calculating status for ${completedForm.formID}:`, error);
            status = "Error";
          }
        }
        statusClass = status === "Pass" ? "status-pass" : status === "Fail" ? "status-fail" : "status-pending";
      }

      const lastCompleted = completedForm?.last_complete_date
        ? new Date(completedForm.last_complete_date).toLocaleDateString("en-GB")
        : "Not completed";

      allCards.push(
        <div key={`completed-${form.form_id}`} className="assessment-card completed-card">
          <div className="card-header">
            <h6 className="assessment-title">{form.topic}</h6>
            <div className={`status-badge ${statusClass}`}>
              {status}
            </div>
          </div>
          <div className="card-body">
            <div className="assessment-info">
              <div className="info-item">
                <span className="info-label">Target:</span>
                <span className="info-value">Angel Programme</span>
              </div>
              <div className="info-item">
                <span className="info-label">Last Completed:</span>
                <span className="info-value">{lastCompleted}</span>
              </div>
            </div>
          </div>
          <div className="card-actions">
            <button
              className="action-btn primary-btn"
              onClick={(event) => {
                event.preventDefault();
                storeFetchedInfoAndRedirect(event, form);
              }}
            >
              <Feather name="edit-3" size={16} color="white" />
              Take Assessment
            </button>
            <button
              className="action-btn secondary-btn"
              onClick={(event) => viewFormWithModal(event, form)}
            >
              <Feather name="eye" size={16} color="#667eea" />
              View Results
            </button>
          </div>
        </div>
      );
    });

    // Add incomplete forms
    incompleteForms.forEach((form) => {
      allCards.push(
        <div key={`incomplete-${form.form_id}`} className="assessment-card incomplete-card">
          <div className="card-header">
            <h6 className="assessment-title">{form.topic}</h6>
            <div className="status-badge status-pending">
              Pending
            </div>
          </div>
          <div className="card-body">
            <div className="assessment-info">
              <div className="info-item">
                <span className="info-label">Target:</span>
                <span className="info-value">Angel Programme</span>
              </div>
              <div className="info-item">
                <span className="info-label">Status:</span>
                <span className="info-value">Not started</span>
              </div>
            </div>
          </div>
          <div className="card-actions">
            <button
              className="action-btn primary-btn"
              onClick={(event) => {
                event.preventDefault();
                storeFetchedInfoAndRedirect(event, form);
              }}
            >
              <Feather name="play-circle" size={16} color="white" />
              Start Assessment
            </button>
          </div>
        </div>
      );
    });

    return allCards;
  }

  // Update `checkPassFail` to use `safeArray`
  function checkPassFail(formResponse, formQuestions) {
    const questions = safeArray(formQuestions);

    // Calculate compliance rate
    const yesResponses = formResponse.filter(res => res.item_assessed === 'YES').length;
    const totalResponses = formResponse.length - formResponse.filter(res => res.item_assessed === 'NA').length;
    const complianceRate = totalResponses > 0 ? (yesResponses / totalResponses) * 100 : 0;
    console.log("Compliance Rate:", complianceRate);

    const criticalFailures = safeArray(formResponse)
      .some((res, idx) => res.item_assessed === 'NO' && questions[idx]?.startsWith('*'));

    return criticalFailures || complianceRate < 80 ? "Fail" : "Pass";
  }

  // 4. Debugging Logs for Troubleshooting
  console.log("Fetched Completed Forms:", fetchedCompletedForms);
  completedForms.forEach(form => console.log("Form Details:", form));
  console.log("Staff Info:", staffInfo);
  console.log("Component State:", { completedForms, fetchedCompletedForms, auditForms });
  return (
    <div id="ELogbookBackgroundDiv">
      <AlertDialog
        deleteForm={deleteSpecificAttempt}
        openDialog={openDialog}
        handleCloseDialog={handleCloseDialog}
      />
      <SwipeableDrawer open={open} toggleDrawer={toggleDrawer} />
      <Fab
        variant="extended"
        color="primary"
        aria-label="add"
        sx={{
          position: "absolute",
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
          visibility: "visible",
          right: "25px",
          bottom: {
            xs: "70px", // On mobile, position above bottom navigation
            sm: "70px", // On small screens, position above bottom navigation
            md: 0,      // On medium and larger screens, position at bottom
          },
          zIndex: 1999,
        }}
        onClick={() => {
          setOpen(true);
        }}
      >
        <NavigationIcon sx={{ mr: 1 }} />
        Extended
      </Fab>
      <div id="ELogbookInnerDiv">
        <div
          class="modal fade"
          id="ViewCompletedFormModal"
          tabindex="-1"
          role="dialog"
          aria-hidden="true"
        >
          <div class="modal-dialog" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <Chip
                  label="Delete this attempt record"
                  color="warning"
                  onClick={() => {
                    setOpenDialog(true);
                  }}
                />
                <Chip
                  label="Close"
                  variant="outlined"
                  color="warning"
                  onDelete={closeModal}
                />
              </div>
              <div class="modal-body">
                <div id="FormPreviewDiv">
                  {viewForm && currentFormTopic ? (
                    <>
                      <h3>{currentFormTopic}</h3>
                      <h5 className="ViewFormInfo">
                        Completed Date:
                        {isNaN(currentAttempt) &&
                          fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0]
                          ? new Date(
                            fetchedCompletedForms.filter(
                              (form) => form.formID === currentFormID
                            )[0].last_complete_date
                          ).toLocaleDateString("it")
                          : fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0]
                            ? new Date(
                              JSON.parse(
                                fetchedCompletedForms.filter(
                                  (form) => form.formID === currentFormID
                                )[0].past_response[currentAttempt]
                              )[
                                JSON.parse(
                                  fetchedCompletedForms.filter(
                                    (form) => form.formID === currentFormID
                                  )[0].past_response[currentAttempt]
                                ).length - 1
                              ].complete_date
                            ).toLocaleDateString("it")
                            : null}
                      </h5>
                      <h5 className="ViewFormInfo">
                        Compliance Percentage:
                        {
                          staffResponseOnSelectedForm.filter(
                            (response) => response.item_assessed === "YES"
                          ).length
                        }
                        /
                        {staffResponseOnSelectedForm.length -
                          1 -
                          staffResponseOnSelectedForm.filter(
                            (response) => response.item_assessed === "NA"
                          ).length}
                      </h5>
                      <h5 className="ViewFormInfo">
                        Status:
                        {failedOnCriticalItem(viewForm, staffResponseOnSelectedForm)
                          ? "Fail"
                          : "Pass"}
                      </h5>
                      <h5 className="ViewFormInfo">
                        Auditor:{" "}
                        {isNaN(currentAttempt)
                          ? fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0].auditor
                          : JSON.parse(
                            fetchedCompletedForms.filter(
                              (form) => form.formID === currentFormID
                            )[0].past_response[currentAttempt]
                          )[
                            JSON.parse(
                              fetchedCompletedForms.filter(
                                (form) => form.formID === currentFormID
                              )[0].past_response[currentAttempt]
                            ).length - 1
                          ].auditor}
                      </h5>
                      <h5 className="ViewFormInfo">
                        Completed Location:{" "}
                        {isNaN(currentAttempt)
                          ? fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0].staff_department +
                          "/" +
                          fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0].staff_ward
                          : JSON.parse(
                            fetchedCompletedForms.filter(
                              (form) => form.formID === currentFormID
                            )[0].past_response[currentAttempt]
                          )[
                            JSON.parse(
                              fetchedCompletedForms.filter(
                                (form) => form.formID === currentFormID
                              )[0].past_response[currentAttempt]
                            ).length - 1
                          ].staff_department +
                          "/" +
                          fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0].staff_ward}
                      </h5>
                      <h5 className="ViewFormInfo">
                        Remarks:{" "}
                        {isNaN(currentAttempt)
                          ? fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0].form_remarks
                          : JSON.parse(
                            fetchedCompletedForms.filter(
                              (form) => form.formID === currentFormID
                            )[0].past_response[currentAttempt]
                          )[
                            JSON.parse(
                              fetchedCompletedForms.filter(
                                (form) => form.formID === currentFormID
                              )[0].past_response[currentAttempt]
                            ).length - 1
                          ].form_remarks}
                      </h5>
                      <p className="ViewFormInfo">
                        {fetchedCompletedForms.filter(
                          (form) => form.formID === currentFormID
                        )[0].past_response &&
                          fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0].past_response.length > 1
                          ? `There is  ${fetchedCompletedForms.filter(
                            (form) => form.formID === currentFormID
                          )[0].past_response.length
                          } attempts on this form, select attempt to view.`
                          : null}
                      </p>
                      {fetchedCompletedForms.filter(
                        (form) => form.formID === currentFormID
                      )[0].past_response &&
                        fetchedCompletedForms.filter(
                          (form) => form.formID === currentFormID
                        )[0].past_response.length > 1 ? (
                        <select
                          id="ViewHistoryButton"
                          onChange={(event) => {
                            setStaffResponseOnSelectedForm(
                              JSON.parse(
                                fetchedCompletedForms.filter(
                                  (form) => form.formID === currentFormID
                                )[0].past_response[parseInt(event.target.value)]
                              )
                            );
                            setCurrentAttempt(parseInt(event.target.value));
                          }}
                        >
                          <option
                            value={
                              fetchedCompletedForms.filter(
                                (form) => form.formID === currentFormID
                              )[0].past_response.length - 1
                            }
                          >
                            Latest
                          </option>
                          {[
                            ...Array(
                              fetchedCompletedForms.filter(
                                (form) => form.formID === currentFormID
                              )[0].past_response.length - 1
                            ).keys(),
                          ].map((option) => {
                            return (
                              <option value={option}>
                                Attempt {option + 1}
                              </option>
                            );
                          })}
                        </select>
                      ) : null}
                      <hr />
                    </>
                  ) : null}
                  <Table striped bordered hover size="sm">
                    <thead>
                      <tr>
                        <th>#</th>
                        <th>Standard Criteria</th>
                        <th>Source of Information</th>
                        <th>Yes/No/NA</th>
                        <th>Remarks</th>
                      </tr>
                    </thead>
                    <tbody>
                      {viewForm &&
                        currentFormID &&
                        staffResponseOnSelectedForm.length > 0
                        ? viewForm.map((question, index) => {
                          return (
                            <tr>
                              <td>Q{index + 1}.</td>
                              <td>{question}</td>
                              <td>
                                {staffResponseOnSelectedForm[index] &&
                                  staffResponseOnSelectedForm[index]
                                    .source_of_info !== undefined
                                  ? staffResponseOnSelectedForm[index]
                                    .source_of_info
                                  : null}
                              </td>
                              <td>
                                {staffResponseOnSelectedForm[index] &&
                                  staffResponseOnSelectedForm[index]
                                    .item_assessed !== undefined
                                  ? staffResponseOnSelectedForm[index]
                                    .item_assessed
                                  : null}
                              </td>
                              <td>
                                {staffResponseOnSelectedForm[index] &&
                                  staffResponseOnSelectedForm[index].remarks !==
                                  undefined
                                  ? staffResponseOnSelectedForm[index].remarks
                                  : null}
                              </td>
                            </tr>
                          );
                        })
                        : null}
                    </tbody>
                  </Table>
                </div>
              </div>
            </div>
          </div>
        </div>
        {isFetching ? (
          <div id="SpinnerDiv">
            <Spinner animation="grow" role="status">
              <span className="visually-hidden">Loading...</span>
            </Spinner>
            <Spinner animation="grow" role="status">
              <span className="visually-hidden">Loading...</span>
            </Spinner>
            <Spinner animation="grow" role="status">
              <span className="visually-hidden">Loading...</span>
            </Spinner>
          </div>
        ) : (
          <>
            <div id="CardAndButtonRow">
              <div id="StaffCardDiv">
                <div className="staff-card-content">
                  <div className="staff-name-section">
                    <h3 className="staff-name">{staffInfo.staff_name}</h3>
                    <div className="staff-rank-badge">{staffInfo.rank}</div>
                  </div>
                  <div className="staff-details">
                    <div className="detail-row">
                      <span className="detail-label">Department:</span>
                      <span className="detail-value">{staffInfo.department.toUpperCase()}</span>
                    </div>
                    <div className="detail-row">
                      <span className="detail-label">Ward:</span>
                      <span className="detail-value">{staffInfo.ward}</span>
                    </div>
                  </div>
                  <div className="card-footer">
                    <span className="card-logo">T-Class</span>
                  </div>
                </div>
              </div>
              <div id="btn_div">
                <h5 className="account-settings-title">ACCOUNT SETTING</h5>
                <Button
                  variant="outlined"
                  color="secondary"
                  className="action-button"
                  onClick={() => {
                    window.location.assign("/user_passwordchange");
                  }}
                >
                  Reset Password
                </Button>

                <Button
                  variant="outlined"
                  color="secondary"
                  className="action-button"
                  onClick={() => {
                    signout();
                    window.location.reload();
                  }}
                >
                  Sign Out
                </Button>
                <Button
                  variant="outlined"
                  className="action-button angel-program-button"
                  onClick={() => {
                    window.location.href = "https://firebasestorage.googleapis.com/v0/b/tkoh-t-class.appspot.com/o/pdfs%2FRN-preceptorship%20Programme%20Record_TKOH%20(2024)%20V3.pdf?alt=media&token=926bc5aa-3013-4695-9750-9fb07346147a";
                  }}
                >
                  Angel Program Logbook
                </Button>



              </div>
            </div>
            <div id="AuditFormsContentDiv">
              <div id="LearningContractContentHeader">
                <h5>
                  <Feather name="inbox" size={22} color="white" />
                  <span>Learning Contract</span>
                </h5>
              </div>
              <table class="table table-striped">
                <tbody>
                  {learningContracts.map((contract, index) => {
                    return (
                      <tr>
                        <th>
                          <a href={`/learning_contract/${index + 1}`}>
                            {contract}
                          </a>
                        </th>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
              {/* I. Mandatory Skill Assessment */}
              <div className="assessment-section">
                <div className="AuditFormsContentHeader" style={{background: 'linear-gradient(to right, #d32f2f, #f44336)'}}>
                  <h5>
                    <Feather name="clipboard" size={22} color="white" />
                    <span>I. Mandatory Skill Assessment</span>
                  </h5>
                </div>

                <div className="assessment-cards-container">
                  {renderAssessmentCards(mandatoryCompletedForms, mandatoryIncompleteForms)}
                  {(mandatoryCompletedForms.length === 0 && mandatoryIncompleteForms.length === 0) && (
                    <div className="no-assessments-message">
                      <Feather name="info" size={48} color="#ccc" />
                      <h5>No mandatory assessments available.</h5>
                      <p>Check back later for new assessments.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* II. Optional Skill Assessment */}
              <div className="assessment-section">
                <div className="AuditFormsContentHeader" style={{background: 'linear-gradient(to right, #1976d2, #2196f3)'}}>
                  <h5>
                    <Feather name="star" size={22} color="white" />
                    <span>II. Optional Skill Assessment</span>
                  </h5>
                </div>

                <div className="assessment-cards-container">
                  {renderAssessmentCards(optionalCompletedForms, optionalIncompleteForms)}
                  {(optionalCompletedForms.length === 0 && optionalIncompleteForms.length === 0) && (
                    <div className="no-assessments-message">
                      <Feather name="info" size={48} color="#ccc" />
                      <h5>No optional assessments available.</h5>
                      <p>Check back later for new assessments.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Temporary section for uncategorized forms - for debugging */}
              {(uncategorizedCompletedForms.length > 0 || uncategorizedIncompleteForms.length > 0) && (
                <div className="assessment-section">
                  <div className="AuditFormsContentHeader" style={{background: 'linear-gradient(to right, #ff9800, #ffc107)'}}>
                    <h5>
                      <Feather name="alert-triangle" size={22} color="white" />
                      <span>Uncategorized Assessments (Debug)</span>
                    </h5>
                  </div>

                  <div className="assessment-cards-container">
                    {renderAssessmentCards(uncategorizedCompletedForms, uncategorizedIncompleteForms)}
                  </div>
                </div>
              )}

              <div id="AuditFormsContentHeader">
                <h5>
                  <Feather name="inbox" size={22} color="white" />
                  <span>Records of Extra Curriculum Activities</span>
                </h5>
              </div>
              <table class="table table-striped">
                <tbody>
                  <tr>
                    <th>
                      <a href="/extra_activities">
                        Records of Extra Curriculum Activities
                      </a>
                    </th>
                  </tr>
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
