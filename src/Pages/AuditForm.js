import React, { useState, useEffect} from 'react';
import Spinner from "react-bootstrap/Spinner";
import { db } from "../index";
import { doc, getDoc } from "firebase/firestore";
import "./AuditForm.css";
import "../Components/SSRForm";
import SSRForm from '../Components/SSRForm';

export default function AuditForm() {
    const [fethcedForm, setFetchedForm] = useState([]);
    const [staffInfo, setStaffInfo] = useState(null);
    const [formInfo, setFormInfo] = useState(null);

    
    useEffect(() => {
        const fetchAuditForm = async () =>{
            const docID = window.location.pathname.replace("/audit_form/", "");
            const docRef = doc(db, `AuditForms/${docID}/Hide_Collection`, "Questions");
            const docSnap = await getDoc(docRef);
            if (docSnap.exists()) {
                const tmpQuestionBank = docSnap.data().questions;
                setFetchedForm([...tmpQuestionBank])
                //setFetchedForm([...tmpQuestionBank]);
            } else {
                // doc.data() will be undefined in this case
                console.log("No such document!");
            }
        }
        const loadSessionStorage = async() =>{
            const tmpStaffInfo = sessionStorage.getItem("StaffInfo");
            const tmpFormInfo = sessionStorage.getItem("FormInfo");
            setStaffInfo(JSON.parse(tmpStaffInfo));
            setFormInfo(JSON.parse(tmpFormInfo));
        }
        loadSessionStorage();
        fetchAuditForm();
    },[]);

    return (
        <>
            {fethcedForm.length === 0 ? (
                <div id="SpinnerDiv">
                    <Spinner animation="grow" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                    <Spinner animation="grow" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                    <Spinner animation="grow" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                </div>
            ) : (
                <div id="AuditFormOuterWrapper">
                <div id="AuditFormInnerWrapper">
                    <SSRForm name={staffInfo.staff_name} ward={staffInfo.ward} department={staffInfo.department.toUpperCase()} staff_docID={staffInfo.docID} form_docID={formInfo.form_id} standard_no={formInfo.standard_no} standard_statement={formInfo.standard_statement} topic={formInfo.topic} questions={fethcedForm}/>
                </div>
            </div>
            )}
        </>
    )
}