import { useState, useEffect } from 'react';
import { doc, setDoc, getDoc } from "firebase/firestore";
import { db } from "../index";
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import "./ExtraActivities.css"
import AntDesign from 'react-native-vector-icons/dist/AntDesign';
import TextField from '@mui/material/TextField';



export default function ExtraActivities() {
    const [staffInfo, setStaffInfo] = useState(null);
    const [fetchedDraft, setFetchedDraft] = useState(false);
    const [activitiesData, setActivitiesData] = useState({
        experience: "wt",
        bls: "",
        prcc: "",
        acls: "",
        mer: ""
    })

    useEffect(() => {
        const loadSessionStorage = async () => {
            const tmpStaffInfo = sessionStorage.getItem("StaffInfo");
            setStaffInfo(JSON.parse(tmpStaffInfo));
        }
        loadSessionStorage();
    }, [])

    useEffect(() => {
        if (staffInfo && staffInfo.docID) {
            fetchDraft();
        }
    }, [staffInfo])

    function handleChange(key, value) {
        let tmpActivitiesData = activitiesData;
        tmpActivitiesData[key] = value;
        console.log("Updating", tmpActivitiesData);
        setActivitiesData(tmpActivitiesData);
    }

    async function uploadActivities() {
        await setDoc(doc(db, "ExtraActivities", staffInfo.docID), {
            ...activitiesData
        }).then(() => {
            alert("Draft Saved")
        });
    }

    async function fetchDraft() {
        const docRef = doc(db, "ExtraActivities", staffInfo.docID);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            setActivitiesData({ ...activitiesData, ...docSnap.data() });
        } else {
            // doc.data() will be undefined in this case
            console.log("No such document!");
        }
        setFetchedDraft(true);
    }


    return (
        <div id="ExtraActivitiesDiv">
            {staffInfo && fetchedDraft ? (
                <Paper elevation={3} id="ExtraActivitiesPaper">
                    <h1>Records of Extra Curriculum Activities</h1>
                    <h5>Preceptee Name: {staffInfo.staff_name}</h5>
                    <h5>Department/Specialty: {staffInfo.department}</h5>
                    <h5>Ward: {staffInfo.ward}</h5>
                    <Button className="ForgetPasswordButton" variant="outlined" id="ForgetPasswordButtonInApp" onClick={() => { window.location.assign("/e-logbook") }}> <AntDesign name="caretleft" size={14} color="blue" />Back</Button>
                    <hr />
                    <div className="flexRowSingleItem" id="FreeTextDiv">
                        <TextField
                            id="FreeTextField"
                            className="FreeText"
                            label="Experience"
                            multiline
                            helperText="Share your working experience during your preceptee period"
                            defaultValue={activitiesData.experience}
                            onChange={(event) => handleChange("experience", event.target.value)}
                        />
                    </div>
                    <Button className="LearningContractButtons" variant="contained" onClick={() => { uploadActivities() }}>Save Activities</Button>
                </Paper>
            ) : null
            }

        </div >
    )
}