import { useState } from "react";
import "./LoginPanel.css";
import AntDesign from "react-native-vector-icons/dist/AntDesign";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { sha256 } from "crypto-hash";
import { db } from "../index";

export default function AuditorChangePassword() {
  const [username, setUsername] = useState("");
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  async function resetPassword(e) {
    e.preventDefault();
    const docRef = doc(db, "Auditors", username);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const hashAttempt = await sha256(oldPassword);
      const hashcode = await sha256(newPassword);
      if (hashAttempt == docSnap.data().hashcode) {
        if (newPassword == confirmPassword) {
          await updateDoc(docRef, {
            hashcode: hashcode,
          }).then(()=>{
            alert("Successfully update auditor password");
            window.location.assign("/")
          })
        } else {
          alert("New Password & Confirm Password not match");
        }
      } else {
        alert("Old Password Incorrect");
      }
    } else {
      // doc.data() will be undefined in this case
      alert(
        "No auditor registered with this Employee Number, please confirm your Employee Number"
      );
    }
  }

  return (
    <div id="LoginPanelBackground">
      <div class="background">
        <div class="shape"></div>
        <div class="shape"></div>
      </div>
      <form
        onSubmit={(event) => {
          resetPassword(event);
        }}
      >
        <h5>Update Auditor Password</h5>
        <label for="username">Username</label>
        <input
          type="text"
          placeholder="Your Employee Number"
          id="username"
          value={username}
          onChange={(event) => {
            setUsername(event.target.value);
          }}
          autoCapitalize="off"
        />

        <label for="oldPassword">Old Password</label>
        <input
          type="password"
          placeholder="Your Old Password"
          id="oldPassword"
          value={oldPassword}
          onChange={(event) => {
            setOldPassword(event.target.value);
          }}
          autoCapitalize="off"
        />

        <label for="username">New Password</label>
        <input
          type="password"
          placeholder="Your New Password"
          id="newPassword"
          value={newPassword}
          onChange={(event) => {
            setNewPassword(event.target.value);
          }}
          autoCapitalize="off"
        />

        <label for="password">Confirm New Password</label>
        <input
          type="password"
          placeholder="Confirm New Password"
          id="password"
          value={confirmPassword}
          onChange={(event) => {
            setConfirmPassword(event.target.value);
          }}
          autoCapitalize="off"
        />

        <button type="submit">Confirm</button>
      </form>
      <button
        className="ForgetPasswordButton"
        onClick={() => window.location.replace("/")}
      >
        {" "}
        <AntDesign name="caretleft" size={14} color="white" />
        Back to T-Class Homepage
      </button>
    </div>
  );
}
