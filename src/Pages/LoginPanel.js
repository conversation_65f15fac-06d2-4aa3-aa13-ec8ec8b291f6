import React, { useState } from 'react';
import Feather from 'react-native-vector-icons/dist/Feather';
import "./LoginPanel.css";
import { collection, query, getDocs, where } from "firebase/firestore";
import { getAuth, signInWithEmailAndPassword, updateProfile, browserSessionPersistence, setPersistence, sendPasswordResetEmail } from "firebase/auth";
import AntDesign from 'react-native-vector-icons/dist/AntDesign';
import Button from '@mui/material/Button';
import { db } from "../index";

export default function Login() {
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");
    const [email, setEmail] = useState("");



    async function loginWithEmail(event) {
        event.preventDefault();
        const q = query(collection(db, "UsersPendingLogin"), where("username", "==", username));
        const querySnapshot = await getDocs(q);
        if (querySnapshot.size > 0) {
            querySnapshot.forEach((doc) => {
                //Start Login here
                const email = doc.data().email;
                const auth = getAuth();
                setPersistence(auth, browserSessionPersistence)
                signInWithEmailAndPassword(auth, email, password)
                    .then(async (userCredential) => {
                        // Signed in 
                        updateProfile(auth.currentUser, {
                            displayName: username,
                        }).then(() => {
                            // Profile updated!
                            window.location.replace('/e-logbook');
                        }).catch((error) => {
                            // An error occurred

                        });

                        // ...
                    })
                    .catch((error) => {
                        const errorMessage = error.message;
                        console.log("Error during sign in user...", errorMessage)
                        alert("密碼錯誤")
                    });
            });
        } else if (querySnapshot.size === 0) {
            alert("未能在伺服器找到相應帳號，請確認網路連線狀態良好及輸入正確的eLC帳號")
        }

    }

    function openModal(event) {
        event.preventDefault();
        document.getElementById("ViewCompletedFormModal").style.display = "block";
        document.getElementById("ViewCompletedFormModal").classList.add("show");
    }

    function closeModal() {
        document.getElementById("ViewCompletedFormModal").style.display = "none";
        document.getElementById("ViewCompletedFormModal").classList.remove("show");
    }

    async function forgetPassword() {
        const auth = getAuth();
        sendPasswordResetEmail(auth, email)
            .then(() => {
                // Password reset email sent!
                // ..
            })
            .catch((error) => {
                const errorCode = error.code;
                const errorMessage = error.message;
                alert(errorMessage)
                // ..
            });
    }

    return (
        <div id="LoginPanelBackground">
            <div class="background">
                <div class="shape"></div>
                <div class="shape"></div>
            </div>
            <div
                class="modal fade"
                id="ViewCompletedFormModal"
                tabindex="-1"
                role="dialog"
                aria-hidden="true"
            >
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">
                                Send reset password email
                            </h5>
                        </div>
                        <div class="modal-body">
                            <input id="ForgetPasswordEmail" placeholder='Please enter your HA email here' value={email} onChange={(event) => { setEmail(event.target.value) }} />
                        </div>
                        <div class="modal-footer">
                            <Button
                                variant="outlined"
                                onClick={() => {
                                    forgetPassword();
                                    closeModal();
                                }}
                            >
                                Submit
                            </Button>
                            <Button
                                variant="outlined"
                                className="mt-3"
                                onClick={() => {
                                    closeModal();
                                }}
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
            <form onSubmit={(event) => { loginWithEmail(event) }}>
                <h3><Feather name="inbox" size={32} color="white" /> Preceptee-Logbook</h3>
                <label for="username">Username</label>
                <input type="text" placeholder="Empolyee Number" id="username" onChange={(event) => { setUsername(event.target.value) }} autoCapitalize='off' />

                <label for="password">Password</label>
                <input type="password" placeholder="Password" id="password" onChange={(event) => { setPassword(event.target.value) }} autoCapitalize='off' />

                <button type="submit">Log In</button>
                <button className="ForgetPasswordButton" onClick={(event) => { openModal(event) }}>Forget Password</button>
                <a href="mailto:<EMAIL>">Contact Us</a>
                <button className="ForgetPasswordButton" onClick={() => { window.location.assign("/") }}> <AntDesign name="caretleft" size={14} color="white" />Back to T-Class Homepage</button>
            </form>
        </div>
    )
}