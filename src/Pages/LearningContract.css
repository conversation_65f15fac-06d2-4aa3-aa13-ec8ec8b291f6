#LearningContractDiv {
    padding: 1%;
    background-color: white !important;
    overflow: scroll;
    background-image: url("../Assets/AppBackground.jpg");
    background-repeat: no-repeat;
    background-attachment: local;
    background-size: cover;
    opacity: 0.9;
}

#LearningContractDiv h5{
    background-color: white;
    color:black;
    font-weight: 200;
    width: fit-content;
}


.flexRow {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    width: 75%;
    margin-bottom: 25px;
}

.flexRowSingleItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 75%;
    justify-content: space-around;
    margin-bottom: 25px;
}

.flexRow label {
    font-weight: 300;
    padding: 0;
    width: 100px;
}

.flexRowSingleItem label {
    font-weight: 300;
    padding: 0;
    width: 100px;
}

.DateSelectionInRow {
    width: 25%;
    background-color: white;
}

.DateSelection {
    width: 70%;
    background-color: white;
}

.FreeTextBar {
    width: 69.7%;
    min-height: 200px;
}

.LearningContractButtons {
    width: 90%;
    align-self: center;
    left: 5%;
    position: relative;
    margin-bottom: 5px;
}

#ForgetPasswordButtonInApp{
    color:blue;
    width:10%;
}

#LearningContractPaper{
    padding:2%;
}