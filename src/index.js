import React from 'react';
import { render } from 'react-dom';
import './index.css';

import reportWebVitals from './reportWebVitals';
import 'bootstrap/dist/css/bootstrap.min.css';
import "./Icons";
import TopLevelRouter from './TopLevelRouter';


// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";



// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyBiE3f_JniaLCj4ADdPLY7zJffngR99-sQ",
  authDomain: "tkoh-t-class.firebaseapp.com",
  projectId: "tkoh-t-class",
  storageBucket: "tkoh-t-class.appspot.com",
  messagingSenderId: "89706928680",
  appId: "1:89706928680:web:4b5e890692289d8e37331d",
  measurementId: "G-3HV677MM73"
};

// Initialize Firebase
export const Firebase = initializeApp(firebaseConfig);
export const db = getFirestore();
export const storage = getStorage(Firebase);

const rootElement = document.getElementById("root");

render(
  <TopLevelRouter/>,
  rootElement
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
