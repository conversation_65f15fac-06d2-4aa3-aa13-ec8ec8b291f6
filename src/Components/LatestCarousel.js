import React from 'react';
import Carousel from 'react-bootstrap/Carousel';
import "./LatestCarousel.css"

export default function LatestCarousel(props) {
    return (
        <Carousel id="LatestCarousel" interval={props.interval} indicators={false}>
            {props.images_array.map((image, index) => {
                return (
                    <Carousel.Item>
                        <img
                            className="d-block w-100"
                            src={image}
                            id={`carousel-image${index}`}
                            alt="ImageSlide"
                        />
                    </Carousel.Item>
                )
            })}
        </Carousel>
    )
}
