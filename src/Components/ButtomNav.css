.css-imwso6-Mu<PERSON>BottomNavigationAction-label{
    font-size: 0.75rem !important;
    white-space: nowrap !important;
}

/* General styling for all bottom navigation labels */
.MuiBottomNavigationAction-label {
    font-size: 0.75rem !important;
    white-space: nowrap !important;
    margin-top: 4px !important;
    line-height: 1.2 !important;
}

/* Ensure bottom navigation has high z-index and proper styling for mobile */
.MuiBottomNavigation-root {
    z-index: 2000 !important; /* Higher than other elements to prevent overlap issues */
    min-height: 64px !important; /* Ensure enough height for icons and labels */
}

/* Style for bottom navigation actions */
.MuiBottomNavigationAction-root {
    padding: 6px 12px 8px !important;
    min-width: 64px !important;
}

/* Additional mobile-specific styling */
@media only screen and (hover: none) and (pointer: coarse) {
    .MuiBottomNavigation-root {
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* Add shadow for better visibility */
        border-top: 1px solid #e0e0e0; /* Add subtle border */
        min-height: 70px !important; /* Slightly taller on mobile for better touch targets */
    }

    .MuiBottomNavigationAction-label {
        font-size: 0.7rem !important; /* Slightly smaller on mobile to fit better */
    }

    .MuiBottomNavigationAction-root {
        padding: 8px 4px 8px !important; /* Adjust padding for mobile */
    }
}