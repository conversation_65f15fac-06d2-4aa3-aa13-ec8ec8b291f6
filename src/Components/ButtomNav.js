import * as React from 'react';
import Box from '@mui/material/Box';
import BottomNavigation from '@mui/material/BottomNavigation';
import BottomNavigationAction from '@mui/material/BottomNavigationAction';
import RestoreIcon from '@mui/icons-material/Restore';
import FavoriteIcon from '@mui/icons-material/Favorite';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import BookmarksIcon from '@mui/icons-material/Bookmarks';
import BookIcon from '@mui/icons-material/Book';
import EditLocationAltIcon from '@mui/icons-material/EditLocationAlt';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import "./ButtomNav.css"

import NewspaperIcon from '@mui/icons-material/Newspaper';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import AutoStoriesIcon from '@mui/icons-material/AutoStories';
import { Link } from "react-router-dom";
// import Link from "next/link";
export default function ButtomNav() {
  
  //const Pathname = Window.location.pathname;

  return (
    <Box sx={{ width: 'auto' ,position: 'fixed', bottom: 0, left: 0, right: 0 }}> 
      <BottomNavigation
        showLabels
      >
        <BottomNavigationAction label="中央培訓素材" icon={<MedicalInformationIcon/>} sx={{fontSize:"min(1 em,1 vmax) !important"}} component="a" href ='https://tkohnsd2021.wixsite.com/my-site/services-1'  />

        <BottomNavigationAction label="部門培訓錦囊" icon={<LocalHospitalIcon />} />


      

        <BottomNavigationAction label="護士培訓手冊"  icon={<AutoStoriesIcon/>} component={Link} to="/e-logbook"/>
     
        

        <BottomNavigationAction label="網上報名"  icon={<EditLocationAltIcon/>} component={Link} to="/online_application"/>
     

       

      
      </BottomNavigation>
    </Box>
  );
}
