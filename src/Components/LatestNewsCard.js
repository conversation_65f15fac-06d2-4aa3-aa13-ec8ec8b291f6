import React from 'react';
import Card from 'react-bootstrap/Card';
import ListGroup  from 'react-bootstrap/ListGroup';
import "./LatestNewsCard.css";

export default function LatestNewsCard(props) {
    return (
        <Card>
            <Card.Header>{props.title}</Card.Header>
            {props.list_items?(
                <ListGroup variant="flush">
                {props.list_items.map(item=>{
                    return(
                        <ListGroup.Item><a href={item.link} target="_blank" rel="noreferrer">{item.title}</a></ListGroup.Item>
                    )
                })}
            </ListGroup>
            ):null}
        </Card>
    )
}