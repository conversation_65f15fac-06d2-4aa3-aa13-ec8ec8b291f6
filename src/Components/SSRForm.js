import { useRef, useState } from 'react';
import Button from '@mui/material/Button';
import "./SSRForm.css";
import SignFormModal from '../Components/SignFormModal';
import TextareaAutosize from '@mui/base/TextareaAutosize';

import { db } from "../index";
import { doc, setDoc, getDoc } from "firebase/firestore";




export default function SSRForm(props) {
    const formResult = useRef(new Array(props.questions.length));
    const [previewMode, setPreviewMode] = useState(false);


    async function handleSourceOfInfo(choice, index) {
        document.getElementById(`Q${index + 1}_yes_button`).checked = true;
        formResult.current[index] = {
            source_of_info: choice,
            item_assessed: "YES",
            remarks: document.getElementById(`Q${index + 1}_remarks`).value || ""
        };
    }

    async function handleYesChoice(index) {
        formResult.current[index] = {
            source_of_info: "",
            item_assessed: "YES",
            remarks: document.getElementById(`Q${index + 1}_remarks`).value || ""
        };
        console.log("FormResult", formResult);
    }

    function handleNotAssessed(choice, index) {
        document.getElementById(`Q${index + 1}_an_button`).checked = false;
        document.getElementById(`Q${index + 1}_ap_button`).checked = false;
        document.getElementById(`Q${index + 1}_af_button`).checked = false;
        document.getElementById(`Q${index + 1}_o_button`).checked = false;
        document.getElementById(`Q${index + 1}_cr_button`).checked = false;
        formResult.current[index] = {
            source_of_info: "",
            item_assessed: choice,
            remarks: document.getElementById(`Q${index + 1}_remarks`).value || ""
        };
        console.log("FormResult", formResult);
    }

    function handleRemarks(string, index) {
        if (formResult.current[index] !== undefined) {
            formResult.current[index].remarks = string;
            console.log("FormResult", formResult);
        } else {
            formResult.current[index] = {
                source_of_info: "",
                item_assessed: "",
                remarks: string
            };
            console.log("FormResult", formResult);
        }
    }

    function checkFormValidity() {
        for (let index = 0; index < formResult.current.length; index++) {
            if (formResult.current[index] === undefined) {
                alert("Response cannot be empty");
                document.getElementById(`Q${index + 1}_div`).scrollIntoView({ behavior: "smooth" });
                return false;
            } else if (formResult.current[index].item_assessed === "YES" && formResult.current[index].source_of_info === "") {
                alert("Source of Information should be checked");
                document.getElementById(`Q${index + 1}_div`).scrollIntoView({ behavior: "smooth" });
                return false;
            } else if (props.questions[index].startsWith('*') && formResult.current[index].item_assessed === "NA") {
                alert("Item started with * is labeled as critical item and must be assessed");
                document.getElementById(`Q${index + 1}_div`).scrollIntoView({ behavior: "smooth" });
                return false;
            } else if (index === props.questions.length - 1) {
                return true;
            }
        }
    };

    function showCouterSignModal() {
        const valid = checkFormValidity();
        if (valid) {
            openModal();
        }
    }

    function openModal() {
        document.getElementById("signatureModal").style.display = "block";
        document.getElementById("signatureModal").classList.add("show");
    }

    function autoComplete() {
        for (var i = 0; i < props.questions.length; i++) {
            document.getElementById(`Q${i + 1}_yes_button`).checked = true;
            document.getElementById(`Q${i + 1}_o_button`).checked = true;
            formResult.current[i] = {
                source_of_info: "O",
                item_assessed: "YES",
                remarks: document.getElementById(`Q${i + 1}_remarks`).value || ""
            };
        }
    }

    async function submitResponse(auditor) {
        var tmp_formResult = formResult.current;
        var infoObject = { complete_date: new Date(document.getElementById("RetrospectiveDate").value).getTime(), auditor: auditor, form_remarks: document.getElementById("FormRemarks").value }
        tmp_formResult.push(infoObject);
        const jsonForm = JSON.stringify(tmp_formResult);
        // Add a new document in collection "cities"
        const docRef = doc(db, `AuditForms/${props.form_docID}/Completed_Forms`, props.staff_docID);
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
            await setDoc(docRef, {
                staff_name: props.name,
                staff_ward: props.ward,
                staff_department: props.department,
                staff_docID: props.staff_docID,
                auditor: auditor,
                response: jsonForm,
                past_response: docSnap.data().past_response ? [...docSnap.data().past_response, jsonForm] : [jsonForm],
                last_complete_date: new Date(document.getElementById("RetrospectiveDate").value).getTime(),
                form_id: props.form_docID,
                form_remarks: document.getElementById("FormRemarks").value
            }, { merge: true })
                .then((res => {
                    alert("Competency Assessment Signed and Uploaded!");
                    window.location.replace("/e-logbook");
                }))
                .catch(error => {
                    console.log("Error occurs on uploading form", error)
                })
        } else {
            await setDoc(docRef, {
                staff_name: props.name,
                staff_ward: props.ward,
                staff_department: props.department,
                staff_docID: props.staff_docID,
                auditor: auditor,
                response: jsonForm,
                past_response: [jsonForm],
                last_complete_date: new Date(document.getElementById("RetrospectiveDate").value).getTime(),
                form_id: props.form_docID,
                form_remarks: document.getElementById("FormRemarks").value
            }, { merge: true })
                .then((res => {
                    alert("Competency Assessment Signed and Uploaded!");
                    window.location.replace("/e-logbook");
                }))
                .catch(error => {
                    console.log("Error occurs on uploading form", error)
                })
        }
    }




    return (
        <>
            <Button id="PreviewButton" variant="contained" onClick={() => { setPreviewMode(!previewMode) }}>{previewMode ? "Fill Form" : "Preview Form"}</Button>
            <Button id="AutoFillButton" variant="contained" color="success" onClick={() => { autoComplete() }}>Auto Fill</Button>
            <Button id="BackButton" variant="outlined" onClick={() => window.location.replace("/e-logbook")}>Back</Button>
            <SignFormModal onSubmit={submitResponse} />
            <div id="InfoBox">
                <h3>{props.topic} - Competency Assessment</h3>
                <h5><b>Perceptee:</b> {props.name}</h5>
                <h5><b>Unit/Dept.</b> {props.ward}/{props.department}</h5>
                <h5><b>Date:</b> {new Date().toLocaleDateString('it')}</h5>
                <h5><b>Nursing Standard No. </b>{props.standard_no}</h5>
                <h5><b>Nursing Standard Statement </b>{props.standard_statement}</h5>
                <h5><b>Evaluation Date:</b> </h5>
                <input className="DateSelection mb-3" id="RetrospectiveDate" type="date" defaultValue={new Date().toLocaleDateString("en-CA")} />
                <TextareaAutosize id="FormRemarks" defaultValue={props.form_remarks} className="p-2 mb-2" placeholder="Name of Auditor ( if using shared account )"></TextareaAutosize>
                <h5 style={{ "color": "red" }}>Critical Items (*) must be assessed</h5>
            </div>
            <div id="QuestionDiv">
                {/* Button for single question per screen system
                <button className="controlQuestionButton" onClick={() => { previousQuestion() }}>
                    <Feather name="arrow-left-circle" size={20} color="gray" />
                </button>
                */}
                <div id="QuestionContent">
                    {props.questions.map((question, index) => {
                        return (
                            <div id={`Q${index + 1}_div`} className="questionDiv">
                                <hr />
                                <h3><span style={question.startsWith("*") ? { "color": "red" } : {}}>{question.startsWith("*") ? "*" : null}Q{`${index + 1}`}.</span>{question.replace("*", "")}</h3>
                                {previewMode ? null : (
                                    <>
                                        <div className="RadioRow">
                                            <h4>Item Assessed:</h4>
                                            { /* Enable on Click function to ensure check of Source of Information to yes_button if need*/}
                                            <input type="radio" name={`Q${index + 1}_item_assessed_choice`} id={`Q${index + 1}_yes_button`} value="YES" onClick={() => { handleYesChoice(index) }} />
                                            <label for="yes_button">Yes</label>
                                            <input type="radio" name={`Q${index + 1}_item_assessed_choice`} id={`Q${index + 1}_no_button`} value="NO" onClick={(event) => { handleNotAssessed(event.target.value, index) }} />
                                            <label for="no_button">No</label>
                                            <input type="radio" name={`Q${index + 1}_item_assessed_choice`} id={`Q${index + 1}_na_button`} value="NA" onClick={(event) => { handleNotAssessed(event.target.value, index) }} />
                                            <label for="na_button">Not Applicable</label>
                                        </div>
                                        <div className="RadioRow">
                                            <h4>Source of Information:</h4>
                                            <input type="radio" name={`Q${index + 1}_source_of_information_choices`} id={`Q${index + 1}_an_button`} value="AN" onClick={(event) => { handleSourceOfInfo(event.target.value, index) }} />
                                            <label for="an_button">Ask Nurse</label>
                                            <input type="radio" name={`Q${index + 1}_source_of_information_choices`} id={`Q${index + 1}_ap_button`} value="AP" onClick={(event) => { handleSourceOfInfo(event.target.value, index) }} />
                                            <label for="ap_button">Ask Patient</label>
                                            <input type="radio" name={`Q${index + 1}_source_of_information_choices`} id={`Q${index + 1}_af_button`} value="AF" onClick={(event) => { handleSourceOfInfo(event.target.value, index) }} />
                                            <label for="af_button">Ask Family</label>
                                            <input type="radio" name={`Q${index + 1}_source_of_information_choices`} id={`Q${index + 1}_o_button`} value="O" onClick={(event) => { handleSourceOfInfo(event.target.value, index) }} />
                                            <label for="o_button">Observe</label>
                                            <input type="radio" name={`Q${index + 1}_source_of_information_choices`} id={`Q${index + 1}_cr_button`} value="CR" onClick={(event) => { handleSourceOfInfo(event.target.value, index) }} />
                                            <label for="cr_button">Check Record</label>
                                        </div>
                                        <div className="RadioRow">
                                            <h4>Remarks:</h4>
                                            <input type="text" className="Remarks" id={`Q${index + 1}_remarks`} onChange={(event) => { handleRemarks(event.target.value, index) }} />
                                        </div>
                                    </>
                                )}
                            </div>
                        )
                    })}
                    {/*<h5>{questionNumber + 1}/{props.questions.length}</h5> ---Question Index for single question per screen system*/}
                    <hr />
                </div>
                {/* Button for single question per screen system
                <button className="controlQuestionButton" onClick={() => { nextQuestion() }}>
                    <Feather name="arrow-right-circle" size={20} color="gray" />
                </button>
                */}
                <Button variant="success" title="Submit" onClick={() => { showCouterSignModal() }}>Submit</Button>
            </div>
        </>
    )
}