#InfoBox {
    margin-right: auto;
    width: 100%;
    display: flex !important;
    flex-direction: column;
}

#InfoBox h5 {
    font-size: 2vh;
    color:black;
    font-weight: 200;
}

#QuestionDiv {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;
}

#QuestionContent {
    width: 90%;
    height: 100%;
    margin-bottom: 10%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

#QuestionContent hr {
    color: black !important;
    width: 98%;
}

#QuestionContent h3 {
    font-size: 3vmin;
}

.controlQuestionButton {
    border: none;
    height: 30px;
    width: 6%;
    padding: 0;
    overflow: visible;
}

.RadioRow {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    margin-left: -24px;
    margin-top: 20px;
    margin-bottom: 40px;
}

.RadioRow input {
    height: 3vmin;
    width: 3vmin;
    padding: 0;
    margin: 0;
}

.Remarks {
    width: 80% !important;
    height: 4vh !important;
    margin-left: 15px;
}

.RadioRow h4 {
    font-size: 2vmin;
    margin-right: 15px;
}

.RadioRow label {
    height: 24px;
    width: auto;
    font-family: 'Noto Sans HK';
    font-weight: 300;
    color: rgb(0, 122, 255);
    font-size: 2vmin;
    margin-right: 5px;
    padding: 0;
}

@media (min-width:960px) {
    .RadioRow input {
        height: 2vmin;
        width: 2vmin;
        padding: 0;
        margin: 0;
    }
}

@media(min-width:960px) {
    .RadioRow label {
        height: 24px;
        width: auto;
        font-family: 'Noto Sans HK';
        font-weight: 300;
        color: rgb(0, 122, 255);
        font-size: 2vmin;
        margin-right: 20px;
        padding: 0;
    }
}

#PreviewButton {
    margin: 0px;
    width: 35%;
    position: fixed;
    top: 90%;
    right: 5%;
    opacity: 0.8;
}

#BackButton {
    margin: 0px;
    width: 30%;
    position: fixed;
    top: 90%;
    left: 5%;
    opacity: 0.8;
    background-color: black;
}


.questionDiv {
    width: 100%;
}

.sign_login {
    padding: 5px;
    border: 1px solid rgba(0, 0, 0, 0.5);
}

.modal-body label {
    font-weight: 300;
    margin: 0px;
}

#FormRemarks {
    height: 2.5vh;
    resize: none;
    ;
    width: 70%;
}

#AutoFillButton {
    margin: 0px;
    width: 30%;
    position: fixed;
    top: 15%;
    right: 5%;
    opacity: 0.8;
}