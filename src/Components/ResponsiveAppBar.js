import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Menu from "@mui/material/Menu";
import MenuIcon from "@mui/icons-material/Menu";
import Container from "@mui/material/Container";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import Tooltip from "@mui/material/Tooltip";
import MenuItem from "@mui/material/MenuItem";

const TKOHLogoNGRgb = require("../Assets/TKOHLogoNHRgb.png");
const Banner = require("../Assets/WelcomeBanner.png");
const TKOHTCLASSLogo = require("../Assets/tclasslogo.png");
const pages = [
 // "網上報名",
  
 // "中央培訓素材",
  //"護士培訓手冊",
  
  "E-Logbook Auditor更改密碼",
  "用戶教學影片",
];

const ResponsiveAppBar = () => {
  const [anchorElNav, setAnchorElNav] = React.useState(null);
  const [anchorElUser, setAnchorElUser] = React.useState(null);

  const handleOpenNavMenu = (event) => {
    setAnchorElNav(event.currentTarget);
  };

  const handleCloseNavMenu = () => {
    setAnchorElNav(null);
  };

  function handlePageNavigate(page) {
    switch (page) {
      //case "網上報名":
      //  window.location.assign("/online_application");
      //  break;
      //case "護士培訓手冊":
      //  window.location.assign("/e-logbook");
      //  break;
        
      //  case "中央培訓素材":
      //    window.location.href='https://tkohnsd2021.wixsite.com/my-site/services-1';
      //    break;
        case "E-Logbook Auditor更改密碼":
        window.location.assign("/auditor_passwordchange");
          break;
        case "用戶教學影片":
       
        window.location.href=' https://tkohnsd2021.wixsite.com/my-site/projects-2';
      
         break;
    default:
        handleCloseNavMenu();
    }
  }

  return (
    <AppBar position="sticky" sx={{ bgcolor: "white" }}>
      <Container maxWidth="xl">
        <Toolbar disableGutters>
          <img id="TkohLogoFull" src={Banner} alt="Tkoh Logo" />
          
          
          <Box
            sx={{
              flexGrow: 1,
              color: "black",
              maxWidth: "30%",
              marginLeft: "auto",
              justifyContent: "flex-end",
            }}
          >
            <Tooltip title="Open pages">
              <IconButton
                size="small"
                aria-label="account of current user"
                aria-controls="menu-appbar"
                aria-haspopup="true"
                onClick={handleOpenNavMenu}
                color="inherit"
              >
                <MenuIcon />
              </IconButton>
            </Tooltip>
            <Menu
              sx={{ mt: "45px" }}
              id="menu-appbar"
              anchorEl={anchorElUser}
              anchorOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
              keepMounted
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
              open={Boolean(anchorElNav)}
              onClose={handleCloseNavMenu}
            >
              {pages.map((pages) => (
                <MenuItem key={pages} onClick={() => handlePageNavigate(pages)}>
                  <Typography textAlign="center">{pages}</Typography>
                </MenuItem>
              ))}
            </Menu>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};
export default ResponsiveAppBar;
