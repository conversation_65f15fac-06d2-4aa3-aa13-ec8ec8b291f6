import Navbar from "react-bootstrap/NavBar";
import Nav from "react-bootstrap/Nav";
import "./AppNavBar.css"
import FeatherIcon from 'react-native-vector-icons/dist/Feather';
import { useState } from 'react';

export default function AppNavbar(){
    const [extendNavBar, setExtendNavBar ] = useState(false);
    return (
        <Navbar bg="light" variant="light" id="AppNavBar" fixed="top"> 
                {extendNavBar?
                <div id="ExtendedNavBar">
                    <Nav.Link href="/">首頁</Nav.Link>
                    <Nav.Link href="/">網上報名</Nav.Link>
                    <Nav.Link href="/">部門培訓</Nav.Link>
                    <Nav.Link href="/">中央培訓</Nav.Link>
                    <Nav.Link href="/">新入職同事培訓手冊</Nav.Link>
                    <Nav.Link href="/">稽查表格</Nav.Link>
                    <Nav.Link href="/">NSD Channel</Nav.Link>
                    <Nav.Link href="/">其他活動</Nav.Link>
                    <Nav.Link href="/">意見箱</Nav.Link>
                </div>:null}
                <button id="AppNavBarExtendButton" onClick={()=>{setExtendNavBar(!extendNavBar)}}>
                <FeatherIcon name="align-justify" size={24} />
                </button>
        </Navbar>
    )
}