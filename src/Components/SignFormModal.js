import { db } from "../index";
import { doc, getDoc } from "firebase/firestore";
import { useState } from 'react';
import {sha256} from 'crypto-hash';


export default function SignFormModal(props) {
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");

    function closeModal() {
        document.getElementById("signatureModal").style.display = "none";
        document.getElementById("signatureModal").classList.remove("show");
    }

    async function compareHash() {
        const docRef = doc(db, "Auditors", username);
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
            const hashAttempt = await sha256(password);
            if(hashAttempt == docSnap.data().hashcode){
                await props.onSubmit(docSnap.data().staffInfo);
            }else{
                alert("Incorrect Password")
            }
            
        } else {
            alert("Auditor account not registered")
        }
    }

    return (
        <div
            class="modal fade"
            id="signatureModal"
            tabindex="-1"
            role="dialog"
            data-docID=""
            data-roomNumber=""
            aria-hidden="true"
        >
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">
                            Sign Competency Assessment
                        </h5>
                    </div>
                    <div class="modal-body">
                        <input type="text" id="auditor_username" className="sign_login" value={username} onChange={(event) => { setUsername(event.target.value) }} />
                        <label for="auditor_username">Auditor username</label>
                        <input type="password" id="auditor_password" className="sign_login" value={password} onChange={(event) => { setPassword(event.target.value) }} />
                        <label for="auditor_username">Auditor Password</label>
                    </div>
                    <div class="modal-footer">
                        <button
                            type="button"
                            class="btn btn-primary"
                            onClick={() => { compareHash() }}
                        >
                            Sign
                        </button>
                        <button
                            type="button"
                            class="btn btn-secondary"
                            onClick={() => {
                                closeModal();
                            }}
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    )
}