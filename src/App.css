body>#root>div {
  height: 100vh;
  background-color: #eaeef6;
}

.App {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f6f6f7;
  color: black;
  background-image: url("./Assets/AppBackground.jpg");
  background-attachment: local;
  background-size: cover;
  overflow-y: scroll;
  position: relative;
}

.TopJumbotron {
  width: 100%;
  height: auto;
  position: relative;
}

.TopJumbotron img{
  width: 100%;
  height: auto;
}

#LatestCarouselDiv {
  height: 60%;
  margin-top: 2%;
  order: 1;
  background-color: #000000;
}

.ContentDiv {
  margin-bottom: 5vh;
  flex: 2;
}


#TopJumbotron {
  width: 100%;
  opacity: 0.9;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-bottom: 0.5px solid #353535;
  margin-bottom: 2%;
  position: relative;
  top: 0;
  background-color: #eaeef6;
  overflow-x: scroll;
}

#TopJumbotron::before {
  opacity: 0.8;
}

@media (min-width:960px) {
  #TopJumbotronInner {
    width: 70%;
    height: 100%;
    padding: 1%;
  }
}
/* CSS */
.button-54 {
  font-family: "Open Sans", sans-serif;
  font-size: 1px;
  letter-spacing: 2px;
  text-decoration: none;
  text-transform: uppercase;
  color: rgb(236, 12, 12);
  cursor: pointer;
  border: 3px solid;
  padding: 0.25em 0.5em;
  box-shadow: 1px 1px 0px 0px, 2px 2px 0px 0px, 3px 3px 0px 0px, 4px 4px 0px 0px, 5px 5px 0px 0px;
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  margin: 5px;
    padding: 0px;
    height: 120px;
  
}

.button-54:active {
  box-shadow: 0px 0px 0px 0px;
  top: 5px;
  left: 5px;
}

@media (min-width: 768px) {
  .button-54 {
    padding: 0.25em 0.75em;
  }
}


@media (max-width:960px) {

  #CarouselContainer {
    min-height: 350px;
    overflow: hidden;
    border-radius: 15px;
    padding: 3%;
  }

  .MainFunctionCol h3 {
    font-size: 15px;
    font-weight: 300;
  }

  #FooterRow {
    order: 2;
    margin-bottom: 25px;
  }

  #MainFunctionsRow {
    order: 3;
  }

  #FooterSmall {
    order: 5;
  }

  #TopJumbotron {
    width: 100%;
    min-height: 150px;
  }

  #TkohLogoFull {
    width: 25%;
  }
  #TclassLogoFull{
    width : 1%}

   
  .MainFunctionCol {
    background-color:white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-transform: uppercase;
    flex: 1;
    border: none;
    margin: 5px;
    padding: 0px;
    height: 120px;
    border-bottom: 10px solid rgb(27, 177, 60);
    min-width: 5%;
    border-radius: 20%;
    cursor: pointer;
    box-shadow: 1px 1px 0px 0px, 2px 2px 0px 0px, 3px 3px 0px 0px, 4px 4px 0px 0px, 5px 5px 0px 0px;
  }
  .MainFunctionCol:active {
    box-shadow: 0px 0px 0px 0px;
    top: 5px;
    left: 5px;
  }

  #MainFunctionsRow {
    width: 100%;
    display: flex;
    flex-direction: row;
    z-index: 999;
    width: 100%;
    height: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between flex-start;
    margin-top: auto;
    margin-bottom: auto;
  }
}

.dropdown-item {
  background: #323232;
  color: white !important;
}


.dropdown-toggle {
  margin-top: 0;
}

#TkohLogoFull {
  height: 100px;
width:90%;

padding-left: 10%;
  margin-bottom: 0px;
  padding: 0px;
}
#TclassLogoFull{
  height: 50px;
  width : 50px;
  
  padding: 10px;}


@media (min-width:960px) {
  #CarouselContainer {
    padding-left: 18%;
    padding-right: 18%;
    margin-top:auto;
    background-color: #000000;
    padding-top: 1%;
    padding-bottom: 1%;
    
  }

  #MainFunctionsRow {
    width: 100%;
    display: flex;
    flex-direction: row;
    z-index: 999;
    width: 100%;
    height: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between flex-start;
    margin-top: auto;
    margin-bottom: auto;
  }

  #FooterRow {
    padding-left: 18%;
    padding-right: 18%;
  }

  #TkohLogoFull {
    width: 100%;
  }
  #TclassLogoFull{
    width: 5%;
  height : 5%;}
/*Desktop*/
  .MainFunctionCol {
    flex: 1;
    background-color:white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    
    border:none;
    border-bottom: 5px solid rgb(27, 177, 60);;
    margin: 5px;
    padding: 0px;
    height: 120px;
    border-radius: 60%;
    box-shadow: 1px 1px 0px 0px, 2px 2px 0px 0px, 3px 3px 0px 0px, 4px 4px 0px 0px, 5px 5px 0px 0px;

    
    
    
    
    
    
    
    min-width: 10%;


  }

  .MainFunctionCol h3 {
    font-size: 18px;
    font-weight: 200;
  }

  .TopJumbotron h1{
    background-color: white;
    position: absolute;
    top:1%;
    
    left: 300px;
    font-size: 100px;
    padding:10px;
    padding-left:10px;
  }

  .TopJumbotron p{
    background-color: white;
    position: absolute;
    top:62%;
    right:0;
    font-weight: 200;
    letter-spacing: 1px;
  }
}


#FooterRow {
  display: flex;
  flex-direction: row;
  height: 25%;
  width: 100%;
  font-weight: 200 !important;
  height: 340px;
}

#FooterRow h3 {
  font-weight: 200;
  padding-left: 1vw;
}

.FooterCol {
  flex: 1;
  width: 50%;
}

#WhatsNews {
  display: flex;
  flex-direction: column;
  padding-left: 2%;
  margin-top: auto;
  border-radius: 10px;
  background-image: url("./Assets/AppBackground.jpg");
  overflow: auto;
  height: 150px;
  margin-left: 15px;
  padding: 1%;
  position: relative;
  background-color: white;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
}

#RecentPhoto {
  border-radius: 10px;
  margin-top: 15px;
  overflow: hidden;
  height: 340px;
  padding-top: 5px;
  position: relative;
  background-color: white;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
}

#RecentPhoto h3 {
  margin-left: 5px;
}

@media (max-width:960px) {
  #WhatsNews {
    height: 130px;
    width: 90%;
    margin-top :auto;
    margin-left: 20px;
    margin-right: 5px;
  }

  #FooterRow {
    flex-direction: column;
    align-items: center;
    height: 80%;
  }

  #RecentPhoto {
    order: 4;
    width: 90%;
    min-height: 270px;
  }

  .App {
    padding-bottom: 100px;
  }

  .TopJumbotron h1{
    background-color: white;
    position: absolute;
    top:-7%;
    left: 160px;
    letter-spacing: 1px;
    padding:0px 0px 0px 0px;
    font-size: 10px;
  }

  .TopJumbotron p{
    background-color: white;
    position: absolute;
    top:33%;
    right:0;
    font-weight: 200;
    font-size: 10px;
  }
}

#FooterSmall {
  margin-top: 25px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: gray;
  font-weight: 300;
  padding-bottom: 15px;
}

#FooterSmall p {
  font-weight: 300;
  margin: 0px;
}

.CustomNav {
  display: flex;
  flex-direction: row;
  width: 50%;
  position: absolute;
  right: 0;
  bottom: 15px;
}