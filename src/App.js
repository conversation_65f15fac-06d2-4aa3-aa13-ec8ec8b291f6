import { useState, useEffect } from "react";
import { getStorage, ref, getDownloadURL, listAll } from "firebase/storage";
import { collection, query, getDocs } from "firebase/firestore";
import Button from '@mui/material/Button';
import { db } from "./index";

import "./App.css";

import Spinner from "react-bootstrap/Spinner";
import LatestCarousel from "./Components/LatestCarousel";
import TKOHGIF from "./Assets/TKOHGIF.gif";
import TopJumbotron from "./Assets/TopJumbotron.jpg";
import FontAwesome from "react-native-vector-icons/dist/FontAwesome";
import BookmarksIcon from '@mui/icons-material/Bookmarks';
import BookIcon from '@mui/icons-material/Book';
import EditLocationAltIcon from '@mui/icons-material/EditLocationAlt';
import NewspaperIcon from '@mui/icons-material/Newspaper';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import AutoStoriesIcon from '@mui/icons-material/AutoStories';
import ResponsiveAppBar from "./Components/ResponsiveAppBar";
import NavBar from "./Components/NavBar";
import ButtomNav from "./Components/ButtomNav";

import { color } from "@mui/system";

export default function App() {
  const [fetched, setFetched] = useState(false);
  const [imagesURLs, setImagesURLs] = useState([]);
  const [recentPhotosURLs, setRecentPhotosURLs] = useState([]);
  const [whatsNews, setWhatsNews] = useState([]);

  useEffect(() => {
    fetchWhatsNews();
    fetchCarouselImages();
    fetchRecentPhotos();
  }, []);

  async function fetchCarouselImages() {
    const storage = getStorage();
    const listRef = ref(storage, "images/home_carousel");
    var tmp_imagesURLs = [];
    var itemsProcessed = 0;

    await listAll(listRef).then((res) => {
      res.items.forEach(async (itemRef, index, array) => {
        // All the items under listRef.
        await getDownloadURL(itemRef)
          .then((url) => {
            itemsProcessed++;
            // `url` is the download URL for 'images/stars.jpg'
            tmp_imagesURLs.push(url);
            if (itemsProcessed === array.length) {
              callback();
            }
          })
          .catch((error) => {
            // Handle any errors
          });
      });
    });
    function callback() {
      setImagesURLs([...tmp_imagesURLs]);
    }
  }

  async function fetchRecentPhotos() {
    const storage = getStorage();
    const listRef = ref(storage, "images/recent_photo");
    var tmp_recentPhotosURLs = [];
    var itemsProcessed = 0;

    await listAll(listRef).then((res) => {
      res.items.forEach(async (itemRef, index, array) => {
        // All the items under listRef.
        await getDownloadURL(itemRef)
          .then((url) => {
            itemsProcessed++;
            // `url` is the download URL for 'images/stars.jpg'
            tmp_recentPhotosURLs.push(url);
            if (itemsProcessed === array.length) {
              callback();
            }
          })
          .catch((error) => {
            // Handle any errors
          });
      });
    });
    function callback() {
      setRecentPhotosURLs([...tmp_recentPhotosURLs]);
    }
    setFetched(true);
  }

  async function fetchWhatsNews() {
    const q = query(collection(db, "WhatsNewsPost"));
    const tmpWhatsNews = [];
    const querySnapshot = await getDocs(q);
    querySnapshot.forEach((doc) => {
      tmpWhatsNews.push(doc.data());
    });
    setWhatsNews([...tmpWhatsNews]);
  }

 /* login page button
 <button
  className="MainFunctionCol"
  onClick={() => {
    window.location.assign("/e-logbook");
  }}
>
  <MedicalInformationIcon color="success" sx={{ fontSize: 40 }} />
  <h3><b>Login Preceptee Logbook</b></h3>
  <h3>登入 新入職同事培訓手冊</h3>
</button>*/

  return (
    <>
      {!fetched ? (
        <div id="SpinnerDiv">
          <Spinner animation="grow" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <Spinner animation="grow" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <Spinner animation="grow" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
        </div>
      ) : (
        <div className="App">
          <ResponsiveAppBar />
        
      
          <div className="TopJumbotron">
            <img src={TKOHGIF} alt="TKOH" />
            
            
            <div id="MainFunctionsRow">
            
                     
          

            
          </div>
          
          <div className="FooterCol" id="WhatsNews">
              <h3>What's New 最新消息</h3>
              <ul>
                {whatsNews.map((post, index) => {
                  return (
                    <li key={index}>
                      <a href={post.materialURL}>{post.title}</a>
                    </li>
                  );
                })}
              </ul>
            </div>
            </div>
          <div id="CarouselContainer">
            <LatestCarousel images_array={imagesURLs} interval={8000} />
          </div>
          
          <ButtomNav/>
          
         
            
          
         
          <div id="FooterSmall">
            <p>
              <FontAwesome name="copyright" size={14} color="gray" />
              Powered by TKOH NSD
              內容只供內部培訓用途
            </p>
      
          </div>
          
          
        </div>
        
       
      )}
    </>
  );
}
