# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@apideck/better-ajv-errors@^0.3.1":
  "integrity" "sha512-JdEazx7qiVqTBzzBl5rolRwl5cmhihjfIcpqRzIZjtT6b18liVmDn/VlWpqW4C/qP2hrFFMLRV1wlex8ZVBPTg=="
  "resolved" "https://registry.npmjs.org/@apideck/better-ajv-errors/-/better-ajv-errors-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "json-schema" "^0.4.0"
    "jsonpointer" "^5.0.0"
    "leven" "^3.1.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.16.0", "@babel/code-frame@^7.16.7", "@babel/code-frame@^7.8.3":
  "integrity" "sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/highlight" "^7.16.7"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.16.4", "@babel/compat-data@^7.16.8":
  "integrity" "sha512-m7OkX0IdKLKPpBlJtF561YJal5y/jyI5fNfWbPxh2D/nbzzGI4qRyrD8xO2jB24u7l+5I2a43scCG2IrfjC50Q=="
  "resolved" "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.16.8.tgz"
  "version" "7.16.8"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.1.0", "@babel/core@^7.11.1", "@babel/core@^7.12.0", "@babel/core@^7.12.3", "@babel/core@^7.13.0", "@babel/core@^7.16.0", "@babel/core@^7.4.0-0", "@babel/core@^7.7.2", "@babel/core@^7.8.0", "@babel/core@>=7.11.0":
  "integrity" "sha512-aeLaqcqThRNZYmbMqtulsetOQZ/5gbR/dWruUCJcpas4Qoyy+QeagfDsPdMrqwsPRDNxJvBlRiZxxX7THO7qtA=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.16.7"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helpers" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.1.2"
    "semver" "^6.3.0"
    "source-map" "^0.5.0"

"@babel/eslint-parser@^7.16.3":
  "integrity" "sha512-mUqYa46lgWqHKQ33Q6LNCGp/wPR3eqOYTUixHFsfrSQqRxH0+WOzca75iEjFr5RDGH1dDz622LaHhLOzOuQRUA=="
  "resolved" "https://registry.npmjs.org/@babel/eslint-parser/-/eslint-parser-7.16.5.tgz"
  "version" "7.16.5"
  dependencies:
    "eslint-scope" "^5.1.1"
    "eslint-visitor-keys" "^2.1.0"
    "semver" "^6.3.0"

"@babel/generator@^7.16.7", "@babel/generator@^7.16.8", "@babel/generator@^7.7.2":
  "integrity" "sha512-1ojZwE9+lOXzcWdWmO6TbUzDfqLD39CmEhN8+2cX9XkDo5yW1OpgfejfliysR2AWLpMamTiOiAp/mtroaymhpw=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/types" "^7.16.8"
    "jsesc" "^2.5.1"
    "source-map" "^0.5.0"

"@babel/helper-annotate-as-pure@^7.16.7":
  "integrity" "sha512-s6t2w/IPQVTAET1HitoowRGXooX8mCgtuP5195wD/QJPV6wYjpujCGF7JuMODVX2ZAJOf1GT6DT9MHEZvLOFSw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.16.7":
  "integrity" "sha512-C6FdbRaxYjwVu/geKW4ZeQ0Q31AftgRcdSnZ5/jsH6BzCJbtvXvhpfkbkThYSuutZA7nCXpPR6AD9zd1dprMkA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.7":
  "integrity" "sha512-mGojBwIWcwGD6rfqgRXVlVYmPAv7eOpIemUG3dGnDdCY4Pae70ROij3XmfrH6Fa1h1aiDylpglbZyktfzyo/hA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-validator-option" "^7.16.7"
    "browserslist" "^4.17.5"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.16.7":
  "integrity" "sha512-kIFozAvVfK05DM4EVQYKK+zteWvY85BFdGBRQBytRyY3y+6PX0DkDOn/CZ3lEuczCfrCxEzwt0YtP/87YPTWSw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"

"@babel/helper-create-regexp-features-plugin@^7.16.7":
  "integrity" "sha512-fk5A6ymfp+O5+p2yCkXAu5Kyj6v0xh0RBeNcAkYUMDvvAAoxvSKXn+Jb37t/yWFiQVDFK1ELpUTD8/aLhCPu+g=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "regexpu-core" "^4.7.1"

"@babel/helper-define-polyfill-provider@^0.3.1":
  "integrity" "sha512-J9hGMpJQmtWmj46B3kBHmL38UhJGhYX7eqkcq+2gsstyYt341HmPeWspihX43yVRA0mS+8GGk2Gckc7bY/HCmA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-environment-visitor@^7.16.7":
  "integrity" "sha512-SLLb0AAn6PkUeAfKJCCOl9e1R53pQlGAfc4y4XuMRZfqeMYLE0dM1LMhqbGAlGQY0lfw5/ohoYWAe9V1yibRag=="
  "resolved" "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-explode-assignable-expression@^7.16.7":
  "integrity" "sha512-KyUenhWMC8VrxzkGP0Jizjo4/Zx+1nNZhgocs+gLzyZyB8SHidhoq9KK/8Ato4anhwsivfkBLftky7gvzbZMtQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-function-name@^7.16.7":
  "integrity" "sha512-QfDfEnIUyyBSR3HtrtGECuZ6DAyCkYFp7GHl75vFtTnn6pjKeK0T1DB5lLkFvBea8MdaiUABx3osbgLyInoejA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-get-function-arity" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-get-function-arity@^7.16.7":
  "integrity" "sha512-flc+RLSOBXzNzVhcLu6ujeHUrD6tANAOU5ojrRx/as+tbzf8+stUCj7+IfRRoAbEZqj/ahXEMsjhOhgeZsrnTw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-hoist-variables@^7.16.7":
  "integrity" "sha512-m04d/0Op34H5v7pbZw6pSKP7weA6lsMvfiIAMeIvkY/R4xQtBSMFEigu9QTZ2qB/9l22vsxtM8a+Q8CzD255fg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-member-expression-to-functions@^7.16.7":
  "integrity" "sha512-VtJ/65tYiU/6AbMTDwyoXGPKHgTsfRarivm+YbB5uAzKUyuPjgZSgAFeG87FCigc7KNHu2Pegh1XIT3lXjvz3Q=="
  "resolved" "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-imports@^7.10.4", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.16.7":
  "integrity" "sha512-LVtS6TqjJHFc+nYeITRo6VLXve70xmq7wPhWTqDJusJEgGmkAACWwMiTNrvfoQo6hEhFwAIixNkvB0jPXDL8Wg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-transforms@^7.16.7":
  "integrity" "sha512-gaqtLDxJEFCeQbYp9aLAefjhkKdjKcdh6DB7jniIGU3Pz52WAmP268zK0VgPz9hUNkMSYeH976K2/Y6yPadpng=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-simple-access" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-optimise-call-expression@^7.16.7":
  "integrity" "sha512-EtgBhg7rd/JcnpZFXpBy0ze1YRfdm7BnBX4uKMBd3ixa3RGAE002JZB66FJyNH7g0F38U05pXmA5P8cBh7z+1w=="
  "resolved" "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha512-Qg3Nk7ZxpgMrsox6HreY1ZNKdBq7K72tDSliA6dCl5f007jR4ne8iD5UzuNnCJH2xBf2BEEVGr+/OL6Gdp7RxA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz"
  "version" "7.16.7"

"@babel/helper-remap-async-to-generator@^7.16.8":
  "integrity" "sha512-fm0gH7Flb8H51LqJHy3HJ3wnE1+qtYR2A99K06ahwrawLdOFsCEWjZOrYricXJHoPSudNKxrMBUPEIPxiIIvBw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-wrap-function" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helper-replace-supers@^7.16.7":
  "integrity" "sha512-y9vsWilTNaVnVh6xiJfABzsNpgDPKev9HnAgz6Gb1p6UUwf9NepdlsV7VXGCftJM+jqD5f7JIEubcpLjZj5dBw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-simple-access@^7.16.7":
  "integrity" "sha512-ZIzHVyoeLMvXMN/vok/a4LWRy8G2v205mNP0XOuf9XRLyX5/u9CnVulUtDgUTama3lT+bf/UqucuZjqiGuTS1g=="
  "resolved" "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-skip-transparent-expression-wrappers@^7.16.0":
  "integrity" "sha512-+il1gTy0oHwUsBQZyJvukbB4vPMdcYBrFHa0Uc4AizLxbq6BOYC51Rv4tWocX9BLBDLZ4kc6qUFpQ6HRgL+3zw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@^7.16.7":
  "integrity" "sha512-xbWoy/PFoxSWazIToT9Sif+jJTlrMcndIsaOKvTA6u7QEo7ilkRZpjew18/W3c7nm8fXdUDXh02VXTbZ0pGDNw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-validator-identifier@^7.16.7":
  "integrity" "sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz"
  "version" "7.16.7"

"@babel/helper-validator-option@^7.16.7":
  "integrity" "sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.16.7.tgz"
  "version" "7.16.7"

"@babel/helper-wrap-function@^7.16.8":
  "integrity" "sha512-8RpyRVIAW1RcDDGTA+GpPAwV22wXCfKOoM9bet6TLkGIFTkRQSkH1nMQ5Yet4MpoXe1ZwHPVtNasc2w0uZMqnw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-function-name" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helpers@^7.16.7":
  "integrity" "sha512-9ZDoqtfY7AuEOt3cxchfii6C7GDyyMBffktR5B2jvWv8u2+efwvpnVKXMWzNehqy68tKgAfSwfdw/lWpthS2bw=="
  "resolved" "https://registry.npmjs.org/@babel/helpers/-/helpers-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/highlight@^7.16.7":
  "integrity" "sha512-aKpPMfLvGO3Q97V0qhw/V2SWNWlwfJknuwAunU7wZLSfrM4xTBvg7E5opUVi1kJTBKihE38CPg4nBiqX83PWYw=="
  "resolved" "https://registry.npmjs.org/@babel/highlight/-/highlight-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.16.7", "@babel/parser@^7.16.8":
  "integrity" "sha512-i7jDUfrVBWc+7OKcBzEe5n7fbv3i2fWtxKzzCvOjnzSxMfWMigAhtfJ7qzZNGFNMsCCd67+uz553dYKWXPvCKw=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.16.8.tgz"
  "version" "7.16.8"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.16.7":
  "integrity" "sha512-anv/DObl7waiGEnC24O9zqL0pSuI9hljihqiDuFHC8d7/bjr/4RLGPWuc8rYOff/QPzbEPSkzG8wGG9aDuhHRg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.16.7":
  "integrity" "sha512-di8vUHRdf+4aJ7ltXhaDbPoszdkh59AQtJM5soLsuHpQJdFQZOA4uGj0V2u/CZ8bJ/u8ULDL5yq6FO/bCXnKHw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"

"@babel/plugin-proposal-async-generator-functions@^7.16.8":
  "integrity" "sha512-71YHIvMuiuqWJQkebWJtdhQTfd4Q4mF76q2IX37uZPkG9+olBxsX+rH1vkhFto4UeJZ9dPY2s+mDvhDm1u2BGQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.8"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.16.0", "@babel/plugin-proposal-class-properties@^7.16.7":
  "integrity" "sha512-IobU0Xme31ewjYOShSIqd/ZGM/r/cuOz2z0MDbNrhF5FW+ZVgi0f2lyeoj9KFPDOAqsYxmLWZte1WOwlvY9aww=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-class-static-block@^7.16.7":
  "integrity" "sha512-dgqJJrcZoG/4CkMopzhPJjGxsIe9A8RlkQLnL/Vhhx8AA9ZuaRwGSlscSh42hazc7WSrya/IK7mTeoF0DP9tEw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@^7.16.4":
  "integrity" "sha512-DoEpnuXK14XV9btI1k8tzNGCutMclpj4yru8aXKoHlVmbO1s+2A+g2+h4JhcjrxkFJqzbymnLG6j/niOf3iFXQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-decorators" "^7.16.7"

"@babel/plugin-proposal-dynamic-import@^7.16.7":
  "integrity" "sha512-I8SW9Ho3/8DRSdmDdH3gORdyUuYnk1m4cMxUAdu5oy4n3OfN8flDEH+d60iG7dUfi0KkYwSvoalHzzdRzpWHTg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.16.7":
  "integrity" "sha512-ZxdtqDXLRGBL64ocZcs7ovt71L3jhC1RGSyR996svrCi3PYqHNkb3SwPJCs8RIzD86s+WPpt2S73+EHCGO+NUA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.16.7":
  "integrity" "sha512-lNZ3EEggsGY78JavgbHsK9u5P3pQaW7k4axlgFLYkMd7UBsiNahCITShLjNQschPyjtO6dADrL24757IdhBrsQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.16.7":
  "integrity" "sha512-K3XzyZJGQCr00+EtYtrDjmwX7o7PLK6U9bi1nCwkQioRFVUv6dJoxbQjtWVtP+bCPy82bONBKG8NPyQ4+i6yjg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator@^7.16.7":
  "integrity" "sha512-aUOrYU3EVtjf62jQrCj63pYZ7k6vns2h/DQvHPWGmsJRYzWXZ6/AsfgpiRy6XiuIDADhJzP2Q9MwSMKauBQ+UQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.0", "@babel/plugin-proposal-numeric-separator@^7.16.7":
  "integrity" "sha512-vQgPMknOIgiuVqbokToyXbkY/OmmjAzr/0lhSIbG/KmnzXPGwW/AdhdKpi+O4X/VkWiWjnkKOBiqJrTaC98VKw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.16.7":
  "integrity" "sha512-3O0Y4+dw94HA86qSg9IHfyPktgR7q3gpNVAeiKQd+8jBKFaU5NQS1Yatgo4wY+UFNuLjvxcSmzcsHqrhgTyBUA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.16.7"

"@babel/plugin-proposal-optional-catch-binding@^7.16.7":
  "integrity" "sha512-eMOH/L4OvWSZAE1VkHbr1vckLG1WUcHGJSLqqQwl2GaUqG6QjddvrOaTUMNYiv77H5IKPMZ9U9P7EaHwvAShfA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.16.0", "@babel/plugin-proposal-optional-chaining@^7.16.7":
  "integrity" "sha512-eC3xy+ZrUcBtP7x+sq62Q/HYd674pPTb/77XZMb5wbDPGWIdUbSr4Agr052+zaUPSb+gGRnjxXfKFvx5iMJ+DA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.0", "@babel/plugin-proposal-private-methods@^7.16.7":
  "integrity" "sha512-7twV3pzhrRxSwHeIvFE6coPgvo+exNDOiGUMg39o2LiLo1Y+4aKpfkcLGcg1UHonzorCt7SNXnoMyCnnIOA8Sw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-private-property-in-object@^7.16.7":
  "integrity" "sha512-rMQkjcOFbm+ufe3bTZLyOfsOUOxyvLXZJCTARhJr+8UMSoZmqTe1K1BgkFcrW37rAchWg57yI69ORxiWvUINuQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.16.7", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha512-QRK0YI/40VLhNVGIjRNAAQkEHws0cswSdFFjpFyt943YmJIU1da9uW63Iu6NFV6CxTZW5eTDCrwZUstBWgp/Rg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  "integrity" "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13", "@babel/plugin-syntax-class-properties@^7.8.3":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.16.7":
  "integrity" "sha512-vQ+PxL+srA7g6Rx6I1e15m55gftknl2X8GCUW1JTlkTaXZLJOS0UcaY0eK9jYT7IYf4awn6qwyghVHLDz1WyMw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-flow@^7.14.5", "@babel/plugin-syntax-flow@^7.16.7":
  "integrity" "sha512-UDo3YGQO0jH6ytzVwgSLv9i/CzMcUjbKenL67dTrAZPPv6GFAtDhe6jqnvmoKzC/7htNTohhos+onPtDMqJwaQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-import-meta@^7.8.3":
  "integrity" "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.12.13", "@babel/plugin-syntax-jsx@^7.16.7":
  "integrity" "sha512-Esxmk7YjA8QysKeT3VhTXvF6y77f/a91SIs4pWb4H2eWGQkCKFgQaG6hdoEVZtGsrAcb2K5BW66XsOErD4WU3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4", "@babel/plugin-syntax-numeric-separator@^7.8.3":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5", "@babel/plugin-syntax-top-level-await@^7.8.3":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.16.7", "@babel/plugin-syntax-typescript@^7.7.2":
  "integrity" "sha512-YhUIJHHGkqPgEcMYkPCKTyGUdoGKWtopIycQyjJH8OjvRgOYsXsaKehLVPScKJWAULPxMa4N1vCe6szREFlZ7A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-arrow-functions@^7.16.7":
  "integrity" "sha512-9ffkFFMbvzTvv+7dTp/66xvZAWASuPD5Tl9LK3Z9vhOmANo6j94rik+5YMBt4CwHVMWLWpMsriIc2zsa3WW3xQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-async-to-generator@^7.16.8":
  "integrity" "sha512-MtmUmTJQHCnyJVrScNzNlofQJ3dLFuobYn3mwOTKHnSCMtbNsqvF71GQmJfFjdrXSsAA7iysFmYWw4bXZ20hOg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.8"

"@babel/plugin-transform-block-scoped-functions@^7.16.7":
  "integrity" "sha512-JUuzlzmF40Z9cXyytcbZEZKckgrQzChbQJw/5PuEHYeqzCsvebDx0K0jWnIIVcmmDOAVctCgnYs0pMcrYj2zJg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-block-scoping@^7.16.7":
  "integrity" "sha512-ObZev2nxVAYA4bhyusELdo9hb3H+A56bxH3FZMbEImZFiEDYVHXQSJ1hQKFlDnlt8G9bBrCZ5ZpURZUrV4G5qQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-classes@^7.16.7":
  "integrity" "sha512-WY7og38SFAGYRe64BrjKf8OrE6ulEHtr5jEYaZMwox9KebgqPi67Zqz8K53EKk1fFEJgm96r32rkKZ3qA2nCWQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.16.7":
  "integrity" "sha512-gN72G9bcmenVILj//sv1zLNaPyYcOzUho2lIJBMh/iakJ9ygCo/hEF9cpGb61SCMEDxbbyBoVQxrt+bWKu5KGw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-destructuring@^7.16.7":
  "integrity" "sha512-VqAwhTHBnu5xBVDCvrvqJbtLUa++qZaWC0Fgr2mqokBlulZARGyIvZDoqbPlPaKImQ9dKAcCzbv+ul//uqu70A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-dotall-regex@^7.16.7", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha512-Lyttaao2SjZF6Pf4vk1dVKv8YypMpomAbygW+mU5cYP3S5cWTfCJjG8xV6CFdzGFlfWK81IjL9viiTvpb6G7gQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-duplicate-keys@^7.16.7":
  "integrity" "sha512-03DvpbRfvWIXyK0/6QiR1KMTWeT6OcQ7tbhjrXyFS02kjuX/mu5Bvnh5SDSWHxyawit2g5aWhKwI86EE7GUnTw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-exponentiation-operator@^7.16.7":
  "integrity" "sha512-8UYLSlyLgRixQvlYH3J2ekXFHDFLQutdy7FfFAMm3CPZ6q9wHCwnUyiXpQCe3gVVnQlHc5nsuiEVziteRNTXEA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-flow-strip-types@^7.16.0":
  "integrity" "sha512-mzmCq3cNsDpZZu9FADYYyfZJIOrSONmHcop2XEKPdBNMa4PDC4eEvcOvzZaCNcjKu72v0XQlA5y1g58aLRXdYg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-flow" "^7.16.7"

"@babel/plugin-transform-for-of@^7.16.7":
  "integrity" "sha512-/QZm9W92Ptpw7sjI9Nx1mbcsWz33+l8kuMIQnDwgQBG5s3fAfQvkRjQ7NqXhtNcKOnPkdICmUHyCaWW06HCsqg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-function-name@^7.16.7":
  "integrity" "sha512-SU/C68YVwTRxqWj5kgsbKINakGag0KTgq9f2iZEXdStoAbOzLHEBRYzImmA6yFo8YZhJVflvXmIHUO7GWHmxxA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-literals@^7.16.7":
  "integrity" "sha512-6tH8RTpTWI0s2sV6uq3e/C9wPo4PTqqZps4uF0kzQ9/xPLFQtipynvmT1g/dOfEJ+0EQsHhkQ/zyRId8J2b8zQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-member-expression-literals@^7.16.7":
  "integrity" "sha512-mBruRMbktKQwbxaJof32LT9KLy2f3gH+27a5XSuXo6h7R3vqltl0PgZ80C8ZMKw98Bf8bqt6BEVi3svOh2PzMw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-modules-amd@^7.16.7":
  "integrity" "sha512-KaaEtgBL7FKYwjJ/teH63oAmE3lP34N3kshz8mm4VMAw7U3PxjVwwUmxEFksbgsNUaO3wId9R2AVQYSEGRa2+g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.16.8":
  "integrity" "sha512-oflKPvsLT2+uKQopesJt3ApiaIS2HW+hzHFcwRNtyDGieAeC/dIHZX8buJQ2J2X1rxGPy4eRcUijm3qcSPjYcA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-simple-access" "^7.16.7"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.16.7":
  "integrity" "sha512-DuK5E3k+QQmnOqBR9UkusByy5WZWGRxfzV529s9nPra1GE7olmxfqO2FHobEOYSPIjPBTr4p66YDcjQnt8cBmw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.16.7":
  "integrity" "sha512-EMh7uolsC8O4xhudF2F6wedbSHm1HHZ0C6aJ7K67zcDNidMzVcxWdGr+htW9n21klm+bOn+Rx4CBsAntZd3rEQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-named-capturing-groups-regex@^7.16.8":
  "integrity" "sha512-j3Jw+n5PvpmhRR+mrgIh04puSANCk/T/UA3m3P1MjJkhlK906+ApHhDIqBQDdOgL/r1UYpz4GNclTXxyZrYGSw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"

"@babel/plugin-transform-new-target@^7.16.7":
  "integrity" "sha512-xiLDzWNMfKoGOpc6t3U+etCE2yRnn3SM09BXqWPIZOBpL2gvVrBWUKnsJx0K/ADi5F5YC5f8APFfWrz25TdlGg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-object-super@^7.16.7":
  "integrity" "sha512-14J1feiQVWaGvRxj2WjyMuXS2jsBkgB3MdSN5HuC2G5nRspa5RK9COcs82Pwy5BuGcjb+fYaUj94mYcOj7rCvw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"

"@babel/plugin-transform-parameters@^7.16.7":
  "integrity" "sha512-AT3MufQ7zZEhU2hwOA11axBnExW0Lszu4RL/tAlUJBuNoRak+wehQW8h6KcXOcgjY42fHtDxswuMhMjFEuv/aw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-property-literals@^7.16.7":
  "integrity" "sha512-z4FGr9NMGdoIl1RqavCqGG+ZuYjfZ/hkCIeuH6Do7tXmSm0ls11nYVSJqFEUOSJbDab5wC6lRE/w6YjVcr6Hqw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-react-constant-elements@^7.12.1":
  "integrity" "sha512-lF+cfsyTgwWkcw715J88JhMYJ5GpysYNLhLP1PkvkhTRN7B3e74R/1KsDxFxhRpSn0UUD3IWM4GvdBR2PEbbQQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-constant-elements/-/plugin-transform-react-constant-elements-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-react-display-name@^7.16.0", "@babel/plugin-transform-react-display-name@^7.16.7":
  "integrity" "sha512-qgIg8BcZgd0G/Cz916D5+9kqX0c7nPZyXaP8R2tLNN5tkyIZdG5fEwBrxwplzSnjC1jvQmyMNVwUCZPcbGY7Pg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-react-jsx-development@^7.16.7":
  "integrity" "sha512-RMvQWvpla+xy6MlBpPlrKZCMRs2AGiHOGHY3xRwl0pEeim348dDyxeH4xBsMPbIMhujeq7ihE702eM2Ew0Wo+A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.16.7"

"@babel/plugin-transform-react-jsx@^7.14.9", "@babel/plugin-transform-react-jsx@^7.16.7":
  "integrity" "sha512-8D16ye66fxiE8m890w0BpPpngG9o9OVBBy0gH2E+2AR7qMR2ZpTYJEqLxAsoroenMId0p/wMW+Blc0meDgu0Ag=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-jsx" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/plugin-transform-react-pure-annotations@^7.16.7":
  "integrity" "sha512-hs71ToC97k3QWxswh2ElzMFABXHvGiJ01IB1TbYQDGeWRKWz/MPUTh5jGExdHvosYKpnJW5Pm3S4+TA3FyX+GA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-regenerator@^7.16.7":
  "integrity" "sha512-mF7jOgGYCkSJagJ6XCujSQg+6xC1M77/03K2oBmVJWoFGNUtnVJO4WHKJk3dnPC8HCcj4xBQP1Egm8DWh3Pb3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "regenerator-transform" "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.16.7":
  "integrity" "sha512-KQzzDnZ9hWQBjwi5lpY5v9shmm6IVG0U9pB18zvMu2i4H90xpT4gmqwPYsn8rObiadYe2M0gmgsiOIF5A/2rtg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-runtime@^7.16.4":
  "integrity" "sha512-6Kg2XHPFnIarNweZxmzbgYnnWsXxkx9WQUVk2sksBRL80lBC1RAQV3wQagWxdCHiYHqPN+oenwNIuttlYgIbQQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "babel-plugin-polyfill-corejs2" "^0.3.0"
    "babel-plugin-polyfill-corejs3" "^0.5.0"
    "babel-plugin-polyfill-regenerator" "^0.3.0"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.16.7":
  "integrity" "sha512-hah2+FEnoRoATdIb05IOXf+4GzXYTq75TVhIn1PewihbpyrNWUt2JbudKQOETWw6QpLe+AIUpJ5MVLYTQbeeUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-spread@^7.16.7":
  "integrity" "sha512-+pjJpgAngb53L0iaA5gU/1MLXJIfXcYepLgXB3esVRf4fqmj8f2cxM3/FKaHsZms08hFQJkFccEWuIpm429TXg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"

"@babel/plugin-transform-sticky-regex@^7.16.7":
  "integrity" "sha512-NJa0Bd/87QV5NZZzTuZG5BPJjLYadeSZ9fO6oOUoL4iQx+9EEuw/eEM92SrsT19Yc2jgB1u1hsjqDtH02c3Drw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-template-literals@^7.16.7":
  "integrity" "sha512-VwbkDDUeenlIjmfNeDX/V0aWrQH2QiVyJtwymVQSzItFDTpxfyJh3EVaQiS0rIN/CqbLGr0VcGmuwyTdZtdIsA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typeof-symbol@^7.16.7":
  "integrity" "sha512-p2rOixCKRJzpg9JB4gjnG4gjWkWa89ZoYUnl9snJ1cWIcTH/hvxZqfO+WjG6T8DRBpctEol5jw1O5rA8gkCokQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typescript@^7.16.7":
  "integrity" "sha512-bHdQ9k7YpBDO2d0NVfkj51DpQcvwIzIusJ7mEUaMlbZq3Kt/U47j24inXZHQ5MDiYpCs+oZiwnXyKedE8+q7AQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-typescript" "^7.16.7"

"@babel/plugin-transform-unicode-escapes@^7.16.7":
  "integrity" "sha512-TAV5IGahIz3yZ9/Hfv35TV2xEm+kaBDaZQCn2S/hG9/CZ0DktxJv9eKfPc7yYCvOYR4JGx1h8C+jcSOvgaaI/Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-unicode-regex@^7.16.7":
  "integrity" "sha512-oC5tYYKw56HO75KZVLQ+R/Nl3Hro9kf8iG0hXoaHP7tjAyCpvqBiSNe6vGrZni1Z6MggmUOC6A7VP7AVmw225Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/preset-env@^7.11.0", "@babel/preset-env@^7.12.1", "@babel/preset-env@^7.16.4":
  "integrity" "sha512-9rNKgVCdwHb3z1IlbMyft6yIXIeP3xz6vWvGaLHrJThuEIqWfHb0DNBH9VuTgnDfdbUDhkmkvMZS/YMCtP7Elg=="
  "resolved" "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/compat-data" "^7.16.8"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.16.7"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-async-generator-functions" "^7.16.8"
    "@babel/plugin-proposal-class-properties" "^7.16.7"
    "@babel/plugin-proposal-class-static-block" "^7.16.7"
    "@babel/plugin-proposal-dynamic-import" "^7.16.7"
    "@babel/plugin-proposal-export-namespace-from" "^7.16.7"
    "@babel/plugin-proposal-json-strings" "^7.16.7"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.16.7"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.7"
    "@babel/plugin-proposal-numeric-separator" "^7.16.7"
    "@babel/plugin-proposal-object-rest-spread" "^7.16.7"
    "@babel/plugin-proposal-optional-catch-binding" "^7.16.7"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-private-methods" "^7.16.7"
    "@babel/plugin-proposal-private-property-in-object" "^7.16.7"
    "@babel/plugin-proposal-unicode-property-regex" "^7.16.7"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.16.7"
    "@babel/plugin-transform-async-to-generator" "^7.16.8"
    "@babel/plugin-transform-block-scoped-functions" "^7.16.7"
    "@babel/plugin-transform-block-scoping" "^7.16.7"
    "@babel/plugin-transform-classes" "^7.16.7"
    "@babel/plugin-transform-computed-properties" "^7.16.7"
    "@babel/plugin-transform-destructuring" "^7.16.7"
    "@babel/plugin-transform-dotall-regex" "^7.16.7"
    "@babel/plugin-transform-duplicate-keys" "^7.16.7"
    "@babel/plugin-transform-exponentiation-operator" "^7.16.7"
    "@babel/plugin-transform-for-of" "^7.16.7"
    "@babel/plugin-transform-function-name" "^7.16.7"
    "@babel/plugin-transform-literals" "^7.16.7"
    "@babel/plugin-transform-member-expression-literals" "^7.16.7"
    "@babel/plugin-transform-modules-amd" "^7.16.7"
    "@babel/plugin-transform-modules-commonjs" "^7.16.8"
    "@babel/plugin-transform-modules-systemjs" "^7.16.7"
    "@babel/plugin-transform-modules-umd" "^7.16.7"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.16.8"
    "@babel/plugin-transform-new-target" "^7.16.7"
    "@babel/plugin-transform-object-super" "^7.16.7"
    "@babel/plugin-transform-parameters" "^7.16.7"
    "@babel/plugin-transform-property-literals" "^7.16.7"
    "@babel/plugin-transform-regenerator" "^7.16.7"
    "@babel/plugin-transform-reserved-words" "^7.16.7"
    "@babel/plugin-transform-shorthand-properties" "^7.16.7"
    "@babel/plugin-transform-spread" "^7.16.7"
    "@babel/plugin-transform-sticky-regex" "^7.16.7"
    "@babel/plugin-transform-template-literals" "^7.16.7"
    "@babel/plugin-transform-typeof-symbol" "^7.16.7"
    "@babel/plugin-transform-unicode-escapes" "^7.16.7"
    "@babel/plugin-transform-unicode-regex" "^7.16.7"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.16.8"
    "babel-plugin-polyfill-corejs2" "^0.3.0"
    "babel-plugin-polyfill-corejs3" "^0.5.0"
    "babel-plugin-polyfill-regenerator" "^0.3.0"
    "core-js-compat" "^3.20.2"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.5":
  "integrity" "sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA=="
  "resolved" "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/preset-react@^7.12.5", "@babel/preset-react@^7.16.0":
  "integrity" "sha512-fWpyI8UM/HE6DfPBzD8LnhQ/OcH8AgTaqcqP2nGOXEUV+VKBR5JRN9hCk9ai+zQQ57vtm9oWeXguBCPNUjytgA=="
  "resolved" "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-transform-react-display-name" "^7.16.7"
    "@babel/plugin-transform-react-jsx" "^7.16.7"
    "@babel/plugin-transform-react-jsx-development" "^7.16.7"
    "@babel/plugin-transform-react-pure-annotations" "^7.16.7"

"@babel/preset-typescript@^7.16.0":
  "integrity" "sha512-WbVEmgXdIyvzB77AQjGBEyYPZx+8tTsO50XtfozQrkW8QB2rLJpH2lgx0TRw5EJrBxOZQ+wCcyPVQvS8tjEHpQ=="
  "resolved" "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-transform-typescript" "^7.16.7"

"@babel/runtime-corejs3@^7.10.2":
  "integrity" "sha512-3fKhuICS1lMz0plI5ktOE/yEtBRMVxplzRkdn6mJQ197XiY0JnrzYV0+Mxozq3JZ8SBV9Ecurmw1XsGbwOf+Sg=="
  "resolved" "https://registry.npmjs.org/@babel/runtime-corejs3/-/runtime-corejs3-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "core-js-pure" "^3.20.2"
    "regenerator-runtime" "^0.13.4"

"@babel/runtime@^7.10.2", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.5", "@babel/runtime@^7.13.10", "@babel/runtime@^7.13.16", "@babel/runtime@^7.14.0", "@babel/runtime@^7.15.4", "@babel/runtime@^7.16.3", "@babel/runtime@^7.17.2", "@babel/runtime@^7.5.5", "@babel/runtime@^7.6.2", "@babel/runtime@^7.6.3", "@babel/runtime@^7.7.2", "@babel/runtime@^7.7.6", "@babel/runtime@^7.8.4", "@babel/runtime@^7.8.7", "@babel/runtime@^7.9.2":
  "integrity" "sha512-38Y8f7YUhce/K7RMwTp7m0uCumpv9hZkitCbBClqQIow1qSbCvGkcegKOXpEWCQLfWmevgRiWokZ1GkpfhbZug=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.18.3.tgz"
  "version" "7.18.3"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/template@^7.16.7", "@babel/template@^7.3.3":
  "integrity" "sha512-I8j/x8kHUrbYRTUxXrrMbfCa7jxkE7tZre39x3kjr9hvI82cK1FfqLygotcWN5kdPGWcLdWMHpSBavse5tWw3w=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/traverse@^7.13.0", "@babel/traverse@^7.16.7", "@babel/traverse@^7.16.8", "@babel/traverse@^7.7.2":
  "integrity" "sha512-xe+H7JlvKsDQwXRsBhSnq1/+9c+LlQcCK3Tn/l5sbx02HYns/cn7ibp9+RV1sIUqu7hKg91NWsgHurO9dowITQ=="
  "resolved" "https://registry.npmjs.org/@babel/traverse/-/traverse-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.16.8"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/parser" "^7.16.8"
    "@babel/types" "^7.16.8"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.12.6", "@babel/types@^7.16.0", "@babel/types@^7.16.7", "@babel/types@^7.16.8", "@babel/types@^7.3.0", "@babel/types@^7.3.3", "@babel/types@^7.4.4":
  "integrity" "sha512-smN2DQc5s4M7fntyjGtyIPbRJv6wW4rU/94fmYJ7PKQuZkC0qGMHXJbg6sNGt12JmVr4k5YaptI/XtiLJBnmIg=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.16.8.tgz"
  "version" "7.16.8"
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    "to-fast-properties" "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  "integrity" "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw=="
  "resolved" "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  "version" "0.2.3"

"@csstools/normalize.css@*":
  "integrity" "sha512-M0qqxAcwCsIVfpFQSlGN5XjXWu8l5JDZN+fPt1LeW5SZexQTgnaEvgXAY+CeygRw0EeppWHi12JxESWiWrB0Sg=="
  "resolved" "https://registry.npmjs.org/@csstools/normalize.css/-/normalize.css-12.0.0.tgz"
  "version" "12.0.0"

"@emotion/babel-plugin@^11.7.1":
  "integrity" "sha512-Pr/7HGH6H6yKgnVFNEj2MVlreu3ADqftqjqwUvDy/OJzKFgxKeTQ+eeUf20FOTuHVkDON2iNa25rAXVYtWJCjw=="
  "resolved" "https://registry.npmjs.org/@emotion/babel-plugin/-/babel-plugin-11.9.2.tgz"
  "version" "11.9.2"
  dependencies:
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/plugin-syntax-jsx" "^7.12.13"
    "@babel/runtime" "^7.13.10"
    "@emotion/hash" "^0.8.0"
    "@emotion/memoize" "^0.7.5"
    "@emotion/serialize" "^1.0.2"
    "babel-plugin-macros" "^2.6.1"
    "convert-source-map" "^1.5.0"
    "escape-string-regexp" "^4.0.0"
    "find-root" "^1.1.0"
    "source-map" "^0.5.7"
    "stylis" "4.0.13"

"@emotion/cache@^11.7.1":
  "integrity" "sha512-r65Zy4Iljb8oyjtLeCuBH8Qjiy107dOYC6SJq7g7GV5UCQWMObY4SJDPGFjiiVpPrOJ2hmJOoBiYTC7hwx9E2A=="
  "resolved" "https://registry.npmjs.org/@emotion/cache/-/cache-11.7.1.tgz"
  "version" "11.7.1"
  dependencies:
    "@emotion/memoize" "^0.7.4"
    "@emotion/sheet" "^1.1.0"
    "@emotion/utils" "^1.0.0"
    "@emotion/weak-memoize" "^0.2.5"
    "stylis" "4.0.13"

"@emotion/hash@^0.8.0":
  "integrity" "sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow=="
  "resolved" "https://registry.npmjs.org/@emotion/hash/-/hash-0.8.0.tgz"
  "version" "0.8.0"

"@emotion/is-prop-valid@^1.1.2":
  "integrity" "sha512-3QnhqeL+WW88YjYbQL5gUIkthuMw7a0NGbZ7wfFVk2kg/CK5w8w5FFa0RzWjyY1+sujN0NWbtSHH6OJmWHtJpQ=="
  "resolved" "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@emotion/memoize" "^0.7.4"

"@emotion/memoize@^0.7.4", "@emotion/memoize@^0.7.5":
  "integrity" "sha512-igX9a37DR2ZPGYtV6suZ6whr8pTFtyHL3K/oLUotxpSVO2ASaprmAe2Dkq7tBo7CRY7MMDrAa9nuQP9/YG8FxQ=="
  "resolved" "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.7.5.tgz"
  "version" "0.7.5"

"@emotion/react@^11.0.0-rc.0", "@emotion/react@^11.4.1", "@emotion/react@^11.5.0", "@emotion/react@^11.9.0":
  "integrity" "sha512-lBVSF5d0ceKtfKCDQJveNAtkC7ayxpVlgOohLgXqRwqWr9bOf4TZAFFyIcNngnV6xK6X4x2ZeXq7vliHkoVkxQ=="
  "resolved" "https://registry.npmjs.org/@emotion/react/-/react-11.9.0.tgz"
  "version" "11.9.0"
  dependencies:
    "@babel/runtime" "^7.13.10"
    "@emotion/babel-plugin" "^11.7.1"
    "@emotion/cache" "^11.7.1"
    "@emotion/serialize" "^1.0.3"
    "@emotion/utils" "^1.1.0"
    "@emotion/weak-memoize" "^0.2.5"
    "hoist-non-react-statics" "^3.3.1"

"@emotion/serialize@^1.0.2", "@emotion/serialize@^1.0.3":
  "integrity" "sha512-2mSSvgLfyV3q+iVh3YWgNlUc2a9ZlDU7DjuP5MjK3AXRR0dYigCrP99aeFtaB2L/hjfEZdSThn5dsZ0ufqbvsA=="
  "resolved" "https://registry.npmjs.org/@emotion/serialize/-/serialize-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "@emotion/hash" "^0.8.0"
    "@emotion/memoize" "^0.7.4"
    "@emotion/unitless" "^0.7.5"
    "@emotion/utils" "^1.0.0"
    "csstype" "^3.0.2"

"@emotion/sheet@^1.1.0":
  "integrity" "sha512-u0AX4aSo25sMAygCuQTzS+HsImZFuS8llY8O7b9MDRzbJM0kVJlAz6KNDqcG7pOuQZJmj/8X/rAW+66kMnMW+g=="
  "resolved" "https://registry.npmjs.org/@emotion/sheet/-/sheet-1.1.0.tgz"
  "version" "1.1.0"

"@emotion/styled@^11.3.0", "@emotion/styled@^11.8.1":
  "integrity" "sha512-OghEVAYBZMpEquHZwuelXcRjRJQOVayvbmNR0zr174NHdmMgrNkLC6TljKC5h9lZLkN5WGrdUcrKlOJ4phhoTQ=="
  "resolved" "https://registry.npmjs.org/@emotion/styled/-/styled-11.8.1.tgz"
  "version" "11.8.1"
  dependencies:
    "@babel/runtime" "^7.13.10"
    "@emotion/babel-plugin" "^11.7.1"
    "@emotion/is-prop-valid" "^1.1.2"
    "@emotion/serialize" "^1.0.2"
    "@emotion/utils" "^1.1.0"

"@emotion/unitless@^0.7.5":
  "integrity" "sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg=="
  "resolved" "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.7.5.tgz"
  "version" "0.7.5"

"@emotion/utils@^1.0.0", "@emotion/utils@^1.1.0":
  "integrity" "sha512-iRLa/Y4Rs5H/f2nimczYmS5kFJEbpiVvgN3XVfZ022IYhuNA1IRSHEizcof88LtCTXtl9S2Cxt32KgaXEu72JQ=="
  "resolved" "https://registry.npmjs.org/@emotion/utils/-/utils-1.1.0.tgz"
  "version" "1.1.0"

"@emotion/weak-memoize@^0.2.5":
  "integrity" "sha512-6U71C2Wp7r5XtFtQzYrW5iKFT67OixrSxjI4MptCHzdSVlgabczzqLe0ZSgnub/5Kp4hSbpDB1tMytZY9pwxxA=="
  "resolved" "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.2.5.tgz"
  "version" "0.2.5"

"@eslint/eslintrc@^1.0.5":
  "integrity" "sha512-BLxsnmK3KyPunz5wmCCpqy0YelEoxxGmH73Is+Z74oOTMtExcjkr3dDR6quwrjh1YspA8DH9gnX1o069KiS9AQ=="
  "resolved" "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.3.2"
    "espree" "^9.2.0"
    "globals" "^13.9.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "minimatch" "^3.0.4"
    "strip-json-comments" "^3.1.1"

"@firebase/analytics-compat@0.1.6":
  "integrity" "sha512-xvdp4/zwOG1f+v9JSpfCQoPJ98HcJR42cEnZ9pRIQLmUy7L7QceIuaF3m+zVtoqa4agBQnJ1dhe58FshOFKOPw=="
  "resolved" "https://registry.npmjs.org/@firebase/analytics-compat/-/analytics-compat-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "@firebase/analytics" "0.7.5"
    "@firebase/analytics-types" "0.7.0"
    "@firebase/component" "0.5.10"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/analytics-types@0.7.0":
  "integrity" "sha512-DNE2Waiwy5+zZnCfintkDtBfaW6MjIG883474v6Z0K1XZIvl76cLND4iv0YUb48leyF+PJK1KO2XrgHb/KpmhQ=="
  "resolved" "https://registry.npmjs.org/@firebase/analytics-types/-/analytics-types-0.7.0.tgz"
  "version" "0.7.0"

"@firebase/analytics@0.7.5":
  "integrity" "sha512-vrKDh84hBbKPJaU2oAZDewyC79D8opJOQZ5AU3BXBBwEfRjKt3C3jj/Vl6aJUme+RKXlomTw3xcHIOoPzTgBVA=="
  "resolved" "https://registry.npmjs.org/@firebase/analytics/-/analytics-0.7.5.tgz"
  "version" "0.7.5"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/installations" "0.5.5"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/app-check-compat@0.2.3":
  "integrity" "sha512-e2mKkuecr1XgsyTGXKfg83PcV1UdT7+tXYoHIjeBeLrP5gGL4OQbWCzzt6uVQpk1gmJbUktje/rd6Et6cdL+wg=="
  "resolved" "https://registry.npmjs.org/@firebase/app-check-compat/-/app-check-compat-0.2.3.tgz"
  "version" "0.2.3"
  dependencies:
    "@firebase/app-check" "0.5.3"
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/app-check-interop-types@0.1.0":
  "integrity" "sha512-uZfn9s4uuRsaX5Lwx+gFP3B6YsyOKUE+Rqa6z9ojT4VSRAsZFko9FRn6OxQUA1z5t5d08fY4pf+/+Dkd5wbdbA=="
  "resolved" "https://registry.npmjs.org/@firebase/app-check-interop-types/-/app-check-interop-types-0.1.0.tgz"
  "version" "0.1.0"

"@firebase/app-check@0.5.3":
  "integrity" "sha512-M2/UO5PgxHCl0wPYWGdF6lO8nqclwuRMCIrc+75xv3/Dr3hhUu4ztF5JNaAV5tktSCt1UrnASG+4rNVifCzSRw=="
  "resolved" "https://registry.npmjs.org/@firebase/app-check/-/app-check-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/app-compat@0.1.14", "@firebase/app-compat@0.x":
  "integrity" "sha512-CvT7/TdfWNRudrExAyWiPcMVtaqljE4mch/KfmfSz1mGmK0j/y1DN6PDJ+NZxkI+Za+YRkOI55H6DdIBsYQ0Qg=="
  "resolved" "https://registry.npmjs.org/@firebase/app-compat/-/app-compat-0.1.14.tgz"
  "version" "0.1.14"
  dependencies:
    "@firebase/app" "0.7.13"
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/app-types@0.7.0", "@firebase/app-types@0.x":
  "integrity" "sha512-6fbHQwDv2jp/v6bXhBw2eSRbNBpxHcd1NBF864UksSMVIqIyri9qpJB1Mn6sGZE+bnDsSQBC5j2TbMxYsJQkQg=="
  "resolved" "https://registry.npmjs.org/@firebase/app-types/-/app-types-0.7.0.tgz"
  "version" "0.7.0"

"@firebase/app@0.7.13", "@firebase/app@0.x":
  "integrity" "sha512-nMnz+lxASVZrWcAgLIgvs2QcsySjYvNpGjDeyhMzrbyBoBLgTux0cGWtm5RrJKx7arqueRpIihxcJtKAzCcIsw=="
  "resolved" "https://registry.npmjs.org/@firebase/app/-/app-0.7.13.tgz"
  "version" "0.7.13"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/auth-compat@0.2.5":
  "integrity" "sha512-Ft9PkmWOioxPMts6CMopN7sHpSXipQigOdm4BQ5HYTGHyLZpid2cj+2LxWsOYqQlhA1YBtzwE7sBRpV0W6bblQ=="
  "resolved" "https://registry.npmjs.org/@firebase/auth-compat/-/auth-compat-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "@firebase/auth" "0.19.5"
    "@firebase/auth-types" "0.11.0"
    "@firebase/component" "0.5.10"
    "@firebase/util" "1.4.3"
    "node-fetch" "2.6.5"
    "selenium-webdriver" "^4.0.0-beta.2"
    "tslib" "^2.1.0"

"@firebase/auth-interop-types@0.1.6":
  "integrity" "sha512-etIi92fW3CctsmR9e3sYM3Uqnoq861M0Id9mdOPF6PWIg38BXL5k4upCNBggGUpLIS0H1grMOvy/wn1xymwe2g=="
  "resolved" "https://registry.npmjs.org/@firebase/auth-interop-types/-/auth-interop-types-0.1.6.tgz"
  "version" "0.1.6"

"@firebase/auth-types@0.11.0":
  "integrity" "sha512-q7Bt6cx+ySj9elQHTsKulwk3+qDezhzRBFC9zlQ1BjgMueUOnGMcvqmU0zuKlQ4RhLSH7MNAdBV2znVaoN3Vxw=="
  "resolved" "https://registry.npmjs.org/@firebase/auth-types/-/auth-types-0.11.0.tgz"
  "version" "0.11.0"

"@firebase/auth@0.19.5":
  "integrity" "sha512-3+9XUnxaNb+ck6yULtEwOZbikWpL9KXuNLR34GxRv3mpOKD3uNbbONT149zMo3C6asI1bdv4+hCM78aS8VhZ0w=="
  "resolved" "https://registry.npmjs.org/@firebase/auth/-/auth-0.19.5.tgz"
  "version" "0.19.5"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "node-fetch" "2.6.5"
    "selenium-webdriver" "4.0.0-rc-1"
    "tslib" "^2.1.0"

"@firebase/component@0.5.10":
  "integrity" "sha512-mzUpg6rsBbdQJvAdu1rNWabU3O7qdd+B+/ubE1b+pTbBKfw5ySRpRRE6sKcZ/oQuwLh0HHB6FRJHcylmI7jDzA=="
  "resolved" "https://registry.npmjs.org/@firebase/component/-/component-0.5.10.tgz"
  "version" "0.5.10"
  dependencies:
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/database-compat@0.1.5":
  "integrity" "sha512-UVxkHL24sZfsjsjs+yiKIdYdrWXHrLxSFCYNdwNXDlTkAc0CWP9AAY3feLhBVpUKk+4Cj0I4sGnyIm2C1ltAYg=="
  "resolved" "https://registry.npmjs.org/@firebase/database-compat/-/database-compat-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/database" "0.12.5"
    "@firebase/database-types" "0.9.4"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/database-types@0.9.4":
  "integrity" "sha512-uAQuc6NUZ5Oh/cWZPeMValtcZ+4L1stgKOeYvz7mLn8+s03tnCDL2N47OLCHdntktVkhImQTwGNARgqhIhtNeA=="
  "resolved" "https://registry.npmjs.org/@firebase/database-types/-/database-types-0.9.4.tgz"
  "version" "0.9.4"
  dependencies:
    "@firebase/app-types" "0.7.0"
    "@firebase/util" "1.4.3"

"@firebase/database@0.12.5":
  "integrity" "sha512-1Pd2jYqvqZI7SQWAiXbTZxmsOa29PyOaPiUtr8pkLSfLp4AeyMBegYAXCLYLW6BNhKn3zNKFkxYDxYHq4q+Ixg=="
  "resolved" "https://registry.npmjs.org/@firebase/database/-/database-0.12.5.tgz"
  "version" "0.12.5"
  dependencies:
    "@firebase/auth-interop-types" "0.1.6"
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "faye-websocket" "0.11.4"
    "tslib" "^2.1.0"

"@firebase/firestore-compat@0.1.12":
  "integrity" "sha512-+8FwiYctRc5Vwa59iGD6IdTNCKqgZYB6yl/PvDJfi+WNhJbMznpHYWBI+urNGHAXBpHRDCwJS08LVsVTsBsS0w=="
  "resolved" "https://registry.npmjs.org/@firebase/firestore-compat/-/firestore-compat-0.1.12.tgz"
  "version" "0.1.12"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/firestore" "3.4.3"
    "@firebase/firestore-types" "2.5.0"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/firestore-types@2.5.0":
  "integrity" "sha512-I6c2m1zUhZ5SH0cWPmINabDyH5w0PPFHk2UHsjBpKdZllzJZ2TwTkXbDtpHUZNmnc/zAa0WNMNMvcvbb/xJLKA=="
  "resolved" "https://registry.npmjs.org/@firebase/firestore-types/-/firestore-types-2.5.0.tgz"
  "version" "2.5.0"

"@firebase/firestore@3.4.3":
  "integrity" "sha512-mUZY/aTKpliCyoYs7/64olumeTbM42axu2u8QDl28AX+4q7vHGIiks9+H2gaqz/zgWODXiQeBmJlHCb1RlJGhQ=="
  "resolved" "https://registry.npmjs.org/@firebase/firestore/-/firestore-3.4.3.tgz"
  "version" "3.4.3"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "@firebase/webchannel-wrapper" "0.6.1"
    "@grpc/grpc-js" "^1.3.2"
    "@grpc/proto-loader" "^0.6.0"
    "node-fetch" "2.6.5"
    "tslib" "^2.1.0"

"@firebase/functions-compat@0.1.8":
  "integrity" "sha512-9nB6uPzSbnzOE+V7USbHsQxze/xeJC5WTgBOhyHA8eEU/z5mBGfD1eV31QbI7mbSFL8m4N8F5cidDw3zB1G/Jw=="
  "resolved" "https://registry.npmjs.org/@firebase/functions-compat/-/functions-compat-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/functions" "0.7.7"
    "@firebase/functions-types" "0.5.0"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/functions-types@0.5.0":
  "integrity" "sha512-qza0M5EwX+Ocrl1cYI14zoipUX4gI/Shwqv0C1nB864INAD42Dgv4v94BCyxGHBg2kzlWy8PNafdP7zPO8aJQA=="
  "resolved" "https://registry.npmjs.org/@firebase/functions-types/-/functions-types-0.5.0.tgz"
  "version" "0.5.0"

"@firebase/functions@0.7.7":
  "integrity" "sha512-e944UigvrqwGHODww8QU1oaZ+KFdqcf/hmf5L2vEakQEIOjCRy6Kal8xAlYpaP4QbC1DEUfY4qC9QoFUErI2fQ=="
  "resolved" "https://registry.npmjs.org/@firebase/functions/-/functions-0.7.7.tgz"
  "version" "0.7.7"
  dependencies:
    "@firebase/app-check-interop-types" "0.1.0"
    "@firebase/auth-interop-types" "0.1.6"
    "@firebase/component" "0.5.10"
    "@firebase/messaging-interop-types" "0.1.0"
    "@firebase/util" "1.4.3"
    "node-fetch" "2.6.5"
    "tslib" "^2.1.0"

"@firebase/installations@0.5.5":
  "integrity" "sha512-mYWUxYXPlxcR0YOikPw88TjIS2NK35Z0ivkJL0+FevNnVIsqwGSe12AtPlZB/kzjB0RtHoKW+cWC0V9xiTgJ3Q=="
  "resolved" "https://registry.npmjs.org/@firebase/installations/-/installations-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/util" "1.4.3"
    "idb" "3.0.2"
    "tslib" "^2.1.0"

"@firebase/logger@0.3.2":
  "integrity" "sha512-lzLrcJp9QBWpo40OcOM9B8QEtBw2Fk1zOZQdvv+rWS6gKmhQBCEMc4SMABQfWdjsylBcDfniD1Q+fUX1dcBTXA=="
  "resolved" "https://registry.npmjs.org/@firebase/logger/-/logger-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "tslib" "^2.1.0"

"@firebase/messaging-compat@0.1.6":
  "integrity" "sha512-VzNM5ew8YAH7tzyukY0QqrCKdmaIe1FsWJSNPWcfzMNri8mpfKALIjeFzle+6DrRWZweFsp8ejvcvvulIDILGw=="
  "resolved" "https://registry.npmjs.org/@firebase/messaging-compat/-/messaging-compat-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/messaging" "0.9.6"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/messaging-interop-types@0.1.0":
  "integrity" "sha512-DbvUl/rXAZpQeKBnwz0NYY5OCqr2nFA0Bj28Fmr3NXGqR4PAkfTOHuQlVtLO1Nudo3q0HxAYLa68ZDAcuv2uKQ=="
  "resolved" "https://registry.npmjs.org/@firebase/messaging-interop-types/-/messaging-interop-types-0.1.0.tgz"
  "version" "0.1.0"

"@firebase/messaging@0.9.6":
  "integrity" "sha512-weDGzgU0MNtC6FCFJu/AW+pXbuX/YasHqR42NcLyoHNL8EgjXLPC0EYeMi7B8dY7MCsbc5lbPtqiveOP97L1jQ=="
  "resolved" "https://registry.npmjs.org/@firebase/messaging/-/messaging-0.9.6.tgz"
  "version" "0.9.6"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/installations" "0.5.5"
    "@firebase/messaging-interop-types" "0.1.0"
    "@firebase/util" "1.4.3"
    "idb" "3.0.2"
    "tslib" "^2.1.0"

"@firebase/performance-compat@0.1.5":
  "integrity" "sha512-s9mqR0GXJaqvIZD/GsshacpKOGa3NP6Yht33mNEtpL7ERqj35mvD1CBoUwH52eMYAaxlQd9y9JrphQgK3EmWWw=="
  "resolved" "https://registry.npmjs.org/@firebase/performance-compat/-/performance-compat-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/performance" "0.5.5"
    "@firebase/performance-types" "0.1.0"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/performance-types@0.1.0":
  "integrity" "sha512-6p1HxrH0mpx+622Ql6fcxFxfkYSBpE3LSuwM7iTtYU2nw91Hj6THC8Bc8z4nboIq7WvgsT/kOTYVVZzCSlXl8w=="
  "resolved" "https://registry.npmjs.org/@firebase/performance-types/-/performance-types-0.1.0.tgz"
  "version" "0.1.0"

"@firebase/performance@0.5.5":
  "integrity" "sha512-eA8mEKVnyY64fwAKxHbJF5t1hNkdR0EZVib0LfEWl/2elPmFcjik097hqLHzdFE88JYCxNGfFaSPo9Lbk/qe6A=="
  "resolved" "https://registry.npmjs.org/@firebase/performance/-/performance-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/installations" "0.5.5"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/polyfill@0.3.36":
  "integrity" "sha512-zMM9oSJgY6cT2jx3Ce9LYqb0eIpDE52meIzd/oe/y70F+v9u1LDqk5kUF5mf16zovGBWMNFmgzlsh6Wj0OsFtg=="
  "resolved" "https://registry.npmjs.org/@firebase/polyfill/-/polyfill-0.3.36.tgz"
  "version" "0.3.36"
  dependencies:
    "core-js" "3.6.5"
    "promise-polyfill" "8.1.3"
    "whatwg-fetch" "2.0.4"

"@firebase/remote-config-compat@0.1.5":
  "integrity" "sha512-bgpmrCGyOj46c0xNFvivcXRHlaVkbt4mX2etbF9s6jaOILPd4rBHIfAiBpKL64GGwTkrOjWO9/HZun4I01gbpg=="
  "resolved" "https://registry.npmjs.org/@firebase/remote-config-compat/-/remote-config-compat-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/logger" "0.3.2"
    "@firebase/remote-config" "0.3.4"
    "@firebase/remote-config-types" "0.2.0"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/remote-config-types@0.2.0":
  "integrity" "sha512-hqK5sCPeZvcHQ1D6VjJZdW6EexLTXNMJfPdTwbD8NrXUw6UjWC4KWhLK/TSlL0QPsQtcKRkaaoP+9QCgKfMFPw=="
  "resolved" "https://registry.npmjs.org/@firebase/remote-config-types/-/remote-config-types-0.2.0.tgz"
  "version" "0.2.0"

"@firebase/remote-config@0.3.4":
  "integrity" "sha512-SLlyVVNJ6DnU1AOjNrmv5u9Fge7gUwZVooyxMIkaT3Lj9MBM5MwfJsoG3UyiV4l7yI0iPj34LuKPpMJXOOcs4w=="
  "resolved" "https://registry.npmjs.org/@firebase/remote-config/-/remote-config-0.3.4.tgz"
  "version" "0.3.4"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/installations" "0.5.5"
    "@firebase/logger" "0.3.2"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/storage-compat@0.1.9":
  "integrity" "sha512-FwSNw1FMH8Qk9l+nDmlamesEFVjOfmWO4B2BV4l3YRn5ibvxIvBqRQZP8TGUknHCWKM1b7dMq3C19cVxeJ77VQ=="
  "resolved" "https://registry.npmjs.org/@firebase/storage-compat/-/storage-compat-0.1.9.tgz"
  "version" "0.1.9"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/storage" "0.9.1"
    "@firebase/storage-types" "0.6.0"
    "@firebase/util" "1.4.3"
    "tslib" "^2.1.0"

"@firebase/storage-types@0.6.0":
  "integrity" "sha512-1LpWhcCb1ftpkP/akhzjzeFxgVefs6eMD2QeKiJJUGH1qOiows2w5o0sKCUSQrvrRQS1lz3SFGvNR1Ck/gqxeA=="
  "resolved" "https://registry.npmjs.org/@firebase/storage-types/-/storage-types-0.6.0.tgz"
  "version" "0.6.0"

"@firebase/storage@0.9.1":
  "integrity" "sha512-IMPZ21Mm05R9GKTgiiMpbata0tgzQTtZ2YMbVReSTx16GJTIpadXpjFzxhJMjVi/7Wq57LnSxsg9fe56IBSacw=="
  "resolved" "https://registry.npmjs.org/@firebase/storage/-/storage-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "@firebase/component" "0.5.10"
    "@firebase/util" "1.4.3"
    "node-fetch" "2.6.5"
    "tslib" "^2.1.0"

"@firebase/util@1.4.3", "@firebase/util@1.x":
  "integrity" "sha512-gQJl6r0a+MElLQEyU8Dx0kkC2coPj67f/zKZrGR7z7WpLgVanhaCUqEsptwpwoxi9RMFIaebleG+C9xxoARq+Q=="
  "resolved" "https://registry.npmjs.org/@firebase/util/-/util-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "tslib" "^2.1.0"

"@firebase/webchannel-wrapper@0.6.1":
  "integrity" "sha512-9FqhNjKQWpQ3fGnSOCovHOm+yhhiorKEqYLAfd525jWavunDJcx8rOW6i6ozAh+FbwcYMkL7b+3j4UR/30MpoQ=="
  "resolved" "https://registry.npmjs.org/@firebase/webchannel-wrapper/-/webchannel-wrapper-0.6.1.tgz"
  "version" "0.6.1"

"@grpc/grpc-js@^1.3.2":
  "integrity" "sha512-ItOqQ4ff7JrR9W6KDQm+LdsVjuZtV7Qq64Oy3Hjx8ZPBDDwBx7rD8hOL0Vnde0RbnsqLG86WOgF+tQDzf/nSzQ=="
  "resolved" "https://registry.npmjs.org/@grpc/grpc-js/-/grpc-js-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "@grpc/proto-loader" "^0.6.4"
    "@types/node" ">=12.12.47"

"@grpc/proto-loader@^0.6.0", "@grpc/proto-loader@^0.6.4":
  "integrity" "sha512-UlcCS8VbsU9d3XTXGiEVFonN7hXk+oMXZtoHHG2oSA1/GcDP1q6OUgs20PzHDGizzyi8ufGSUDlk3O2NyY7leg=="
  "resolved" "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.6.9.tgz"
  "version" "0.6.9"
  dependencies:
    "@types/long" "^4.0.1"
    "lodash.camelcase" "^4.3.0"
    "long" "^4.0.0"
    "protobufjs" "^6.10.0"
    "yargs" "^16.2.0"

"@humanwhocodes/config-array@^0.9.2":
  "integrity" "sha512-UXOuFCGcwciWckOpmfKDq/GyhlTf9pN/BzG//x8p8zTOFEcGuA68ANXheFS0AGvy3qgZqLBUkMs7hqzqCKOVwA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.9.2.tgz"
  "version" "0.9.2"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.1"
    "debug" "^4.1.1"
    "minimatch" "^3.0.4"

"@humanwhocodes/object-schema@^1.2.1":
  "integrity" "sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz"
  "version" "1.2.1"

"@istanbuljs/load-nyc-config@^1.0.0":
  "integrity" "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "camelcase" "^5.3.1"
    "find-up" "^4.1.0"
    "get-package-type" "^0.1.0"
    "js-yaml" "^3.13.1"
    "resolve-from" "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  "integrity" "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz"
  "version" "0.1.3"

"@jest/console@^27.4.6":
  "integrity" "sha512-jauXyacQD33n47A44KrlOVeiXHEXDqapSdfb9kTekOchH/Pd18kBIO1+xxJQRLuG+LUuljFCwTG92ra4NW7SpA=="
  "resolved" "https://registry.npmjs.org/@jest/console/-/console-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "jest-message-util" "^27.4.6"
    "jest-util" "^27.4.2"
    "slash" "^3.0.0"

"@jest/core@^27.4.7":
  "integrity" "sha512-n181PurSJkVMS+kClIFSX/LLvw9ExSb+4IMtD6YnfxZVerw9ANYtW0bPrm0MJu2pfe9SY9FJ9FtQ+MdZkrZwjg=="
  "resolved" "https://registry.npmjs.org/@jest/core/-/core-27.4.7.tgz"
  "version" "27.4.7"
  dependencies:
    "@jest/console" "^27.4.6"
    "@jest/reporters" "^27.4.6"
    "@jest/test-result" "^27.4.6"
    "@jest/transform" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.0.0"
    "emittery" "^0.8.1"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-changed-files" "^27.4.2"
    "jest-config" "^27.4.7"
    "jest-haste-map" "^27.4.6"
    "jest-message-util" "^27.4.6"
    "jest-regex-util" "^27.4.0"
    "jest-resolve" "^27.4.6"
    "jest-resolve-dependencies" "^27.4.6"
    "jest-runner" "^27.4.6"
    "jest-runtime" "^27.4.6"
    "jest-snapshot" "^27.4.6"
    "jest-util" "^27.4.2"
    "jest-validate" "^27.4.6"
    "jest-watcher" "^27.4.6"
    "micromatch" "^4.0.4"
    "rimraf" "^3.0.0"
    "slash" "^3.0.0"
    "strip-ansi" "^6.0.0"

"@jest/environment@^27.4.6":
  "integrity" "sha512-E6t+RXPfATEEGVidr84WngLNWZ8ffCPky8RqqRK6u1Bn0LK92INe0MDttyPl/JOzaq92BmDzOeuqk09TvM22Sg=="
  "resolved" "https://registry.npmjs.org/@jest/environment/-/environment-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/fake-timers" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "jest-mock" "^27.4.6"

"@jest/fake-timers@^27.4.6":
  "integrity" "sha512-mfaethuYF8scV8ntPpiVGIHQgS0XIALbpY2jt2l7wb/bvq4Q5pDLk4EP4D7SAvYT1QrPOPVZAtbdGAOOyIgs7A=="
  "resolved" "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "@sinonjs/fake-timers" "^8.0.1"
    "@types/node" "*"
    "jest-message-util" "^27.4.6"
    "jest-mock" "^27.4.6"
    "jest-util" "^27.4.2"

"@jest/globals@^27.4.6":
  "integrity" "sha512-kAiwMGZ7UxrgPzu8Yv9uvWmXXxsy0GciNejlHvfPIfWkSxChzv6bgTS3YqBkGuHcis+ouMFI2696n2t+XYIeFw=="
  "resolved" "https://registry.npmjs.org/@jest/globals/-/globals-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/environment" "^27.4.6"
    "@jest/types" "^27.4.2"
    "expect" "^27.4.6"

"@jest/reporters@^27.4.6":
  "integrity" "sha512-+Zo9gV81R14+PSq4wzee4GC2mhAN9i9a7qgJWL90Gpx7fHYkWpTBvwWNZUXvJByYR9tAVBdc8VxDWqfJyIUrIQ=="
  "resolved" "https://registry.npmjs.org/@jest/reporters/-/reporters-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^27.4.6"
    "@jest/test-result" "^27.4.6"
    "@jest/transform" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "collect-v8-coverage" "^1.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.2"
    "graceful-fs" "^4.2.4"
    "istanbul-lib-coverage" "^3.0.0"
    "istanbul-lib-instrument" "^5.1.0"
    "istanbul-lib-report" "^3.0.0"
    "istanbul-lib-source-maps" "^4.0.0"
    "istanbul-reports" "^3.1.3"
    "jest-haste-map" "^27.4.6"
    "jest-resolve" "^27.4.6"
    "jest-util" "^27.4.2"
    "jest-worker" "^27.4.6"
    "slash" "^3.0.0"
    "source-map" "^0.6.0"
    "string-length" "^4.0.1"
    "terminal-link" "^2.0.0"
    "v8-to-istanbul" "^8.1.0"

"@jest/source-map@^27.4.0":
  "integrity" "sha512-Ntjx9jzP26Bvhbm93z/AKcPRj/9wrkI88/gK60glXDx1q+IeI0rf7Lw2c89Ch6ofonB0On/iRDreQuQ6te9pgQ=="
  "resolved" "https://registry.npmjs.org/@jest/source-map/-/source-map-27.4.0.tgz"
  "version" "27.4.0"
  dependencies:
    "callsites" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "source-map" "^0.6.0"

"@jest/test-result@^27.4.6":
  "integrity" "sha512-fi9IGj3fkOrlMmhQqa/t9xum8jaJOOAi/lZlm6JXSc55rJMXKHxNDN1oCP39B0/DhNOa2OMupF9BcKZnNtXMOQ=="
  "resolved" "https://registry.npmjs.org/@jest/test-result/-/test-result-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/console" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "collect-v8-coverage" "^1.0.0"

"@jest/test-sequencer@^27.4.6":
  "integrity" "sha512-3GL+nsf6E1PsyNsJuvPyIz+DwFuCtBdtvPpm/LMXVkBJbdFvQYCDpccYT56qq5BGniXWlE81n2qk1sdXfZebnw=="
  "resolved" "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/test-result" "^27.4.6"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^27.4.6"
    "jest-runtime" "^27.4.6"

"@jest/transform@^27.4.6":
  "integrity" "sha512-9MsufmJC8t5JTpWEQJ0OcOOAXaH5ioaIX6uHVBLBMoCZPfKKQF+EqP8kACAvCZ0Y1h2Zr3uOccg8re+Dr5jxyw=="
  "resolved" "https://registry.npmjs.org/@jest/transform/-/transform-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^27.4.2"
    "babel-plugin-istanbul" "^6.1.1"
    "chalk" "^4.0.0"
    "convert-source-map" "^1.4.0"
    "fast-json-stable-stringify" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^27.4.6"
    "jest-regex-util" "^27.4.0"
    "jest-util" "^27.4.2"
    "micromatch" "^4.0.4"
    "pirates" "^4.0.4"
    "slash" "^3.0.0"
    "source-map" "^0.6.1"
    "write-file-atomic" "^3.0.0"

"@jest/types@^27.4.2":
  "integrity" "sha512-j35yw0PMTPpZsUoOBiuHzr1zTYoad1cVIE0ajEjcrJONxxrko/IRGKkXx3os0Nsi4Hu3+5VmDbVfq5WhG/pWAg=="
  "resolved" "https://registry.npmjs.org/@jest/types/-/types-27.4.2.tgz"
  "version" "27.4.2"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^16.0.0"
    "chalk" "^4.0.0"

"@mui/base@5.0.0-alpha.83":
  "integrity" "sha512-/bFcjiI36R2Epf2Y3BkZOIdxrz5uMLqOU4cRai4igJ8DHTRMZDeKbOff0SdvwJNwg8r6oPUyoeOpsWkaOOX9/g=="
  "resolved" "https://registry.npmjs.org/@mui/base/-/base-5.0.0-alpha.83.tgz"
  "version" "5.0.0-alpha.83"
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@emotion/is-prop-valid" "^1.1.2"
    "@mui/types" "^7.1.3"
    "@mui/utils" "^5.8.0"
    "@popperjs/core" "^2.11.5"
    "clsx" "^1.1.1"
    "prop-types" "^15.8.1"
    "react-is" "^17.0.2"

"@mui/icons-material@^5.8.2":
  "integrity" "sha512-fP6KUCCZZjc2rdbMSmkNmBHDskLkmP0uCox57cbVXvomU6BOPrCxnr5YXsSsQrZB8fchx7hfH0bkAgvMZ5KM0Q=="
  "resolved" "https://registry.npmjs.org/@mui/icons-material/-/icons-material-5.8.2.tgz"
  "version" "5.8.2"
  dependencies:
    "@babel/runtime" "^7.17.2"

"@mui/material@^5.0.0", "@mui/material@^5.8.2":
  "integrity" "sha512-w/A1KG9Czf42uTyJOiRU5U1VullOz1R3xcsBvv3BtKCCWdVP+D6v/Yb8v0tJpIixMEbjeWzWGjotQBU0nd+yNA=="
  "resolved" "https://registry.npmjs.org/@mui/material/-/material-5.8.2.tgz"
  "version" "5.8.2"
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@mui/base" "5.0.0-alpha.83"
    "@mui/system" "^5.8.2"
    "@mui/types" "^7.1.3"
    "@mui/utils" "^5.8.0"
    "@types/react-transition-group" "^4.4.4"
    "clsx" "^1.1.1"
    "csstype" "^3.1.0"
    "hoist-non-react-statics" "^3.3.2"
    "prop-types" "^15.8.1"
    "react-is" "^17.0.2"
    "react-transition-group" "^4.4.2"

"@mui/private-theming@^5.8.0":
  "integrity" "sha512-MjRAneTmCKLR9u2S4jtjLUe6gpHxlbb4g2bqpDJ2PdwlvwsWIUzbc/gVB4dvccljXeWxr5G2M/Co2blXisvFIw=="
  "resolved" "https://registry.npmjs.org/@mui/private-theming/-/private-theming-5.8.0.tgz"
  "version" "5.8.0"
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@mui/utils" "^5.8.0"
    "prop-types" "^15.8.1"

"@mui/styled-engine@^5.8.0":
  "integrity" "sha512-Q3spibB8/EgeMYHc+/o3RRTnAYkSl7ROCLhXJ830W8HZ2/iDiyYp16UcxKPurkXvLhUaILyofPVrP3Su2uKsAw=="
  "resolved" "https://registry.npmjs.org/@mui/styled-engine/-/styled-engine-5.8.0.tgz"
  "version" "5.8.0"
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@emotion/cache" "^11.7.1"
    "prop-types" "^15.8.1"

"@mui/system@^5.8.2":
  "integrity" "sha512-N74gDNKM+MnWvKTMmCPvCVLH4f0ZzakP1bcMDaPctrHwcyxNcEmtTGNpIiVk0Iu7vtThZAFL3DjHpINPGF7+cg=="
  "resolved" "https://registry.npmjs.org/@mui/system/-/system-5.8.2.tgz"
  "version" "5.8.2"
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@mui/private-theming" "^5.8.0"
    "@mui/styled-engine" "^5.8.0"
    "@mui/types" "^7.1.3"
    "@mui/utils" "^5.8.0"
    "clsx" "^1.1.1"
    "csstype" "^3.1.0"
    "prop-types" "^15.8.1"

"@mui/types@^7.1.3":
  "integrity" "sha512-DDF0UhMBo4Uezlk+6QxrlDbchF79XG6Zs0zIewlR4c0Dt6GKVFfUtzPtHCH1tTbcSlq/L2bGEdiaoHBJ9Y1gSA=="
  "resolved" "https://registry.npmjs.org/@mui/types/-/types-7.1.3.tgz"
  "version" "7.1.3"

"@mui/utils@^5.8.0":
  "integrity" "sha512-7LgUtCvz78676iC0wpTH7HizMdCrTphhBmRWimIMFrp5Ph6JbDFVuKS1CwYnWWxRyYKL0QzXrDL0lptAU90EXg=="
  "resolved" "https://registry.npmjs.org/@mui/utils/-/utils-5.8.0.tgz"
  "version" "5.8.0"
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@types/prop-types" "^15.7.5"
    "@types/react-is" "^16.7.1 || ^17.0.0"
    "prop-types" "^15.8.1"
    "react-is" "^17.0.2"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@pmmmwh/react-refresh-webpack-plugin@^0.5.3":
  "integrity" "sha512-zZbZeHQDnoTlt2AF+diQT0wsSXpvWiaIOZwBRdltNFhG1+I3ozyaw7U/nBiUwyJ0D+zwdXp0E3bWOl38Ag2BMw=="
  "resolved" "https://registry.npmjs.org/@pmmmwh/react-refresh-webpack-plugin/-/react-refresh-webpack-plugin-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "ansi-html-community" "^0.0.8"
    "common-path-prefix" "^3.0.0"
    "core-js-pure" "^3.8.1"
    "error-stack-parser" "^2.0.6"
    "find-up" "^5.0.0"
    "html-entities" "^2.1.0"
    "loader-utils" "^2.0.0"
    "schema-utils" "^3.0.0"
    "source-map" "^0.7.3"

"@popperjs/core@^2.10.1", "@popperjs/core@^2.10.2", "@popperjs/core@^2.11.5":
  "integrity" "sha512-9X2obfABZuDVLCgPK9aX0a/x4jaOEweTTWE2+9sr0Qqqevj2Uv5XorvusThmc9XGYpS9yI+fhh8RTafBtGposw=="
  "resolved" "https://registry.npmjs.org/@popperjs/core/-/core-2.11.5.tgz"
  "version" "2.11.5"

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  "integrity" "sha1-m4sMxmPWaafY9vXQiToU00jzD78="
  "resolved" "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz"
  "version" "1.1.2"

"@protobufjs/base64@^1.1.2":
  "integrity" "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg=="
  "resolved" "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz"
  "version" "1.1.2"

"@protobufjs/codegen@^2.0.4":
  "integrity" "sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg=="
  "resolved" "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz"
  "version" "2.0.4"

"@protobufjs/eventemitter@^1.1.0":
  "integrity" "sha1-NVy8mLr61ZePntCV85diHx0Ga3A="
  "resolved" "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz"
  "version" "1.1.0"

"@protobufjs/fetch@^1.1.0":
  "integrity" "sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU="
  "resolved" "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  "integrity" "sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E="
  "resolved" "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz"
  "version" "1.0.2"

"@protobufjs/inquire@^1.1.0":
  "integrity" "sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik="
  "resolved" "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz"
  "version" "1.1.0"

"@protobufjs/path@^1.1.2":
  "integrity" "sha1-bMKyDFya1q0NzP0hynZz2Nf79o0="
  "resolved" "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz"
  "version" "1.1.2"

"@protobufjs/pool@^1.1.0":
  "integrity" "sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q="
  "resolved" "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz"
  "version" "1.1.0"

"@protobufjs/utf8@^1.1.0":
  "integrity" "sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA="
  "resolved" "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz"
  "version" "1.1.0"

"@react-aria/ssr@^3.0.1":
  "integrity" "sha512-RxqQKmE8sO7TGdrcSlHTcVzMP450hqowtBSd2bBS9oPlcokVkaGq28c3Rwa8ty5ctw4EBCjXqjP7xdcKMGDzug=="
  "resolved" "https://registry.npmjs.org/@react-aria/ssr/-/ssr-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "@babel/runtime" "^7.6.2"

"@reduxjs/toolkit@^1.7.1":
  "integrity" "sha512-wXwXYjBVz/ItxB7SMzEAMmEE/FBiY1ze18N+VVVX7NtVbRUrdOGKhpQMHivIJfkbJvSdLUU923a/yAagJQzY0Q=="
  "resolved" "https://registry.npmjs.org/@reduxjs/toolkit/-/toolkit-1.7.1.tgz"
  "version" "1.7.1"
  dependencies:
    "immer" "^9.0.7"
    "redux" "^4.1.2"
    "redux-thunk" "^2.4.1"
    "reselect" "^4.1.5"

"@restart/hooks@^0.4.0", "@restart/hooks@^0.4.5":
  "integrity" "sha512-tLGtY0aHeIfT7aPwUkvQuhIy3+q3w4iqmUzFLPlOAf/vNUacLaBt1j/S//jv/dQhenRh8jvswyMojCwmLvJw8A=="
  "resolved" "https://registry.npmjs.org/@restart/hooks/-/hooks-0.4.5.tgz"
  "version" "0.4.5"
  dependencies:
    "dequal" "^2.0.2"

"@restart/ui@^0.2.5":
  "integrity" "sha512-3dP8pMFickPpvAG5MVQW53HnJl0c17h7MwvI4nNy9QF66sHSYVchudlqlI8eOSaqnmc5YVjGura63vMb9LTNbQ=="
  "resolved" "https://registry.npmjs.org/@restart/ui/-/ui-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "@babel/runtime" "^7.13.16"
    "@popperjs/core" "^2.10.1"
    "@react-aria/ssr" "^3.0.1"
    "@restart/hooks" "^0.4.0"
    "@types/warning" "^3.0.0"
    "dequal" "^2.0.2"
    "dom-helpers" "^5.2.0"
    "prop-types" "^15.7.2"
    "uncontrollable" "^7.2.1"
    "warning" "^4.0.3"

"@rollup/plugin-babel@^5.2.0":
  "integrity" "sha512-9uIC8HZOnVLrLHxayq/PTzw+uS25E14KPUBh5ktF+18Mjo5yK0ToMMx6epY0uEgkjwJw0aBW4x2horYXh8juWw=="
  "resolved" "https://registry.npmjs.org/@rollup/plugin-babel/-/plugin-babel-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@rollup/pluginutils" "^3.1.0"

"@rollup/plugin-node-resolve@^11.2.1":
  "integrity" "sha512-yc2n43jcqVyGE2sqV5/YCmocy9ArjVAP/BeXyTtADTBBX6V0e5UMqwO8CdQ0kzjb6zu5P1qMzsScCMRvE9OlVg=="
  "resolved" "https://registry.npmjs.org/@rollup/plugin-node-resolve/-/plugin-node-resolve-11.2.1.tgz"
  "version" "11.2.1"
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    "@types/resolve" "1.17.1"
    "builtin-modules" "^3.1.0"
    "deepmerge" "^4.2.2"
    "is-module" "^1.0.0"
    "resolve" "^1.19.0"

"@rollup/plugin-replace@^2.4.1":
  "integrity" "sha512-IGcu+cydlUMZ5En85jxHH4qj2hta/11BHq95iHEyb2sbgiN0eCdzvUcHw5gt9pBL5lTi4JDYJ1acCoMGpTvEZg=="
  "resolved" "https://registry.npmjs.org/@rollup/plugin-replace/-/plugin-replace-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    "magic-string" "^0.25.7"

"@rollup/pluginutils@^3.1.0":
  "integrity" "sha512-GksZ6pr6TpIjHm8h9lSQ8pi8BE9VeubNT0OMJ3B5uZJ8pz73NPiqOtCog/x2/QzM1ENChPKxMDhiQuRHsqc+lg=="
  "resolved" "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "@types/estree" "0.0.39"
    "estree-walker" "^1.0.1"
    "picomatch" "^2.2.2"

"@rushstack/eslint-patch@^1.1.0":
  "integrity" "sha512-JLo+Y592QzIE+q7Dl2pMUtt4q8SKYI5jDrZxrozEQxnGVOyYE+GWK9eLkwTaeN9DDctlaRAQ3TBmzZ1qdLE30A=="
  "resolved" "https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.1.0.tgz"
  "version" "1.1.0"

"@sinonjs/commons@^1.7.0":
  "integrity" "sha512-xkNcLAn/wZaX14RPlwizcKicDk9G3F8m2nU3L7Ukm5zBgTwiT0wsoFAHx9Jq56fJA1z/7uKGtCRu16sOUCLIHQ=="
  "resolved" "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.3.tgz"
  "version" "1.8.3"
  dependencies:
    "type-detect" "4.0.8"

"@sinonjs/fake-timers@^8.0.1":
  "integrity" "sha512-OAPJUAtgeINhh/TAlUID4QTs53Njm7xzddaVlEs/SXwgtiD1tW22zAB/W1wdqfrpmikgaWQ9Fw6Ws+hsiRm5Vg=="
  "resolved" "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@surma/rollup-plugin-off-main-thread@^2.2.3":
  "integrity" "sha512-lR8q/9W7hZpMWweNiAKU7NQerBnzQQLvi8qnTDU/fxItPhtZVMbPV3lbCwjhIlNBe9Bbr5V+KHshvWmVSG9cxQ=="
  "resolved" "https://registry.npmjs.org/@surma/rollup-plugin-off-main-thread/-/rollup-plugin-off-main-thread-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "ejs" "^3.1.6"
    "json5" "^2.2.0"
    "magic-string" "^0.25.0"
    "string.prototype.matchall" "^4.0.6"

"@svgr/babel-plugin-add-jsx-attribute@^5.4.0":
  "integrity" "sha512-ZFf2gs/8/6B8PnSofI0inYXr2SDNTDScPXhN7k5EqD4aZ3gi6u+rbmZHVB8IM3wDyx8ntKACZbtXSm7oZGRqVg=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-remove-jsx-attribute@^5.4.0":
  "integrity" "sha512-yaS4o2PgUtwLFGTKbsiAy6D0o3ugcUhWK0Z45umJ66EPWunAz9fuFw2gJuje6wqQvQWOTJvIahUwndOXb7QCPg=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1":
  "integrity" "sha512-LA72+88A11ND/yFIMzyuLRSMJ+tRKeYKeQ+mR3DcAZ5I4h5CPWN9AHyUzJbWSYp/u2u0xhmgOe0+E41+GjEueA=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz"
  "version" "5.0.1"

"@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1":
  "integrity" "sha512-PoiE6ZD2Eiy5mK+fjHqwGOS+IXX0wq/YDtNyIgOrc6ejFnxN4b13pRpiIPbtPwHEc+NT2KCjteAcq33/F1Y9KQ=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz"
  "version" "5.0.1"

"@svgr/babel-plugin-svg-dynamic-title@^5.4.0":
  "integrity" "sha512-zSOZH8PdZOpuG1ZVx/cLVePB2ibo3WPpqo7gFIjLV9a0QsuQAzJiwwqmuEdTaW2pegyBE17Uu15mOgOcgabQZg=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-svg-em-dimensions@^5.4.0":
  "integrity" "sha512-cPzDbDA5oT/sPXDCUYoVXEmm3VIoAWAPT6mSPTJNbQaBNUuEKVKyGH93oDY4e42PYHRW67N5alJx/eEol20abw=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-transform-react-native-svg@^5.4.0":
  "integrity" "sha512-3eYP/SaopZ41GHwXma7Rmxcv9uRslRDTY1estspeB1w1ueZWd/tPlMfEOoccYpEMZU3jD4OU7YitnXcF5hLW2Q=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-transform-svg-component@^5.5.0":
  "integrity" "sha512-q4jSH1UUvbrsOtlo/tKcgSeiCHRSBdXoIoqX1pgcKK/aU3JD27wmMKwGtpB8qRYUYoyXvfGxUVKchLuR5pB3rQ=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-5.5.0.tgz"
  "version" "5.5.0"

"@svgr/babel-preset@^5.5.0":
  "integrity" "sha512-4FiXBjvQ+z2j7yASeGPEi8VD/5rrGQk4Xrq3EdJmoZgz/tpqChpo5hgXDvmEauwtvOc52q8ghhZK4Oy7qph4ig=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^5.0.1"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^5.0.1"
    "@svgr/babel-plugin-svg-dynamic-title" "^5.4.0"
    "@svgr/babel-plugin-svg-em-dimensions" "^5.4.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^5.4.0"
    "@svgr/babel-plugin-transform-svg-component" "^5.5.0"

"@svgr/core@^5.5.0":
  "integrity" "sha512-q52VOcsJPvV3jO1wkPtzTuKlvX7Y3xIcWRpCMtBF3MrteZJtBfQw/+u0B1BHy5ColpQc1/YVTrPEtSYIMNZlrQ=="
  "resolved" "https://registry.npmjs.org/@svgr/core/-/core-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@svgr/plugin-jsx" "^5.5.0"
    "camelcase" "^6.2.0"
    "cosmiconfig" "^7.0.0"

"@svgr/hast-util-to-babel-ast@^5.5.0":
  "integrity" "sha512-cAaR/CAiZRB8GP32N+1jocovUtvlj0+e65TB50/6Lcime+EA49m/8l+P2ko+XPJ4dw3xaPS3jOL4F2X4KWxoeQ=="
  "resolved" "https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@babel/types" "^7.12.6"

"@svgr/plugin-jsx@^5.5.0":
  "integrity" "sha512-V/wVh33j12hGh05IDg8GpIUXbjAPnTdPTKuP4VNLggnwaHMPNQNae2pRnyTAILWCQdz5GyMqtO488g7CKM8CBA=="
  "resolved" "https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@babel/core" "^7.12.3"
    "@svgr/babel-preset" "^5.5.0"
    "@svgr/hast-util-to-babel-ast" "^5.5.0"
    "svg-parser" "^2.0.2"

"@svgr/plugin-svgo@^5.5.0":
  "integrity" "sha512-r5swKk46GuQl4RrVejVwpeeJaydoxkdwkM1mBKOgJLBUJPGaLci6ylg/IjhrRsREKDkr4kbMWdgOtbXEh0fyLQ=="
  "resolved" "https://registry.npmjs.org/@svgr/plugin-svgo/-/plugin-svgo-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "cosmiconfig" "^7.0.0"
    "deepmerge" "^4.2.2"
    "svgo" "^1.2.2"

"@svgr/webpack@^5.5.0":
  "integrity" "sha512-DOBOK255wfQxguUta2INKkzPj6AIS6iafZYiYmHn6W3pHlycSRRlvWKCfLDG10fXfLWqE3DJHgRUOyJYmARa7g=="
  "resolved" "https://registry.npmjs.org/@svgr/webpack/-/webpack-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/plugin-transform-react-constant-elements" "^7.12.1"
    "@babel/preset-env" "^7.12.1"
    "@babel/preset-react" "^7.12.5"
    "@svgr/core" "^5.5.0"
    "@svgr/plugin-jsx" "^5.5.0"
    "@svgr/plugin-svgo" "^5.5.0"
    "loader-utils" "^2.0.0"

"@testing-library/dom@^8.0.0", "@testing-library/dom@>=7.21.4":
  "integrity" "sha512-idsS/cqbYudXcVWngc1PuWNmXs416oBy2g/7Q8QAUREt5Z3MUkAL2XJD7xazLJ6esDfqRDi/ZBxk+OPPXitHRw=="
  "resolved" "https://registry.npmjs.org/@testing-library/dom/-/dom-8.11.2.tgz"
  "version" "8.11.2"
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^4.2.0"
    "aria-query" "^5.0.0"
    "chalk" "^4.1.0"
    "dom-accessibility-api" "^0.5.9"
    "lz-string" "^1.4.4"
    "pretty-format" "^27.0.2"

"@testing-library/jest-dom@^5.16.1":
  "integrity" "sha512-ajUJdfDIuTCadB79ukO+0l8O+QwN0LiSxDaYUTI4LndbbUsGi6rWU1SCexXzBA2NSjlVB9/vbkasQIL3tmPBjw=="
  "resolved" "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.16.1.tgz"
  "version" "5.16.1"
  dependencies:
    "@babel/runtime" "^7.9.2"
    "@types/testing-library__jest-dom" "^5.9.1"
    "aria-query" "^5.0.0"
    "chalk" "^3.0.0"
    "css" "^3.0.0"
    "css.escape" "^1.5.1"
    "dom-accessibility-api" "^0.5.6"
    "lodash" "^4.17.15"
    "redent" "^3.0.0"

"@testing-library/react@^12.1.2":
  "integrity" "sha512-ihQiEOklNyHIpo2Y8FREkyD1QAea054U0MVbwH1m8N9TxeFz+KoJ9LkqoKqJlzx2JDm56DVwaJ1r36JYxZM05g=="
  "resolved" "https://registry.npmjs.org/@testing-library/react/-/react-12.1.2.tgz"
  "version" "12.1.2"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@testing-library/dom" "^8.0.0"

"@testing-library/user-event@^13.5.0":
  "integrity" "sha512-5Kwtbo3Y/NowpkbRuSepbyMFkZmHgD+vPzYB/RJ4oxt5Gj/avFFBYjhw27cqSVPVw/3a67NK1PbiIr9k4Gwmdg=="
  "resolved" "https://registry.npmjs.org/@testing-library/user-event/-/user-event-13.5.0.tgz"
  "version" "13.5.0"
  dependencies:
    "@babel/runtime" "^7.12.5"

"@tootallnate/once@1":
  "integrity" "sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw=="
  "resolved" "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz"
  "version" "1.1.2"

"@trysound/sax@0.2.0":
  "integrity" "sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA=="
  "resolved" "https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz"
  "version" "0.2.0"

"@types/aria-query@^4.2.0":
  "integrity" "sha512-HnYpAE1Y6kRyKM/XkEuiRQhTHvkzMBurTHnpFLYLBGPIylZNPs9jJcuOOYWxPLJCSEtmZT0Y8rHDokKN7rRTig=="
  "resolved" "https://registry.npmjs.org/@types/aria-query/-/aria-query-4.2.2.tgz"
  "version" "4.2.2"

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.14", "@types/babel__core@^7.1.9":
  "integrity" "sha512-S7unDjm/C7z2A2R9NzfKCK1I+BAALDtxEmsJBwlB3EzNfb929ykjL++1CK9LO++EIp2fQrC8O+BwjKvz6UeDyQ=="
  "resolved" "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.18.tgz"
  "version" "7.1.18"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg=="
  "resolved" "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.4.tgz"
  "version" "7.6.4"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g=="
  "resolved" "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz"
  "version" "7.4.1"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  "integrity" "sha512-K2waXdXBi2302XUdcHcR1jCeU0LL4TD9HRs/gk0N2Xvrht+G/BfJa4QObBQZfhMdxiCpV3COl5Nfq4uKTeTnJA=="
  "resolved" "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/types" "^7.3.0"

"@types/body-parser@*":
  "integrity" "sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g=="
  "resolved" "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.2.tgz"
  "version" "1.19.2"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  "integrity" "sha512-p7ienRMiS41Nu2/igbJxxLDWrSZ0WxM8UQgCeO9KhoVF7cOVFkrKsiDr1EsJIla8vV3oEEjGcz11jc5yimhzZw=="
  "resolved" "https://registry.npmjs.org/@types/bonjour/-/bonjour-3.5.10.tgz"
  "version" "3.5.10"
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@^1.3.5":
  "integrity" "sha512-h8QJa8xSb1WD4fpKBDcATDNGXghFj6/3GRWG6dhmRcu0RX1Ubasur2Uvx5aeEwlf0MwblEC2bMzzMQntxnw/Cw=="
  "resolved" "https://registry.npmjs.org/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  "integrity" "sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ=="
  "resolved" "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz"
  "version" "3.4.35"
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.0":
  "integrity" "sha512-PB3ldyrcnAicT35TWPs5IcwKD8S333HMaa2VVv4+wdvebJkjWuW/xESoB8IwRcog8HYVYamb1g/R31Qv5Bx03g=="
  "resolved" "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*", "@types/eslint@^7.28.2":
  "integrity" "sha512-VNcvioYDH8/FxaeTKkM4/TiTwt6pBV9E3OfGmvaw8tPl0rrHCJ4Ll15HRT+pMiFAf/MLQvAzC+6RzUMEL9Ceng=="
  "resolved" "https://registry.npmjs.org/@types/eslint/-/eslint-7.29.0.tgz"
  "version" "7.29.0"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^0.0.50":
  "integrity" "sha512-C6N5s2ZFtuZRj54k2/zyRhNDjJwwcViAM3Nbm8zjBpbqAdZ00mr0CFxvSKeO8Y/e03WVFLpQMdHYVfUd6SB+Hw=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-0.0.50.tgz"
  "version" "0.0.50"

"@types/estree@0.0.39":
  "integrity" "sha512-EYNwp3bU+98cpU4lAWYYL7Zz+2gryWH1qbdDTidVd6hkiR6weksdbMadyXKXNPEkQFhXM+hVO9ZygomHXp+AIw=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-0.0.39.tgz"
  "version" "0.0.39"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  "integrity" "sha512-P1BJAEAW3E2DJUlkgq4tOL3RyMunoWXqbSCygWo5ZIWTjUgN1YnaXWW4VWl/oc8vs/XoYibEGBKP0uZyF4AHig=="
  "resolved" "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.28.tgz"
  "version" "4.17.28"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  "integrity" "sha512-6bSZTPaTIACxn48l50SR+axgrqm6qXFIxrdAKaG6PaJk3+zuUr35hBlgT7vOmJcum+OEaIBLtHV/qloEAFITeA=="
  "resolved" "https://registry.npmjs.org/@types/express/-/express-4.17.13.tgz"
  "version" "4.17.13"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/graceful-fs@^4.1.2":
  "integrity" "sha512-anKkLmZZ+xm4p8JWBf4hElkM4XR+EZeA2M9BAkkTldmcyDY4mbdIJnRghDJH3Ov5ooY7/UAoENtmdMSkaAd7Cw=="
  "resolved" "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "@types/node" "*"

"@types/hoist-non-react-statics@^3.3.0":
  "integrity" "sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA=="
  "resolved" "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"

"@types/html-minifier-terser@^6.0.0":
  "integrity" "sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg=="
  "resolved" "https://registry.npmjs.org/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  "version" "6.1.0"

"@types/http-proxy@^1.17.5":
  "integrity" "sha512-5kPLG5BKpWYkw/LVOGWpiq3nEVqxiN32rTgI53Sk12/xHFQ2rG3ehI9IO+O3W2QoKeyB92dJkoka8SUm6BX1pA=="
  "resolved" "https://registry.npmjs.org/@types/http-proxy/-/http-proxy-1.17.8.tgz"
  "version" "1.17.8"
  dependencies:
    "@types/node" "*"

"@types/invariant@^2.2.33":
  "integrity" "sha512-DxX1V9P8zdJPYQat1gHyY0xj3efl8gnMVjiM9iCY6y27lj+PoQWkgjt8jDqmovPqULkKVpKRg8J36iQiA+EtEg=="
  "resolved" "https://registry.npmjs.org/@types/invariant/-/invariant-2.2.35.tgz"
  "version" "2.2.35"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  "integrity" "sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz"
  "version" "2.0.4"

"@types/istanbul-lib-report@*":
  "integrity" "sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  "integrity" "sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@*":
  "integrity" "sha512-gHl8XuC1RZ8H2j5sHv/JqsaxXkDDM9iDOgu0Wp8sjs4u/snb2PVehyWXJPr+ORA0RPpgw231mnutWI1+0hgjIQ=="
  "resolved" "https://registry.npmjs.org/@types/jest/-/jest-27.4.0.tgz"
  "version" "27.4.0"
  dependencies:
    "jest-diff" "^27.0.0"
    "pretty-format" "^27.0.0"

"@types/json-schema@*", "@types/json-schema@^7.0.4", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  "integrity" "sha512-qcUXuemtEu+E5wZSJHNxUXeCZhAfXKQ41D+duX+VYPde7xyEVZci+/oXKJL13tnRs9lR2pr4fod59GT6/X1/yQ=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz"
  "version" "7.0.9"

"@types/json5@^0.0.29":
  "integrity" "sha1-7ihweulOEdK4J7y+UnC86n8+ce4="
  "resolved" "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  "version" "0.0.29"

"@types/long@^4.0.1":
  "integrity" "sha512-5tXH6Bx/kNGd3MgffdmP4dy2Z+G4eaXw0SE81Tq3BNadtnMR5/ySMzX4SLEzHJzSmPNn4HIdpQsBvXMUykr58w=="
  "resolved" "https://registry.npmjs.org/@types/long/-/long-4.0.1.tgz"
  "version" "4.0.1"

"@types/mime@^1":
  "integrity" "sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw=="
  "resolved" "https://registry.npmjs.org/@types/mime/-/mime-1.3.2.tgz"
  "version" "1.3.2"

"@types/node@*", "@types/node@>=12.12.47", "@types/node@>=13.7.0":
  "integrity" "sha512-5dNBXu/FOER+EXnyah7rn8xlNrfMOQb/qXnw4NQgLkCygKBKhdmF/CA5oXVOKZLBEahw8s2WP9LxIcN/oDDRgQ=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-17.0.9.tgz"
  "version" "17.0.9"

"@types/parse-json@^4.0.0":
  "integrity" "sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA=="
  "resolved" "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"

"@types/prettier@^2.1.5":
  "integrity" "sha512-QzSuZMBuG5u8HqYz01qtMdg/Jfctlnvj1z/lYnIDXs/golxw0fxtRAHd9KrzjR7Yxz1qVeI00o0kiO3PmVdJ9w=="
  "resolved" "https://registry.npmjs.org/@types/prettier/-/prettier-2.4.3.tgz"
  "version" "2.4.3"

"@types/prop-types@*", "@types/prop-types@^15.7.3", "@types/prop-types@^15.7.5":
  "integrity" "sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w=="
  "resolved" "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz"
  "version" "15.7.5"

"@types/q@^1.5.1":
  "integrity" "sha512-L28j2FcJfSZOnL1WBjDYp2vUHCeIFlyYI/53EwD/rKUBQ7MtUUfbQWiyKJGpcnv4/WgrhWsFKrcPstcAt/J0tQ=="
  "resolved" "https://registry.npmjs.org/@types/q/-/q-1.5.5.tgz"
  "version" "1.5.5"

"@types/qs@*":
  "integrity" "sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw=="
  "resolved" "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz"
  "version" "6.9.7"

"@types/range-parser@*":
  "integrity" "sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw=="
  "resolved" "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz"
  "version" "1.2.4"

"@types/react-is@^16.7.1 || ^17.0.0":
  "integrity" "sha512-aBTIWg1emtu95bLTLx0cpkxwGW3ueZv71nE2YFBpL8k/z5czEW8yYpOo8Dp+UUAFAtKwNaOsh/ioSeQnWlZcfw=="
  "resolved" "https://registry.npmjs.org/@types/react-is/-/react-is-17.0.3.tgz"
  "version" "17.0.3"
  dependencies:
    "@types/react" "*"

"@types/react-redux@^7.1.20":
  "integrity" "sha512-GxIA1kM7ClU73I6wg9IRTVwSO9GS+SAKZKe0Enj+82HMU6aoESFU2HNAdNi3+J53IaOHPiUfT3kSG4L828joDQ=="
  "resolved" "https://registry.npmjs.org/@types/react-redux/-/react-redux-7.1.22.tgz"
  "version" "7.1.22"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"
    "redux" "^4.0.0"

"@types/react-transition-group@^4.4.1", "@types/react-transition-group@^4.4.4":
  "integrity" "sha512-7gAPz7anVK5xzbeQW9wFBDg7G++aPLAFY0QaSMOou9rJZpbuI58WAuJrgu+qR92l61grlnCUe7AFX8KGahAgug=="
  "resolved" "https://registry.npmjs.org/@types/react-transition-group/-/react-transition-group-4.4.4.tgz"
  "version" "4.4.4"
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^17.0.0 || ^18.0.0", "@types/react@>=16.14.8", "@types/react@>=16.9.11":
  "integrity" "sha512-SI92X1IA+FMnP3qM5m4QReluXzhcmovhZnLNm3pyeQlooi02qI7sLiepEYqT678uNiyc25XfCqxREFpy3W7YhQ=="
  "resolved" "https://registry.npmjs.org/@types/react/-/react-17.0.38.tgz"
  "version" "17.0.38"
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    "csstype" "^3.0.2"

"@types/resolve@1.17.1":
  "integrity" "sha512-yy7HuzQhj0dhGpD8RLXSZWEkLsV9ibvxvi6EiJ3bkqLAO1RGo0WbkWQiwpRlSFymTJRz0d3k5LM3kkx8ArDbLw=="
  "resolved" "https://registry.npmjs.org/@types/resolve/-/resolve-1.17.1.tgz"
  "version" "1.17.1"
  dependencies:
    "@types/node" "*"

"@types/retry@^0.12.0":
  "integrity" "sha512-xoDlM2S4ortawSWORYqsdU+2rxdh4LRW9ytc3zmT37RIKQh6IHyKwwtKhKis9ah8ol07DCkZxPt8BBvPjC6v4g=="
  "resolved" "https://registry.npmjs.org/@types/retry/-/retry-0.12.1.tgz"
  "version" "0.12.1"

"@types/scheduler@*":
  "integrity" "sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew=="
  "resolved" "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz"
  "version" "0.16.2"

"@types/serve-index@^1.9.1":
  "integrity" "sha512-d/Hs3nWDxNL2xAczmOVZNj92YZCS6RGxfBPjKzuu/XirCgXdpKEb88dYNbrYGint6IVWLNP+yonwVAuRC0T2Dg=="
  "resolved" "https://registry.npmjs.org/@types/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "@types/express" "*"

"@types/serve-static@*":
  "integrity" "sha512-nCkHGI4w7ZgAdNkrEu0bv+4xNV/XDqW+DydknebMOQwkpDGx8G+HTlj7R7ABI8i8nKxVw0wtKPi1D+lPOkh4YQ=="
  "resolved" "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.10.tgz"
  "version" "1.13.10"
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/sockjs@^0.3.33":
  "integrity" "sha512-f0KEEe05NvUnat+boPTZ0dgaLZ4SfSouXUgv5noUiefG2ajgKjmETo9ZJyuqsl7dfl2aHlLJUiki6B4ZYldiiw=="
  "resolved" "https://registry.npmjs.org/@types/sockjs/-/sockjs-0.3.33.tgz"
  "version" "0.3.33"
  dependencies:
    "@types/node" "*"

"@types/stack-utils@^2.0.0":
  "integrity" "sha512-Hl219/BT5fLAaz6NDkSuhzasy49dwQS/DSdu4MdggFB8zcXv7vflBI3xp7FEmkmdDkBUI2bPUNeMttp2knYdxw=="
  "resolved" "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.1.tgz"
  "version" "2.0.1"

"@types/testing-library__jest-dom@^5.9.1":
  "integrity" "sha512-vehbtyHUShPxIa9SioxDwCvgxukDMH//icJG90sXQBUm5lJOHLT5kNeU9tnivhnA/TkOFMzGIXN2cTc4hY8/kg=="
  "resolved" "https://registry.npmjs.org/@types/testing-library__jest-dom/-/testing-library__jest-dom-5.14.2.tgz"
  "version" "5.14.2"
  dependencies:
    "@types/jest" "*"

"@types/trusted-types@^2.0.2":
  "integrity" "sha512-F5DIZ36YVLE+PN+Zwws4kJogq47hNgX3Nx6WyDJ3kcplxyke3XIzB8uK5n/Lpm1HBsbGzd6nmGehL8cPekP+Tg=="
  "resolved" "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.2.tgz"
  "version" "2.0.2"

"@types/warning@^3.0.0":
  "integrity" "sha1-DSUBJorY+ZYrdA04fEZU9fjiPlI="
  "resolved" "https://registry.npmjs.org/@types/warning/-/warning-3.0.0.tgz"
  "version" "3.0.0"

"@types/ws@^8.2.2":
  "integrity" "sha512-NOn5eIcgWLOo6qW8AcuLZ7G8PycXu0xTxxkS6Q18VWFxgPUSOwV0pBj2a/4viNZVu25i7RIB7GttdkAIUUXOOg=="
  "resolved" "https://registry.npmjs.org/@types/ws/-/ws-8.2.2.tgz"
  "version" "8.2.2"
  dependencies:
    "@types/node" "*"

"@types/yargs-parser@*":
  "integrity" "sha512-7tFImggNeNBVMsn0vLrpn1H1uPrUBdnARPTpZoitY37ZrdJREzf7I16tMrlK3hen349gr1NYh8CmZQa7CTG6Aw=="
  "resolved" "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.1.tgz"
  "version" "20.2.1"

"@types/yargs@^16.0.0":
  "integrity" "sha512-T8Yc9wt/5LbJyCaLiHPReJa0kApcIgJ7Bn735GjItUfh08Z1pJvu8QZqb9s+mMvKV6WUQRV7K2R46YbjMXTTJw=="
  "resolved" "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.4.tgz"
  "version" "16.0.4"
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^4.0.0 || ^5.0.0", "@typescript-eslint/eslint-plugin@^5.5.0":
  "integrity" "sha512-XXVKnMsq2fuu9K2KsIxPUGqb6xAImz8MEChClbXmE3VbveFtBUU5bzM6IPVWqzyADIgdkS2Ws/6Xo7W2TeZWjQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "@typescript-eslint/scope-manager" "5.10.0"
    "@typescript-eslint/type-utils" "5.10.0"
    "@typescript-eslint/utils" "5.10.0"
    "debug" "^4.3.2"
    "functional-red-black-tree" "^1.0.1"
    "ignore" "^5.1.8"
    "regexpp" "^3.2.0"
    "semver" "^7.3.5"
    "tsutils" "^3.21.0"

"@typescript-eslint/experimental-utils@^5.0.0", "@typescript-eslint/experimental-utils@^5.9.0":
  "integrity" "sha512-GeQAPqQMI5DVMGOUwGbSR+NdsirryyKOgUFRTWInhlsKUArns/MVnXmPpzxfrzB1nU36cT5WJAwmfCsjoaVBWg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "@typescript-eslint/utils" "5.10.0"

"@typescript-eslint/parser@^5.0.0", "@typescript-eslint/parser@^5.5.0":
  "integrity" "sha512-pJB2CCeHWtwOAeIxv8CHVGJhI5FNyJAIpx5Pt72YkK3QfEzt6qAlXZuyaBmyfOdM62qU0rbxJzNToPTVeJGrQw=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "@typescript-eslint/scope-manager" "5.10.0"
    "@typescript-eslint/types" "5.10.0"
    "@typescript-eslint/typescript-estree" "5.10.0"
    "debug" "^4.3.2"

"@typescript-eslint/scope-manager@5.10.0":
  "integrity" "sha512-tgNgUgb4MhqK6DoKn3RBhyZ9aJga7EQrw+2/OiDk5hKf3pTVZWyqBi7ukP+Z0iEEDMF5FDa64LqODzlfE4O/Dg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "@typescript-eslint/types" "5.10.0"
    "@typescript-eslint/visitor-keys" "5.10.0"

"@typescript-eslint/type-utils@5.10.0":
  "integrity" "sha512-TzlyTmufJO5V886N+hTJBGIfnjQDQ32rJYxPaeiyWKdjsv2Ld5l8cbS7pxim4DeNs62fKzRSt8Q14Evs4JnZyQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "@typescript-eslint/utils" "5.10.0"
    "debug" "^4.3.2"
    "tsutils" "^3.21.0"

"@typescript-eslint/types@5.10.0":
  "integrity" "sha512-wUljCgkqHsMZbw60IbOqT/puLfyqqD5PquGiBo1u1IS3PLxdi3RDGlyf032IJyh+eQoGhz9kzhtZa+VC4eWTlQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.10.0.tgz"
  "version" "5.10.0"

"@typescript-eslint/typescript-estree@5.10.0":
  "integrity" "sha512-x+7e5IqfwLwsxTdliHRtlIYkgdtYXzE0CkFeV6ytAqq431ZyxCFzNMNR5sr3WOlIG/ihVZr9K/y71VHTF/DUQA=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "@typescript-eslint/types" "5.10.0"
    "@typescript-eslint/visitor-keys" "5.10.0"
    "debug" "^4.3.2"
    "globby" "^11.0.4"
    "is-glob" "^4.0.3"
    "semver" "^7.3.5"
    "tsutils" "^3.21.0"

"@typescript-eslint/utils@5.10.0":
  "integrity" "sha512-IGYwlt1CVcFoE2ueW4/ioEwybR60RAdGeiJX/iDAw0t5w0wK3S7QncDwpmsM70nKgGTuVchEWB8lwZwHqPAWRg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "@typescript-eslint/scope-manager" "5.10.0"
    "@typescript-eslint/types" "5.10.0"
    "@typescript-eslint/typescript-estree" "5.10.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^3.0.0"

"@typescript-eslint/visitor-keys@5.10.0":
  "integrity" "sha512-GMxj0K1uyrFLPKASLmZzCuSddmjZVbVj3Ouy5QVuIGKZopxvOr24JsS7gruz6C3GExE01mublZ3mIBOaon9zuQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "@typescript-eslint/types" "5.10.0"
    "eslint-visitor-keys" "^3.0.0"

"@webassemblyjs/ast@1.11.1":
  "integrity" "sha512-ukBh14qFLjxTQNTXocdyksN5QdM28S1CxHt2rdskFyL+xFV7VremuBLVbmCePj+URalXBENx/9Lm7lnhihtCSw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"

"@webassemblyjs/floating-point-hex-parser@1.11.1":
  "integrity" "sha512-iGRfyc5Bq+NnNuX8b5hwBrRjzf0ocrJPI6GWFodBFzmFnyvrQ83SHKhmilCU/8Jv67i4GJZBMhEzltxzcNagtQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-api-error@1.11.1":
  "integrity" "sha512-RlhS8CBCXfRUR/cwo2ho9bkheSXG0+NwooXcc3PAILALf2QLdFyj7KGsKRbVc95hZnhnERon4kW/D3SZpp6Tcg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-buffer@1.11.1":
  "integrity" "sha512-gwikF65aDNeeXa8JxXa2BAk+REjSyhrNC9ZwdT0f8jc4dQQeDQ7G4m0f2QCLPJiMTTO6wfDmRmj/pW0PsUvIcA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-numbers@1.11.1":
  "integrity" "sha512-vDkbxiB8zfnPdNK9Rajcey5C0w+QJugEglN0of+kmO8l7lDb77AnlKYQF7aarZuCrv+l0UvqL+68gSDr3k9LPQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.1":
  "integrity" "sha512-PvpoOGiJwXeTrSf/qfudJhwlvDQxFgelbMqtq52WWiXC6Xgg1IREdngmPN3bs4RoO83PnL/nFrxucXj1+BX62Q=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-wasm-section@1.11.1":
  "integrity" "sha512-10P9No29rYX1j7F3EVPX3JvGPQPae+AomuSTPiF9eBQeChHI6iqjMIwR9JmOJXwpnn/oVGDk7I5IlskuMwU/pg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"

"@webassemblyjs/ieee754@1.11.1":
  "integrity" "sha512-hJ87QIPtAMKbFq6CGTkZYJivEwZDbQUgYd3qKSadTNOhVY7p+gfP6Sr0lLRVTaG1JjFj+r3YchoqRYxNH3M0GQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.1":
  "integrity" "sha512-BJ2P0hNZ0u+Th1YZXJpzW6miwqQUGcIHT1G/sf72gLVD9DZ5AdYTqPNbHZh6K1M5VmKvFXwGSWZADz+qBWxeRw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.1":
  "integrity" "sha512-9kqcxAEdMhiwQkHpkNiorZzqpGrodQQ2IGrHHxCy+Ozng0ofyMA0lTqiLkVs1uzTRejX+/O0EOT7KxqVPuXosQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/wasm-edit@1.11.1":
  "integrity" "sha512-g+RsupUC1aTHfR8CDgnsVRVZFJqdkFHpsHMfJuWQzWU3tvnLC07UqHICfP+4XyL2tnr1amvl1Sdp06TnYCmVkA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/helper-wasm-section" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-opt" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "@webassemblyjs/wast-printer" "1.11.1"

"@webassemblyjs/wasm-gen@1.11.1":
  "integrity" "sha512-F7QqKXwwNlMmsulj6+O7r4mmtAlCWfO/0HdgOxSklZfQcDu0TpLiD1mRt/zF25Bk59FIjEuGAIyn5ei4yMfLhA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wasm-opt@1.11.1":
  "integrity" "sha512-VqnkNqnZlU5EB64pp1l7hdm3hmQw7Vgqa0KF/KCNO9sIpI6Fk6brDEiX+iCOYrvMuBWDws0NkTOxYEb85XQHHw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"

"@webassemblyjs/wasm-parser@1.11.1":
  "integrity" "sha512-rrBujw+dJu32gYB7/Lup6UhdkPx9S9SnobZzRVL7VcBH9Bt9bCBLEuX/YXOOtBsOZ4NQrRykKhffRWHvigQvOA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wast-printer@1.11.1":
  "integrity" "sha512-IQboUWM4eKzWW+N/jij2sRatKMh99QEelo3Eb2q0qXkvPRISAj8Qxtmw5itwqK+TTkBuUIE45AxYPToqPtL5gg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="
  "resolved" "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ=="
  "resolved" "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  "version" "4.2.2"

"abab@^2.0.3", "abab@^2.0.5":
  "integrity" "sha512-9IK9EadsbHo6jLWIpxpR6pL0sazTXV6+SQv25ZB+F7Bj9mJNaOc4nCRabwd5M/JwmUa8idz6Eci6eKfJryPs6Q=="
  "resolved" "https://registry.npmjs.org/abab/-/abab-2.0.5.tgz"
  "version" "2.0.5"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.7":
  "integrity" "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA=="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "mime-types" "~2.1.24"
    "negotiator" "0.6.2"

"acorn-globals@^6.0.0":
  "integrity" "sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg=="
  "resolved" "https://registry.npmjs.org/acorn-globals/-/acorn-globals-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"

"acorn-import-assertions@^1.7.6":
  "integrity" "sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw=="
  "resolved" "https://registry.npmjs.org/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz"
  "version" "1.8.0"

"acorn-jsx@^5.3.1":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-node@^1.6.1":
  "integrity" "sha512-8mt+fslDufLYntIoPAaIMUe/lrbrehIiwmR3t2k9LljIzoigEPF27eLk2hy8zSGzmR/ogr7zbRKINMo1u0yh5A=="
  "resolved" "https://registry.npmjs.org/acorn-node/-/acorn-node-1.8.2.tgz"
  "version" "1.8.2"
  dependencies:
    "acorn" "^7.0.0"
    "acorn-walk" "^7.0.0"
    "xtend" "^4.0.2"

"acorn-walk@^7.0.0", "acorn-walk@^7.1.1":
  "integrity" "sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA=="
  "resolved" "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8", "acorn@^8.2.4", "acorn@^8.4.1", "acorn@^8.5.0", "acorn@^8.7.0":
  "integrity" "sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.7.0.tgz"
  "version" "8.7.0"

"acorn@^7.0.0":
  "integrity" "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^7.1.1":
  "integrity" "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"address@^1.0.1", "address@^1.1.2":
  "integrity" "sha512-aT6camzM4xEA54YVJYSqxz1kv4IHnQZRtThJJHhUMRExaU5spC7jX5ugSwTaTgJliIgs4VhZOk7htClvQ/LmRA=="
  "resolved" "https://registry.npmjs.org/address/-/address-1.1.2.tgz"
  "version" "1.1.2"

"adjust-sourcemap-loader@^4.0.0":
  "integrity" "sha512-OXwN5b9pCUXNQHJpwwD2qP40byEmSgzj8B4ydSN0uMNYWiFmJ6x6KwUllMmfk8Rwu/HJDFR7U8ubsWBoN0Xp0A=="
  "resolved" "https://registry.npmjs.org/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "loader-utils" "^2.0.0"
    "regex-parser" "^2.2.11"

"agent-base@6":
  "integrity" "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ=="
  "resolved" "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "4"

"aggregate-error@^3.0.0":
  "integrity" "sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA=="
  "resolved" "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-formats@^2.1.1":
  "integrity" "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA=="
  "resolved" "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ajv" "^8.0.0"

"ajv-keywords@^3.4.1", "ajv-keywords@^3.5.2":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv-keywords@^5.0.0":
  "integrity" "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"

"ajv@^6.10.0", "ajv@^6.12.2", "ajv@^6.12.4", "ajv@^6.12.5", "ajv@^6.9.1":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.0":
  "integrity" "sha512-qOKJyNj/h+OWx7s5DePL6Zu1KeM9jPZhwBqs+7DzP6bGOvqzVCSf0xueYmVuaC/oQ/VtS2zLMLHdQFbkka+XDQ=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.9.0.tgz"
  "version" "8.9.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ajv@^8.6.0", "ajv@>=8":
  "integrity" "sha512-qOKJyNj/h+OWx7s5DePL6Zu1KeM9jPZhwBqs+7DzP6bGOvqzVCSf0xueYmVuaC/oQ/VtS2zLMLHdQFbkka+XDQ=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.9.0.tgz"
  "version" "8.9.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ajv@^8.8.0", "ajv@^8.8.2":
  "integrity" "sha512-qOKJyNj/h+OWx7s5DePL6Zu1KeM9jPZhwBqs+7DzP6bGOvqzVCSf0xueYmVuaC/oQ/VtS2zLMLHdQFbkka+XDQ=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.9.0.tgz"
  "version" "8.9.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"alphanum-sort@^1.0.2":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://registry.npmjs.org/alphanum-sort/-/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-escapes@^4.2.1", "ansi-escapes@^4.3.1":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-html-community@^0.0.8":
  "integrity" "sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw=="
  "resolved" "https://registry.npmjs.org/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  "version" "0.0.8"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-regex@^6.0.1":
  "integrity" "sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz"
  "version" "6.0.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^5.0.0":
  "integrity" "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  "version" "5.2.0"

"anymatch@^3.0.3", "anymatch@~3.1.2":
  "integrity" "sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"arg@^5.0.1":
  "integrity" "sha512-e0hDa9H2Z9AwFkk2qDlwhoMYE4eToKarchkQHovNdLTCYMHZHeRjI71crOh+dio4K6u1IcwubQqo79Ga4CyAQA=="
  "resolved" "https://registry.npmjs.org/arg/-/arg-5.0.1.tgz"
  "version" "5.0.1"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"aria-query@^4.2.2":
  "integrity" "sha512-o/HelwhuKpTj/frsOsbNLNgnNGVIFsVP/SW2BSF14gVl7kAfMOJ6/8wUAUvG1R1NHKrfG+2sHZTu0yauT1qBrA=="
  "resolved" "https://registry.npmjs.org/aria-query/-/aria-query-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "@babel/runtime" "^7.10.2"
    "@babel/runtime-corejs3" "^7.10.2"

"aria-query@^5.0.0":
  "integrity" "sha512-V+SM7AbUwJ+EBnB8+DXs0hPZHO0W6pqBcc0dW90OwtVG02PswOu/teuARoLQjdDOH+t9pJgGnW5/Qmouf3gPJg=="
  "resolved" "https://registry.npmjs.org/aria-query/-/aria-query-5.0.0.tgz"
  "version" "5.0.0"

"array-find-index@^1.0.2":
  "integrity" "sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E="
  "resolved" "https://registry.npmjs.org/array-find-index/-/array-find-index-1.0.2.tgz"
  "version" "1.0.2"

"array-flatten@^2.1.0":
  "integrity" "sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ=="
  "resolved" "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-includes@^3.1.3", "array-includes@^3.1.4":
  "integrity" "sha512-ZTNSQkmWumEbiHO2GF4GmWxYVTiQyJy2XOTa15sdQSrvKn7l+180egQMqlrMOUMCyLMD7pmyQe4mMDUT6Behrw=="
  "resolved" "https://registry.npmjs.org/array-includes/-/array-includes-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"
    "get-intrinsic" "^1.1.1"
    "is-string" "^1.0.7"

"array-union@^2.1.0":
  "integrity" "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw=="
  "resolved" "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  "version" "2.1.0"

"array.prototype.flat@^1.2.5":
  "integrity" "sha512-KaYU+S+ndVqyUnignHftkwc58o3uVU1jzczILJ1tN2YaIZpFIKBiP/x/j97E5MVPsaCloPbqWLB/8qCTVvT2qg=="
  "resolved" "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.0"

"array.prototype.flatmap@^1.2.5":
  "integrity" "sha512-08u6rVyi1Lj7oqWbS9nUxliETrtIROT4XGTA4D/LWGten6E3ocm7cy9SIrmNHOL5XVbVuckUp3X6Xyg8/zpvHA=="
  "resolved" "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.0"

"asap@~2.0.3", "asap@~2.0.6":
  "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
  "resolved" "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  "version" "2.0.6"

"ast-types-flow@^0.0.7":
  "integrity" "sha1-9wtzXGvKGlycItmCw+Oef+ujva0="
  "resolved" "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.7.tgz"
  "version" "0.0.7"

"async@^2.6.2":
  "integrity" "sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg=="
  "resolved" "https://registry.npmjs.org/async/-/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"async@0.9.x":
  "integrity" "sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0="
  "resolved" "https://registry.npmjs.org/async/-/async-0.9.2.tgz"
  "version" "0.9.2"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"at-least-node@^1.0.0":
  "integrity" "sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg=="
  "resolved" "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz"
  "version" "1.0.0"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^10.0.2", "autoprefixer@^10.4.2":
  "integrity" "sha512-9fOPpHKuDW1w/0EKfRmVnxTDt8166MAnLI3mgZ1JCnhNtYWxcJ6Ud5CO/AVOZi/AvFa8DY9RTy3h3+tFBlrrdQ=="
  "resolved" "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.2.tgz"
  "version" "10.4.2"
  dependencies:
    "browserslist" "^4.19.1"
    "caniuse-lite" "^1.0.30001297"
    "fraction.js" "^4.1.2"
    "normalize-range" "^0.1.2"
    "picocolors" "^1.0.0"
    "postcss-value-parser" "^4.2.0"

"axe-core@^4.3.5":
  "integrity" "sha512-WKTW1+xAzhMS5dJsxWkliixlO/PqC4VhmO9T4juNYcaTg9jzWiJsou6m5pxWYGfigWbwzJWeFY6z47a+4neRXA=="
  "resolved" "https://registry.npmjs.org/axe-core/-/axe-core-4.3.5.tgz"
  "version" "4.3.5"

"axobject-query@^2.2.0":
  "integrity" "sha512-Td525n+iPOOyUQIeBfcASuG6uJsDOITl7Mds5gFyerkWiX7qhUTdYUBlSgNMyVqtSJqwpt1kXGLdUt6SykLMRA=="
  "resolved" "https://registry.npmjs.org/axobject-query/-/axobject-query-2.2.0.tgz"
  "version" "2.2.0"

"babel-jest@^27.4.2", "babel-jest@^27.4.6":
  "integrity" "sha512-qZL0JT0HS1L+lOuH+xC2DVASR3nunZi/ozGhpgauJHgmI7f8rudxf6hUjEHympdQ/J64CdKmPkgfJ+A3U6QCrg=="
  "resolved" "https://registry.npmjs.org/babel-jest/-/babel-jest-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/transform" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/babel__core" "^7.1.14"
    "babel-plugin-istanbul" "^6.1.1"
    "babel-preset-jest" "^27.4.0"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "slash" "^3.0.0"

"babel-loader@^8.2.3":
  "integrity" "sha512-n4Zeta8NC3QAsuyiizu0GkmRcQ6clkV9WFUnUf1iXP//IeSKbWjofW3UHyZVwlOB4y039YQKefawyTn64Zwbuw=="
  "resolved" "https://registry.npmjs.org/babel-loader/-/babel-loader-8.2.3.tgz"
  "version" "8.2.3"
  dependencies:
    "find-cache-dir" "^3.3.1"
    "loader-utils" "^1.4.0"
    "make-dir" "^3.1.0"
    "schema-utils" "^2.6.5"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-istanbul@^6.1.1":
  "integrity" "sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  "version" "6.1.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-instrument" "^5.0.4"
    "test-exclude" "^6.0.0"

"babel-plugin-jest-hoist@^27.4.0":
  "integrity" "sha512-Jcu7qS4OX5kTWBc45Hz7BMmgXuJqRnhatqpUhnzGC3OBYpOmf2tv6jFNwZpwM7wU7MUuv2r9IPS/ZlYOuburVw=="
  "resolved" "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.4.0.tgz"
  "version" "27.4.0"
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

"babel-plugin-macros@^2.6.1":
  "integrity" "sha512-SEP5kJpfGYqYKpBrj5XU3ahw5p5GOHJ0U5ssOSQ/WBVdwkD2Dzlce95exQTs3jOVWPPKLBN2rlEWkCK7dSmLvg=="
  "resolved" "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "cosmiconfig" "^6.0.0"
    "resolve" "^1.12.0"

"babel-plugin-macros@^3.1.0":
  "integrity" "sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg=="
  "resolved" "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "cosmiconfig" "^7.0.0"
    "resolve" "^1.19.0"

"babel-plugin-named-asset-import@^0.3.8":
  "integrity" "sha512-WXiAc++qo7XcJ1ZnTYGtLxmBCVbddAml3CEXgWaBzNzLNoxtQ8AiGEFDMOhot9XjTCQbvP5E77Fj9Gk924f00Q=="
  "resolved" "https://registry.npmjs.org/babel-plugin-named-asset-import/-/babel-plugin-named-asset-import-0.3.8.tgz"
  "version" "0.3.8"

"babel-plugin-polyfill-corejs2@^0.3.0":
  "integrity" "sha512-v7/T6EQcNfVLfcN2X8Lulb7DjprieyLWJK/zOWH5DUYcAgex9sP3h25Q+DLsX9TloXe3y1O8l2q2Jv9q8UVB9w=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.5.0":
  "integrity" "sha512-TihqEe4sQcb/QcPJvxe94/9RZuLQuF1+To4WqQcRvc+3J3gLCPIPgDKzGLG6zmQLfH3nn25heRuDNkS2KR4I8A=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    "core-js-compat" "^3.20.0"

"babel-plugin-polyfill-regenerator@^0.3.0":
  "integrity" "sha512-Y2B06tvgHYt1x0yz17jGkGeeMr5FeKUu+ASJ+N6nB5lQ8Dapfg42i0OVrf8PNGJ3zKL4A23snMi1IRwrqqND7A=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"

"babel-plugin-transform-react-remove-prop-types@^0.4.24":
  "integrity" "sha512-eqj0hVcJUR57/Ug2zE1Yswsw4LhuqqHhD+8v120T1cl3kjg76QwtyBrdIk4WVwK+lAhBJVYCd/v+4nc4y+8JsA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-react-remove-prop-types/-/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz"
  "version" "0.4.24"

"babel-preset-current-node-syntax@^1.0.0":
  "integrity" "sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ=="
  "resolved" "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

"babel-preset-jest@^27.4.0":
  "integrity" "sha512-NK4jGYpnBvNxcGo7/ZpZJr51jCGT+3bwwpVIDY2oNfTxJJldRtB4VAcYdgp1loDE50ODuTu+yBjpMAswv5tlpg=="
  "resolved" "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.4.0.tgz"
  "version" "27.4.0"
  dependencies:
    "babel-plugin-jest-hoist" "^27.4.0"
    "babel-preset-current-node-syntax" "^1.0.0"

"babel-preset-react-app@^10.0.1":
  "integrity" "sha512-b0D9IZ1WhhCWkrTXyFuIIgqGzSkRIH5D5AmB0bXbzYAB1OBAwHcUeyWW2LorutLWF5btNo/N7r/cIdmvvKJlYg=="
  "resolved" "https://registry.npmjs.org/babel-preset-react-app/-/babel-preset-react-app-10.0.1.tgz"
  "version" "10.0.1"
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/plugin-proposal-class-properties" "^7.16.0"
    "@babel/plugin-proposal-decorators" "^7.16.4"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.0"
    "@babel/plugin-proposal-numeric-separator" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-private-methods" "^7.16.0"
    "@babel/plugin-transform-flow-strip-types" "^7.16.0"
    "@babel/plugin-transform-react-display-name" "^7.16.0"
    "@babel/plugin-transform-runtime" "^7.16.4"
    "@babel/preset-env" "^7.16.4"
    "@babel/preset-react" "^7.16.0"
    "@babel/preset-typescript" "^7.16.0"
    "@babel/runtime" "^7.16.3"
    "babel-plugin-macros" "^3.1.0"
    "babel-plugin-transform-react-remove-prop-types" "^0.4.24"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz"
  "version" "0.6.1"

"bfj@^7.0.2":
  "integrity" "sha512-+e/UqUzwmzJamNF50tBV6tZPTORow7gQ96iFow+8b562OdMpEK0BcJEq2OSPEDmAbSMBQ7PKZ87ubFkgxpYWgw=="
  "resolved" "https://registry.npmjs.org/bfj/-/bfj-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "bluebird" "^3.5.5"
    "check-types" "^11.1.1"
    "hoopy" "^0.1.4"
    "tryer" "^1.0.1"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^2.0.0":
  "integrity" "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"bluebird@^3.5.5":
  "integrity" "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="
  "resolved" "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"body-parser@1.19.1":
  "integrity" "sha512-8ljfQi5eBk8EJfECMrgqNGWPEY5jWP+1IzkzkGdFFEwFQZZyaZ21UqdaHktgiMlH0xLHqIFtE/u2OYE5dOtViA=="
  "resolved" "https://registry.npmjs.org/body-parser/-/body-parser-1.19.1.tgz"
  "version" "1.19.1"
  dependencies:
    "bytes" "3.1.1"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "1.8.1"
    "iconv-lite" "0.4.24"
    "on-finished" "~2.3.0"
    "qs" "6.9.6"
    "raw-body" "2.4.2"
    "type-is" "~1.6.18"

"bonjour@^3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://registry.npmjs.org/bonjour/-/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"bootstrap@^5.1.3":
  "integrity" "sha512-fcQztozJ8jToQWXxVuEyXWW+dSo8AiXWKwiSSrKWsRB/Qt+Ewwza+JWoLKiTuQLaEPhdNAJ7+Dosc9DOIqNy7Q=="
  "resolved" "https://registry.npmjs.org/bootstrap/-/bootstrap-5.1.3.tgz"
  "version" "5.1.3"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^3.0.1", "braces@~3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"browser-process-hrtime@^1.0.0":
  "integrity" "sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow=="
  "resolved" "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz"
  "version" "1.0.0"

"browserslist@^4.0.0", "browserslist@^4.14.5", "browserslist@^4.16.0", "browserslist@^4.16.6", "browserslist@^4.17.5", "browserslist@^4.18.1", "browserslist@^4.19.1", "browserslist@>= 4", "browserslist@>=4":
  "integrity" "sha512-u2tbbG5PdKRTUoctO3NBD8FQ5HdPh1ZXPHzp1rwaa5jTc+RV9/+RlWiAIKmjRPQF+xbGM9Kklj5bZQFa2s/38A=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.19.1.tgz"
  "version" "4.19.1"
  dependencies:
    "caniuse-lite" "^1.0.30001286"
    "electron-to-chromium" "^1.4.17"
    "escalade" "^3.1.1"
    "node-releases" "^2.0.1"
    "picocolors" "^1.0.0"

"bser@2.1.1":
  "integrity" "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ=="
  "resolved" "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-int64" "^0.4.0"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer-indexof@^1.0.0":
  "integrity" "sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g=="
  "resolved" "https://registry.npmjs.org/buffer-indexof/-/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"builtin-modules@^3.1.0":
  "integrity" "sha512-lGzLKcioL90C7wMczpkY0n/oART3MbBa8R9OFGE1rJxoVI86u4WAGfEk8Wjv10eKSyTHVGkSo3bvBylCEtk7LA=="
  "resolved" "https://registry.npmjs.org/builtin-modules/-/builtin-modules-3.2.0.tgz"
  "version" "3.2.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.1":
  "integrity" "sha512-dWe4nWO/ruEOY7HkUJ5gFt1DCFV9zPRoJr8pV0/ASQermOZjtq8jMjOprC0Kd10GLN+l7xaUPvxzJFWtxGu8Fg=="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.1.1.tgz"
  "version" "3.1.1"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA=="
  "resolved" "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@^4.1.2":
  "integrity" "sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw=="
  "resolved" "https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "pascal-case" "^3.1.2"
    "tslib" "^2.0.3"

"camelcase-css@^2.0.1":
  "integrity" "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA=="
  "resolved" "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  "version" "2.0.1"

"camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.2.0", "camelcase@^6.2.1":
  "integrity" "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  "version" "6.3.0"

"caniuse-api@^3.0.0":
  "integrity" "sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw=="
  "resolved" "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001286", "caniuse-lite@^1.0.30001297", "caniuse-lite@^1.0.30001299":
  "integrity" "sha512-cVjiJHWGcNlJi8TZVKNMnvMid3Z3TTdDHmLDzlOdIiZq138Exvo0G+G0wTdVYolxKb4AYwC+38pxodiInVtJSA=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001300.tgz"
  "version" "1.0.30001300"

"case-sensitive-paths-webpack-plugin@^2.4.0":
  "integrity" "sha512-roIFONhcxog0JSSWbvVAh3OocukmSgpqOH6YpMkCvav/ySIV3JKg4Dc8vYtQjYi/UxpNE36r/9v+VqTQqgkYmw=="
  "resolved" "https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  "version" "2.4.0"

"chalk@^2.0.0", "chalk@^2.4.1", "chalk@^2.4.2":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.0.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.2":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"char-regex@^1.0.2":
  "integrity" "sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw=="
  "resolved" "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz"
  "version" "1.0.2"

"char-regex@^2.0.0":
  "integrity" "sha512-oGu2QekBMXgyQNWPDRQ001bjvDnZe4/zBTz37TMbiKz1NbNiyiH5hRkobe7npRN6GfbGbxMYFck/vQ1r9c1VMA=="
  "resolved" "https://registry.npmjs.org/char-regex/-/char-regex-2.0.0.tgz"
  "version" "2.0.0"

"check-types@^11.1.1":
  "integrity" "sha512-tzWzvgePgLORb9/3a0YenggReLKAIb2owL03H2Xdoe5pKcUyWRSEQ8xfCar8t2SIAuEDwtmx2da1YB52YuHQMQ=="
  "resolved" "https://registry.npmjs.org/check-types/-/check-types-11.1.2.tgz"
  "version" "11.1.2"

"chokidar@^3.4.2", "chokidar@^3.5.2":
  "integrity" "sha512-ekGhOnNVPgT77r4K/U3GDhu+FQ2S8TnK/s2KbIGXi0SZWuwkZ2QNyfWdZW+TVfn84DpEP7rLeCt2UI6bJ8GwbQ=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.5.2.tgz"
  "version" "3.5.2"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chrome-trace-event@^1.0.2":
  "integrity" "sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg=="
  "resolved" "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
  "version" "1.0.3"

"ci-info@^3.2.0":
  "integrity" "sha512-riT/3vI5YpVH6/qomlDnJow6TBee2PBKSEpx3O32EGPYbWGIRsIlGRms3Sm74wYE1JMo8RnO04Hb12+v1J5ICw=="
  "resolved" "https://registry.npmjs.org/ci-info/-/ci-info-3.3.0.tgz"
  "version" "3.3.0"

"cjs-module-lexer@^1.0.0":
  "integrity" "sha512-cOU9usZw8/dXIXKtwa8pM0OTJQuJkxMN6w30csNRUerHfeQ5R6U3kkU/FtJeIf3M202OHfY2U8ccInBG7/xogA=="
  "resolved" "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.2.tgz"
  "version" "1.2.2"

"classnames@^2.3.1":
  "integrity" "sha512-OlQdbZ7gLfGarSqxesMesDa5uz7KFbID8Kpq/SxIoNGDqY8lSYs0D+hhtBXhcdB3rcbXArFr7vlHheLk1voeNA=="
  "resolved" "https://registry.npmjs.org/classnames/-/classnames-2.3.1.tgz"
  "version" "2.3.1"

"clean-css@^5.2.2":
  "integrity" "sha512-/eR8ru5zyxKzpBLv9YZvMXgTSSQn7AdkMItMYynsFgGwTveCRVam9IUPFloE85B4vAIj05IuKmmEoV7/AQjT0w=="
  "resolved" "https://registry.npmjs.org/clean-css/-/clean-css-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "source-map" "~0.6.0"

"clean-stack@^2.0.0":
  "integrity" "sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A=="
  "resolved" "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cliui@^7.0.2":
  "integrity" "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clsx@^1.1.1":
  "integrity" "sha512-6/bPho624p3S2pMyvP5kKBPXnI3ufHLObBFCfgx+LkeR5lg2XYy2hqZqUf45ypD8COn2bhgGJSUE+l5dhNBieA=="
  "resolved" "https://registry.npmjs.org/clsx/-/clsx-1.1.1.tgz"
  "version" "1.1.1"

"co@^4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  "version" "4.6.0"

"coa@^2.0.2":
  "integrity" "sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA=="
  "resolved" "https://registry.npmjs.org/coa/-/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"collect-v8-coverage@^1.0.0":
  "integrity" "sha512-iBPtljfCNcTKNAto0KEtDfZ3qzjJvqE3aTGZsbhjSBlorqpXJlaWWtPO35D+ZImoC3KWejX64o+yPGxhWSTzfg=="
  "resolved" "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.1.tgz"
  "version" "1.0.1"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.1.4", "color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"colord@^2.9.1":
  "integrity" "sha512-Uqbg+J445nc1TKn4FoDPS6ZZqAvEDnwrH42yo8B40JSOgSLxMZ/gt3h4nmCtPLQeXhjJJkqBx7SCY35WnIixaQ=="
  "resolved" "https://registry.npmjs.org/colord/-/colord-2.9.2.tgz"
  "version" "2.9.2"

"colorette@^2.0.10":
  "integrity" "sha512-hUewv7oMjCp+wkBv5Rm0v87eJhq4woh5rSR+42YSQJKecCqgIqNkZ6lAlQms/BwHPJA5NKMRlpxPRv0n8HQW6g=="
  "resolved" "https://registry.npmjs.org/colorette/-/colorette-2.0.16.tgz"
  "version" "2.0.16"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.20.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^7.2.0":
  "integrity" "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz"
  "version" "7.2.0"

"commander@^8.3.0":
  "integrity" "sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz"
  "version" "8.3.0"

"common-path-prefix@^3.0.0":
  "integrity" "sha512-QE33hToZseCH3jS0qN96O/bSh3kaw/h+Tq7ngyY9eWDUnTlTNUyqfqvCXioLe5Na5jFsL78ra/wuBU4iuEgd4w=="
  "resolved" "https://registry.npmjs.org/common-path-prefix/-/common-path-prefix-3.0.0.tgz"
  "version" "3.0.0"

"common-tags@^1.8.0":
  "integrity" "sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA=="
  "resolved" "https://registry.npmjs.org/common-tags/-/common-tags-1.8.2.tgz"
  "version" "1.8.2"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"compressible@~2.0.16":
  "integrity" "sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg=="
  "resolved" "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.4":
  "integrity" "sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ=="
  "resolved" "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"confusing-browser-globals@^1.0.11":
  "integrity" "sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA=="
  "resolved" "https://registry.npmjs.org/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz"
  "version" "1.0.11"

"connect-history-api-fallback@^1.6.0":
  "integrity" "sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg=="
  "resolved" "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"content-disposition@0.5.4":
  "integrity" "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ=="
  "resolved" "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@~1.0.4":
  "integrity" "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA=="
  "resolved" "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.4.0", "convert-source-map@^1.5.0", "convert-source-map@^1.6.0", "convert-source-map@^1.7.0":
  "integrity" "sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.4.1":
  "integrity" "sha512-ZwrFkGJxUR3EIoXtO+yVE69Eb7KlixbaeAWfBQB9vVsNn/o+Yw69gBWSSDK825hQNdN+wF8zELf3dFNl/kxkUA=="
  "resolved" "https://registry.npmjs.org/cookie/-/cookie-0.4.1.tgz"
  "version" "0.4.1"

"core-js-compat@^3.20.0", "core-js-compat@^3.20.2":
  "integrity" "sha512-c8M5h0IkNZ+I92QhIpuSijOxGAcj3lgpsWdkCqmUTZNwidujF4r3pi6x1DCN+Vcs5qTS2XWWMfWSuCqyupX8gw=="
  "resolved" "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.20.3.tgz"
  "version" "3.20.3"
  dependencies:
    "browserslist" "^4.19.1"
    "semver" "7.0.0"

"core-js-pure@^3.20.2", "core-js-pure@^3.8.1":
  "integrity" "sha512-Q2H6tQ5MtPtcC7f3HxJ48i4Q7T9ybPKgvWyuH7JXIoNa2pm0KuBnycsET/qw1SLLZYfbsbrZQNMeIOClb+6WIA=="
  "resolved" "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.20.3.tgz"
  "version" "3.20.3"

"core-js@^3.19.2":
  "integrity" "sha512-vVl8j8ph6tRS3B8qir40H7yw7voy17xL0piAjlbBUsH7WIfzoedL/ZOr1OV9FyZQLWXsayOJyV4tnRyXR85/ag=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.20.3.tgz"
  "version" "3.20.3"

"core-js@3.6.5":
  "integrity" "sha512-vZVEEwZoIsI+vPEuoF9Iqf5H7/M3eeQqWlQnYa8FSKKePuYTf5MWnxb5SDAzCa60b3JBRS5g9b+Dq7b1y/RCrA=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.6.5.tgz"
  "version" "3.6.5"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cosmiconfig@^6.0.0":
  "integrity" "sha512-xb3ZL6+L8b9JLLCx3ZdoZy4+2ECphCMo2PwqgP1tlfVq6M6YReyzBJtvWWtbDSpNr9hn96pkCiZqUcFEc+54Qg=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.1.0"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.7.2"

"cosmiconfig@^7.0.0", "cosmiconfig@^7.0.1":
  "integrity" "sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.2.1"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.10.0"

"create-react-class@^15.7.0":
  "integrity" "sha512-QZv4sFWG9S5RUvkTYWbflxeZX+JG7Cz0Tn33rQBJ+WFQTqTfUTjMjiv9tnfXazjsO5r0KhPs+AqCjyrQX6h2ng=="
  "resolved" "https://registry.npmjs.org/create-react-class/-/create-react-class-15.7.0.tgz"
  "version" "15.7.0"
  dependencies:
    "loose-envify" "^1.3.1"
    "object-assign" "^4.1.1"

"cross-fetch@^3.0.4":
  "integrity" "sha512-1eAtFWdIubi6T4XPy6ei9iUFoKpUkIF971QLN8lIvvvwueI65+Nw5haMNKUwfJxabqlIIDODJKGrQ66gxC0PbQ=="
  "resolved" "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "node-fetch" "2.6.1"

"cross-spawn@^7.0.2", "cross-spawn@^7.0.3":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypto-hash@^2.0.1":
  "integrity" "sha512-t4mkp7Vh6MuCZRBf0XLzBOfhkH3nW6YEAotMDSjshVQ1GffCMGdPLSr7pKH0rdXY02jTjAZ7QW2apD0buaZXcQ=="
  "resolved" "https://registry.npmjs.org/crypto-hash/-/crypto-hash-2.0.1.tgz"
  "version" "2.0.1"

"crypto-random-string@^2.0.0":
  "integrity" "sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA=="
  "resolved" "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz"
  "version" "2.0.0"

"css-blank-pseudo@^3.0.2":
  "integrity" "sha512-hOb1LFjRR+8ocA071xUSmg5VslJ8NGo/I2qpUpdeAYyBVCgupS5O8SEVo4SxEMYyFBNodBkzG3T1iqW9HCXxew=="
  "resolved" "https://registry.npmjs.org/css-blank-pseudo/-/css-blank-pseudo-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "postcss-selector-parser" "^6.0.8"

"css-declaration-sorter@^6.0.3":
  "integrity" "sha512-lpfkqS0fctcmZotJGhnxkIyJWvBXgpyi2wsFd4J8VB7wzyrT6Ch/3Q+FMNJpjK4gu1+GN5khOnpU2ZVKrLbhCw=="
  "resolved" "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-6.1.4.tgz"
  "version" "6.1.4"
  dependencies:
    "timsort" "^0.3.0"

"css-has-pseudo@^3.0.3":
  "integrity" "sha512-0gDYWEKaGacwxCqvQ3Ypg6wGdD1AztbMm5h1JsactG2hP2eiflj808QITmuWBpE7sjSEVrAlZhPTVd/nNMj/hQ=="
  "resolved" "https://registry.npmjs.org/css-has-pseudo/-/css-has-pseudo-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "postcss-selector-parser" "^6.0.8"

"css-in-js-utils@^2.0.0":
  "integrity" "sha512-PJF0SpJT+WdbVVt0AOYp9C8GnuruRlL/UFW7932nLWmFLQTaWEzTBQEx7/hn4BuV+WON75iAViSUJLiU3PKbpA=="
  "resolved" "https://registry.npmjs.org/css-in-js-utils/-/css-in-js-utils-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "hyphenate-style-name" "^1.0.2"
    "isobject" "^3.0.1"

"css-loader@^6.5.1":
  "integrity" "sha512-gEy2w9AnJNnD9Kuo4XAP9VflW/ujKoS9c/syO+uWMlm5igc7LysKzPXaDoR2vroROkSwsTS2tGr1yGGEbZOYZQ=="
  "resolved" "https://registry.npmjs.org/css-loader/-/css-loader-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "icss-utils" "^5.1.0"
    "postcss" "^8.2.15"
    "postcss-modules-extract-imports" "^3.0.0"
    "postcss-modules-local-by-default" "^4.0.0"
    "postcss-modules-scope" "^3.0.0"
    "postcss-modules-values" "^4.0.0"
    "postcss-value-parser" "^4.1.0"
    "semver" "^7.3.5"

"css-minimizer-webpack-plugin@^3.2.0":
  "integrity" "sha512-SHA7Hu/EiF0dOwdmV2+agvqYpG+ljlUa7Dvn1AVOmSH3N8KOERoaM9lGpstz9nGsoTjANGyUXdrxl/EwdMScRg=="
  "resolved" "https://registry.npmjs.org/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "cssnano" "^5.0.6"
    "jest-worker" "^27.0.2"
    "postcss" "^8.3.5"
    "schema-utils" "^4.0.0"
    "serialize-javascript" "^6.0.0"
    "source-map" "^0.6.1"

"css-prefers-color-scheme@^6.0.2":
  "integrity" "sha512-gv0KQBEM+q/XdoKyznovq3KW7ocO7k+FhPP+hQR1MenJdu0uPGS6IZa9PzlbqBeS6XcZJNAoqoFxlAUW461CrA=="
  "resolved" "https://registry.npmjs.org/css-prefers-color-scheme/-/css-prefers-color-scheme-6.0.2.tgz"
  "version" "6.0.2"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w=="
  "resolved" "https://registry.npmjs.org/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^2.0.0":
  "integrity" "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ=="
  "resolved" "https://registry.npmjs.org/css-select/-/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-select@^4.1.3":
  "integrity" "sha512-/aUslKhzkTNCQUB2qTX84lVmfia9NyjP3WpDGtj/WxhwBzWBYUV3DgUpurHTme8UTPcPlAD1DJ+b0nN/t50zDQ=="
  "resolved" "https://registry.npmjs.org/css-select/-/css-select-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^5.1.0"
    "domhandler" "^4.3.0"
    "domutils" "^2.8.0"
    "nth-check" "^2.0.1"

"css-tree@^1.1.2":
  "integrity" "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q=="
  "resolved" "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-tree@^1.1.3":
  "integrity" "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q=="
  "resolved" "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg=="
  "resolved" "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-what@^3.2.1":
  "integrity" "sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ=="
  "resolved" "https://registry.npmjs.org/css-what/-/css-what-3.4.2.tgz"
  "version" "3.4.2"

"css-what@^5.1.0":
  "integrity" "sha512-arSMRWIIFY0hV8pIxZMEfmMI47Wj3R/aWpZDDxWYCPEiOMv6tfOrnpDtgxBYPEQD4V0Y/958+1TdC3iWTFcUPw=="
  "resolved" "https://registry.npmjs.org/css-what/-/css-what-5.1.0.tgz"
  "version" "5.1.0"

"css.escape@^1.5.1":
  "integrity" "sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s="
  "resolved" "https://registry.npmjs.org/css.escape/-/css.escape-1.5.1.tgz"
  "version" "1.5.1"

"css@^3.0.0":
  "integrity" "sha512-DG9pFfwOrzc+hawpmqX/dHYHJG+Bsdb0klhyi1sDneOgGOXy9wQIC8hzyVp1e4NRYDBdxcylvywPkkXCHAzTyQ=="
  "resolved" "https://registry.npmjs.org/css/-/css-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "inherits" "^2.0.4"
    "source-map" "^0.6.1"
    "source-map-resolve" "^0.6.0"

"cssdb@^5.0.0":
  "integrity" "sha512-/vqjXhv1x9eGkE/zO6o8ZOI7dgdZbLVLUGyVRbPgk6YipXbW87YzUCcO+Jrmi5bwJlAH6oD+MNeZyRgXea1GZw=="
  "resolved" "https://registry.npmjs.org/cssdb/-/cssdb-5.1.0.tgz"
  "version" "5.1.0"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@^5.1.10":
  "integrity" "sha512-BcpSzUVygHMOnp9uG5rfPzTOCb0GAHQkqtUQx8j1oMNF9A1Q8hziOOhiM4bdICpmrBIU85BE64RD5XGYsVQZNA=="
  "resolved" "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-5.1.10.tgz"
  "version" "5.1.10"
  dependencies:
    "css-declaration-sorter" "^6.0.3"
    "cssnano-utils" "^3.0.0"
    "postcss-calc" "^8.2.0"
    "postcss-colormin" "^5.2.3"
    "postcss-convert-values" "^5.0.2"
    "postcss-discard-comments" "^5.0.1"
    "postcss-discard-duplicates" "^5.0.1"
    "postcss-discard-empty" "^5.0.1"
    "postcss-discard-overridden" "^5.0.2"
    "postcss-merge-longhand" "^5.0.4"
    "postcss-merge-rules" "^5.0.4"
    "postcss-minify-font-values" "^5.0.2"
    "postcss-minify-gradients" "^5.0.4"
    "postcss-minify-params" "^5.0.3"
    "postcss-minify-selectors" "^5.1.1"
    "postcss-normalize-charset" "^5.0.1"
    "postcss-normalize-display-values" "^5.0.2"
    "postcss-normalize-positions" "^5.0.2"
    "postcss-normalize-repeat-style" "^5.0.2"
    "postcss-normalize-string" "^5.0.2"
    "postcss-normalize-timing-functions" "^5.0.2"
    "postcss-normalize-unicode" "^5.0.2"
    "postcss-normalize-url" "^5.0.4"
    "postcss-normalize-whitespace" "^5.0.2"
    "postcss-ordered-values" "^5.0.3"
    "postcss-reduce-initial" "^5.0.2"
    "postcss-reduce-transforms" "^5.0.2"
    "postcss-svgo" "^5.0.3"
    "postcss-unique-selectors" "^5.0.2"

"cssnano-utils@^3.0.0":
  "integrity" "sha512-Pzs7/BZ6OgT+tXXuF12DKR8SmSbzUeVYCtMBbS8lI0uAm3mrYmkyqCXXPsQESI6kmLfEVBppbdVY/el3hg3nAA=="
  "resolved" "https://registry.npmjs.org/cssnano-utils/-/cssnano-utils-3.0.0.tgz"
  "version" "3.0.0"

"cssnano@^5.0.6":
  "integrity" "sha512-ppZsS7oPpi2sfiyV5+i+NbB/3GtQ+ab2Vs1azrZaXWujUSN4o+WdTxlCZIMcT9yLW3VO/5yX3vpyDaQ1nIn8CQ=="
  "resolved" "https://registry.npmjs.org/cssnano/-/cssnano-5.0.15.tgz"
  "version" "5.0.15"
  dependencies:
    "cssnano-preset-default" "^5.1.10"
    "lilconfig" "^2.0.3"
    "yaml" "^1.10.2"

"csso@^4.0.2", "csso@^4.2.0":
  "integrity" "sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA=="
  "resolved" "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"cssom@^0.4.4":
  "integrity" "sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw=="
  "resolved" "https://registry.npmjs.org/cssom/-/cssom-0.4.4.tgz"
  "version" "0.4.4"

"cssom@~0.3.6":
  "integrity" "sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg=="
  "resolved" "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz"
  "version" "0.3.8"

"cssstyle@^2.3.0":
  "integrity" "sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A=="
  "resolved" "https://registry.npmjs.org/cssstyle/-/cssstyle-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cssom" "~0.3.6"

"csstype@^3.0.2", "csstype@^3.1.0":
  "integrity" "sha512-uX1KG+x9h5hIJsaKR9xHUeUraxf8IODOwq9JLNPq6BwB04a/xgpq3rcx47l5BZu5zBPlgD342tdke3Hom/nJRA=="
  "resolved" "https://registry.npmjs.org/csstype/-/csstype-3.1.0.tgz"
  "version" "3.1.0"

"damerau-levenshtein@^1.0.7":
  "integrity" "sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA=="
  "resolved" "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
  "version" "1.0.8"

"data-urls@^2.0.0":
  "integrity" "sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ=="
  "resolved" "https://registry.npmjs.org/data-urls/-/data-urls-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "abab" "^2.0.3"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.0.0"

"debug@^2.6.0":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.1":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.7":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.2", "debug@4":
  "integrity" "sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decimal.js@^10.2.1":
  "integrity" "sha512-V0pfhfr8suzyPGOx3nmq4aHqabehUZn6Ch9kyFpV79TGDTWFmHqUqXdabR7QHqxzrYolF4+tVmJhUG4OURg5dQ=="
  "resolved" "https://registry.npmjs.org/decimal.js/-/decimal.js-10.3.1.tgz"
  "version" "10.3.1"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"dedent@^0.7.0":
  "integrity" "sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw="
  "resolved" "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz"
  "version" "0.7.0"

"deep-equal@^1.0.1":
  "integrity" "sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g=="
  "resolved" "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@^0.1.3", "deep-is@~0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^4.2.2":
  "integrity" "sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.2.tgz"
  "version" "4.2.2"

"default-gateway@^6.0.3":
  "integrity" "sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg=="
  "resolved" "https://registry.npmjs.org/default-gateway/-/default-gateway-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "execa" "^5.0.0"

"define-lazy-prop@^2.0.0":
  "integrity" "sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og=="
  "resolved" "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  "version" "2.0.0"

"define-properties@^1.1.3":
  "integrity" "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ=="
  "resolved" "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"defined@^1.0.0":
  "integrity" "sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM="
  "resolved" "https://registry.npmjs.org/defined/-/defined-1.0.0.tgz"
  "version" "1.0.0"

"del@^6.0.0":
  "integrity" "sha512-1shh9DQ23L16oXSZKB2JxpL7iMy2E0S9d517ptA1P8iw0alkPtQcrKH7ru31rYtKwF499HkTu+DRzq3TCKDFRQ=="
  "resolved" "https://registry.npmjs.org/del/-/del-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "globby" "^11.0.1"
    "graceful-fs" "^4.2.4"
    "is-glob" "^4.0.1"
    "is-path-cwd" "^2.2.0"
    "is-path-inside" "^3.0.2"
    "p-map" "^4.0.0"
    "rimraf" "^3.0.2"
    "slash" "^3.0.0"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"dequal@^2.0.2":
  "integrity" "sha512-q9K8BlJVxK7hQYqa6XISGmBZbtQQWVXSrRrWreHC94rMt1QL/Impruc+7p2CYSYuVIUr+YCt6hjrs1kkdJRTug=="
  "resolved" "https://registry.npmjs.org/dequal/-/dequal-2.0.2.tgz"
  "version" "2.0.2"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-newline@^3.0.0":
  "integrity" "sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA=="
  "resolved" "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz"
  "version" "3.1.0"

"detect-node@^2.0.4":
  "integrity" "sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g=="
  "resolved" "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz"
  "version" "2.1.0"

"detect-port-alt@^1.1.6":
  "integrity" "sha512-5tQykt+LqfJFBEYaDITx7S7cR7mJ/zQmLXZ2qt5w04ainYZw6tBf9dBunMjVeVOdYVRUzUOE4HkY5J7+uttb5Q=="
  "resolved" "https://registry.npmjs.org/detect-port-alt/-/detect-port-alt-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "address" "^1.0.1"
    "debug" "^2.6.0"

"detective@^5.2.0":
  "integrity" "sha512-6SsIx+nUUbuK0EthKjv0zrdnajCCXVYGmbYYiYjFVpzcjwEs/JMDZ8tPRG29J/HhN56t3GJp2cGSWDRjjot8Pg=="
  "resolved" "https://registry.npmjs.org/detective/-/detective-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "acorn-node" "^1.6.1"
    "defined" "^1.0.0"
    "minimist" "^1.1.1"

"didyoumean@^1.2.2":
  "integrity" "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw=="
  "resolved" "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  "version" "1.2.2"

"diff-sequences@^27.4.0":
  "integrity" "sha512-YqiQzkrsmHMH5uuh8OdQFU9/ZpADnwzml8z0O5HvRNda+5UZsaX/xN+AAxfR2hWq1Y7HZnAzO9J5lJXOuDz2Ww=="
  "resolved" "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.4.0.tgz"
  "version" "27.4.0"

"dir-glob@^3.0.1":
  "integrity" "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA=="
  "resolved" "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"dlv@^1.1.3":
  "integrity" "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA=="
  "resolved" "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  "version" "1.1.3"

"dns-equal@^1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha512-BQ6F4vycLXBvdrJZ6S3gZewt6rcrks9KBgM9vrhW+knGRqc8uEdT7fuCwloc7nny5xNoMJ17HGH0R/6fpo8ECA=="
  "resolved" "https://registry.npmjs.org/dns-packet/-/dns-packet-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://registry.npmjs.org/dns-txt/-/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"doctrine@^2.1.0":
  "integrity" "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-accessibility-api@^0.5.6", "dom-accessibility-api@^0.5.9":
  "integrity" "sha512-Xu9mD0UjrJisTmv7lmVSDMagQcU9R5hwAbxsaAE/35XPnPLJobbuREfV/rraiSaEj/UOvgrzQs66zyTWTlyd+g=="
  "resolved" "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.10.tgz"
  "version" "0.5.10"

"dom-converter@^0.2.0":
  "integrity" "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA=="
  "resolved" "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-helpers@^5.0.1", "dom-helpers@^5.2.0", "dom-helpers@^5.2.1":
  "integrity" "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA=="
  "resolved" "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "@babel/runtime" "^7.8.7"
    "csstype" "^3.0.2"

"dom-serializer@^1.0.1":
  "integrity" "sha512-5c54Bk5Dw4qAxNOI1pFEizPSjVsx5+bpJKmL2kPn8JhBUq2q09tTCa3mjijun2NfK78NMouDYNMBkOrPZiS+ig=="
  "resolved" "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"dom-serializer@0":
  "integrity" "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g=="
  "resolved" "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha512-DtBMo82pv1dFtUmHyr48beiuq792Sxohr+8Hm9zoxklYPfa6n0Z3Byjj2IV7bmr2IyqClnqEQhfgHJJ5QF0R5A=="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-2.2.0.tgz"
  "version" "2.2.0"

"domelementtype@1":
  "integrity" "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w=="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domexception@^2.0.1":
  "integrity" "sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg=="
  "resolved" "https://registry.npmjs.org/domexception/-/domexception-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "webidl-conversions" "^5.0.0"

"domhandler@^4.0.0", "domhandler@^4.2.0", "domhandler@^4.3.0":
  "integrity" "sha512-fC0aXNQXqKSFTr2wDNZDhsEYjCiYsDWl3D01kwt25hm1YIPyDGHvvi3rw+PLqHAl/m71MaiF7d5zvBr0p5UB2g=="
  "resolved" "https://registry.npmjs.org/domhandler/-/domhandler-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "domelementtype" "^2.2.0"

"domutils@^1.7.0":
  "integrity" "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg=="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@^2.5.2", "domutils@^2.8.0":
  "integrity" "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A=="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"dot-case@^3.0.4":
  "integrity" "sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w=="
  "resolved" "https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"

"dotenv-expand@^5.1.0":
  "integrity" "sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA=="
  "resolved" "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  "version" "5.1.0"

"dotenv@^10.0.0":
  "integrity" "sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q=="
  "resolved" "https://registry.npmjs.org/dotenv/-/dotenv-10.0.0.tgz"
  "version" "10.0.0"

"duplexer@^0.1.2":
  "integrity" "sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg=="
  "resolved" "https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz"
  "version" "0.1.2"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^3.1.6":
  "integrity" "sha512-9lt9Zse4hPucPkoP7FHDF0LQAlGyF9JVpnClFLFH3aSSbxmyoqINRpp/9wePWJTUl4KOQwRL72Iw3InHPDkoGw=="
  "resolved" "https://registry.npmjs.org/ejs/-/ejs-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "jake" "^10.6.1"

"electron-to-chromium@^1.4.17":
  "integrity" "sha512-ZHc8i3/cgeCRK/vC7W2htAG6JqUmOUgDNn/f9yY9J8UjfLjwzwOVEt4MWmgJAdvmxyrsR5KIFA/6+kUHGY0eUA=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.47.tgz"
  "version" "1.4.47"

"emittery@^0.8.1":
  "integrity" "sha512-uDfvUjVrfGJJhymx/kz6prltenw1u7WrCg1oa94zYY8xxVpLLUu045LAT0dhDZdXG58/EpPL/5kA180fQ/qudg=="
  "resolved" "https://registry.npmjs.org/emittery/-/emittery-0.8.1.tgz"
  "version" "0.8.1"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emoji-regex@^9.2.2":
  "integrity" "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  "version" "9.2.2"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"enhanced-resolve@^5.8.3":
  "integrity" "sha512-EGAbGvH7j7Xt2nc0E7D99La1OiEs8LnyimkRgwExpUMScN6O+3x9tIWs7PLQZVNx4YD+00skHXPXi1yQHpAmZA=="
  "resolved" "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.8.3.tgz"
  "version" "5.8.3"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"entities@^2.0.0":
  "integrity" "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz"
  "version" "2.2.0"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.6":
  "integrity" "sha512-d51brTeqC+BHlwF0BhPtcYgF5nlzf9ZZ0ZIUQNZpc9ZB9qw5IJ2diTrBY9jlCJkTLITYPjmiX6OWCwH+fuyNgQ=="
  "resolved" "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "stackframe" "^1.1.1"

"es-abstract@^1.17.2", "es-abstract@^1.19.0", "es-abstract@^1.19.1":
  "integrity" "sha512-2vJ6tjA/UfqLm2MPs7jxVybLoB8i1t1Jd9R3kISld20sIxPcTbLuggQOUxeWeAvIUkduv/CfMjuh4WmiXr2v9w=="
  "resolved" "https://registry.npmjs.org/es-abstract/-/es-abstract-1.19.1.tgz"
  "version" "1.19.1"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.1.1"
    "get-symbol-description" "^1.0.0"
    "has" "^1.0.3"
    "has-symbols" "^1.0.2"
    "internal-slot" "^1.0.3"
    "is-callable" "^1.2.4"
    "is-negative-zero" "^2.0.1"
    "is-regex" "^1.1.4"
    "is-shared-array-buffer" "^1.0.1"
    "is-string" "^1.0.7"
    "is-weakref" "^1.0.1"
    "object-inspect" "^1.11.0"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.2"
    "string.prototype.trimend" "^1.0.4"
    "string.prototype.trimstart" "^1.0.4"
    "unbox-primitive" "^1.0.1"

"es-module-lexer@^0.9.0":
  "integrity" "sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ=="
  "resolved" "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.9.3.tgz"
  "version" "0.9.3"

"es-to-primitive@^1.2.1":
  "integrity" "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA=="
  "resolved" "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^2.0.0":
  "integrity" "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  "version" "2.0.0"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"escodegen@^2.0.0":
  "integrity" "sha512-mmHKys/C8BFUGI+MAWNcSYoORYLMdPzjrknd2Vc+bUsjN5bXcr8EhrNB+UTqfL1y3I9c4fw2ihgtMPQLBRiQxw=="
  "resolved" "https://registry.npmjs.org/escodegen/-/escodegen-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "esprima" "^4.0.1"
    "estraverse" "^5.2.0"
    "esutils" "^2.0.2"
    "optionator" "^0.8.1"
  optionalDependencies:
    "source-map" "~0.6.1"

"eslint-config-react-app@^7.0.0":
  "integrity" "sha512-xyymoxtIt1EOsSaGag+/jmcywRuieQoA2JbPCjnw9HukFj9/97aGPoZVFioaotzk1K5Qt9sHO5EutZbkrAXS0g=="
  "resolved" "https://registry.npmjs.org/eslint-config-react-app/-/eslint-config-react-app-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/eslint-parser" "^7.16.3"
    "@rushstack/eslint-patch" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^5.5.0"
    "@typescript-eslint/parser" "^5.5.0"
    "babel-preset-react-app" "^10.0.1"
    "confusing-browser-globals" "^1.0.11"
    "eslint-plugin-flowtype" "^8.0.3"
    "eslint-plugin-import" "^2.25.3"
    "eslint-plugin-jest" "^25.3.0"
    "eslint-plugin-jsx-a11y" "^6.5.1"
    "eslint-plugin-react" "^7.27.1"
    "eslint-plugin-react-hooks" "^4.3.0"
    "eslint-plugin-testing-library" "^5.0.1"

"eslint-import-resolver-node@^0.3.6":
  "integrity" "sha512-0En0w03NRVMn9Uiyn8YRPDKvWjxCWkslUEhGNTdGx15RvPJYQ+lbOlqrlNI2vEAs4pDYK4f/HN2TbDmk5TP0iw=="
  "resolved" "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "debug" "^3.2.7"
    "resolve" "^1.20.0"

"eslint-module-utils@^2.7.2":
  "integrity" "sha512-zquepFnWCY2ISMFwD/DqzaM++H+7PDzOpUvotJWm/y1BAFt5R4oeULgdrTejKqLkz7MA/tgstsUMNYc7wNdTrg=="
  "resolved" "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.7.2.tgz"
  "version" "2.7.2"
  dependencies:
    "debug" "^3.2.7"
    "find-up" "^2.1.0"

"eslint-plugin-flowtype@^8.0.3":
  "integrity" "sha512-dX8l6qUL6O+fYPtpNRideCFSpmWOUVx5QcaGLVqe/vlDiBSe4vYljDWDETwnyFzpl7By/WVIu6rcrniCgH9BqQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-flowtype/-/eslint-plugin-flowtype-8.0.3.tgz"
  "version" "8.0.3"
  dependencies:
    "lodash" "^4.17.21"
    "string-natural-compare" "^3.0.1"

"eslint-plugin-import@^2.25.3":
  "integrity" "sha512-/KJBASVFxpu0xg1kIBn9AUa8hQVnszpwgE7Ld0lKAlx7Ie87yzEzCgSkekt+le/YVhiaosO4Y14GDAOc41nfxA=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.25.4.tgz"
  "version" "2.25.4"
  dependencies:
    "array-includes" "^3.1.4"
    "array.prototype.flat" "^1.2.5"
    "debug" "^2.6.9"
    "doctrine" "^2.1.0"
    "eslint-import-resolver-node" "^0.3.6"
    "eslint-module-utils" "^2.7.2"
    "has" "^1.0.3"
    "is-core-module" "^2.8.0"
    "is-glob" "^4.0.3"
    "minimatch" "^3.0.4"
    "object.values" "^1.1.5"
    "resolve" "^1.20.0"
    "tsconfig-paths" "^3.12.0"

"eslint-plugin-jest@^25.3.0":
  "integrity" "sha512-PWLUEXeeF7C9QGKqvdSbzLOiLTx+bno7/HC9eefePfEb257QFHg7ye3dh80AZVkaa/RQsBB1Q/ORQvg2X7F0NQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-jest/-/eslint-plugin-jest-25.7.0.tgz"
  "version" "25.7.0"
  dependencies:
    "@typescript-eslint/experimental-utils" "^5.0.0"

"eslint-plugin-jsx-a11y@^6.5.1":
  "integrity" "sha512-sVCFKX9fllURnXT2JwLN5Qgo24Ug5NF6dxhkmxsMEUZhXRcGg+X3e1JbJ84YePQKBl5E0ZjAH5Q4rkdcGY99+g=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "@babel/runtime" "^7.16.3"
    "aria-query" "^4.2.2"
    "array-includes" "^3.1.4"
    "ast-types-flow" "^0.0.7"
    "axe-core" "^4.3.5"
    "axobject-query" "^2.2.0"
    "damerau-levenshtein" "^1.0.7"
    "emoji-regex" "^9.2.2"
    "has" "^1.0.3"
    "jsx-ast-utils" "^3.2.1"
    "language-tags" "^1.0.5"
    "minimatch" "^3.0.4"

"eslint-plugin-react-hooks@^4.3.0":
  "integrity" "sha512-XslZy0LnMn+84NEG9jSGR6eGqaZB3133L8xewQo3fQagbQuGt7a63gf+P1NGKZavEYEC3UXaWEAA/AqDkuN6xA=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.3.0.tgz"
  "version" "4.3.0"

"eslint-plugin-react@^7.27.1":
  "integrity" "sha512-IOlFIRHzWfEQQKcAD4iyYDndHwTQiCMcJVJjxempf203jnNLUnW34AXLrV33+nEXoifJE2ZEGmcjKPL8957eSw=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.28.0.tgz"
  "version" "7.28.0"
  dependencies:
    "array-includes" "^3.1.4"
    "array.prototype.flatmap" "^1.2.5"
    "doctrine" "^2.1.0"
    "estraverse" "^5.3.0"
    "jsx-ast-utils" "^2.4.1 || ^3.0.0"
    "minimatch" "^3.0.4"
    "object.entries" "^1.1.5"
    "object.fromentries" "^2.0.5"
    "object.hasown" "^1.1.0"
    "object.values" "^1.1.5"
    "prop-types" "^15.7.2"
    "resolve" "^2.0.0-next.3"
    "semver" "^6.3.0"
    "string.prototype.matchall" "^4.0.6"

"eslint-plugin-testing-library@^5.0.1":
  "integrity" "sha512-tKZ9G+HnIOnYAhXeoBCiAT8LOdU3m1VquBTKsBW/5zAaB30vq7gC60DIayPfMJt8EZBlqPVzGqSN57sIFmTunQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-testing-library/-/eslint-plugin-testing-library-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "@typescript-eslint/experimental-utils" "^5.9.0"

"eslint-scope@^5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-scope@^7.1.0":
  "integrity" "sha512-aWwkhnS0qAXqNOgKOK0dJ2nvzEbhEvpy8OlJ9kZ0FeZnA6zpjv1/Vei+puGFFX7zkPCkHHXb7IDX3A+7yPrRWg=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-scope@5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-utils@^3.0.0":
  "integrity" "sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA=="
  "resolved" "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "eslint-visitor-keys" "^2.0.0"

"eslint-visitor-keys@^2.0.0":
  "integrity" "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint-visitor-keys@^2.1.0":
  "integrity" "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint-visitor-keys@^3.0.0", "eslint-visitor-keys@^3.1.0", "eslint-visitor-keys@^3.2.0":
  "integrity" "sha512-IOzT0X126zn7ALX0dwFiUQEdsfzrm4+ISsQS8nukaJXwEyYKRSnEIIDULYg1mCtGp7UUXgfGl7BIolXREQK+XQ=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.2.0.tgz"
  "version" "3.2.0"

"eslint-webpack-plugin@^3.1.1":
  "integrity" "sha512-xSucskTN9tOkfW7so4EaiFIkulWLXwCB/15H917lR6pTv0Zot6/fetFucmENRb7J5whVSFKIvwnrnsa78SG2yg=="
  "resolved" "https://registry.npmjs.org/eslint-webpack-plugin/-/eslint-webpack-plugin-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/eslint" "^7.28.2"
    "jest-worker" "^27.3.1"
    "micromatch" "^4.0.4"
    "normalize-path" "^3.0.0"
    "schema-utils" "^3.1.1"

"eslint@*", "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0", "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^7.0.0 || ^8.0.0", "eslint@^7.5.0 || ^8.0.0", "eslint@^8.0.0", "eslint@^8.1.0", "eslint@^8.3.0", "eslint@>= 6", "eslint@>=5":
  "integrity" "sha512-ifHYzkBGrzS2iDU7KjhCAVMGCvF6M3Xfs8X8b37cgrUlDt6bWRTpRh6T/gtSXv1HJ/BUGgmjvNvOEGu85Iif7w=="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-8.7.0.tgz"
  "version" "8.7.0"
  dependencies:
    "@eslint/eslintrc" "^1.0.5"
    "@humanwhocodes/config-array" "^0.9.2"
    "ajv" "^6.10.0"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.3.2"
    "doctrine" "^3.0.0"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^7.1.0"
    "eslint-utils" "^3.0.0"
    "eslint-visitor-keys" "^3.2.0"
    "espree" "^9.3.0"
    "esquery" "^1.4.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^6.0.1"
    "globals" "^13.6.0"
    "ignore" "^5.2.0"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "js-yaml" "^4.1.0"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.0.4"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.1"
    "regexpp" "^3.2.0"
    "strip-ansi" "^6.0.1"
    "strip-json-comments" "^3.1.0"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^9.2.0", "espree@^9.3.0":
  "integrity" "sha512-d/5nCsb0JcqsSEeQzFZ8DH1RmxPcglRWh24EFTlUEmCKoehXGdpsx0RkHDubqUI8LSAIKMQp4r9SzQ3n+sm4HQ=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-9.3.0.tgz"
  "version" "9.3.0"
  dependencies:
    "acorn" "^8.7.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^3.1.0"

"esprima@^4.0.0", "esprima@^4.0.1":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0":
  "integrity" "sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0", "estraverse@^5.2.0", "estraverse@^5.3.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estree-walker@^1.0.1":
  "integrity" "sha512-1fMXF3YP4pZZVozF8j/ZLfvnR8NSIljt56UhbZ5PeeDmmGHpgpdwQt7ITlGvYaQukCvuBRMLEiKiYC+oeIg4cg=="
  "resolved" "https://registry.npmjs.org/estree-walker/-/estree-walker-1.0.1.tgz"
  "version" "1.0.1"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"eventemitter3@^4.0.0":
  "integrity" "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.2.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"execa@^5.0.0":
  "integrity" "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"exit@^0.1.2":
  "integrity" "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw="
  "resolved" "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"
  "version" "0.1.2"

"expect@^27.4.6":
  "integrity" "sha512-1M/0kAALIaj5LaG66sFJTbRsWTADnylly82cu4bspI0nl+pgP4E6Bh/aqdHlTUjul06K7xQnnrAoqfxVU0+/ag=="
  "resolved" "https://registry.npmjs.org/expect/-/expect-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "jest-get-type" "^27.4.0"
    "jest-matcher-utils" "^27.4.6"
    "jest-message-util" "^27.4.6"

"express@^4.17.1":
  "integrity" "sha512-oxlxJxcQlYwqPWKVJJtvQiwHgosH/LrLSPA+H4UxpyvSS6jC5aH+5MoHFM+KABgTOt0APue4w66Ha8jCUo9QGg=="
  "resolved" "https://registry.npmjs.org/express/-/express-4.17.2.tgz"
  "version" "4.17.2"
  dependencies:
    "accepts" "~1.3.7"
    "array-flatten" "1.1.1"
    "body-parser" "1.19.1"
    "content-disposition" "0.5.4"
    "content-type" "~1.0.4"
    "cookie" "0.4.1"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "~1.1.2"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.7"
    "qs" "6.9.6"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.2.1"
    "send" "0.17.2"
    "serve-static" "1.14.2"
    "setprototypeof" "1.2.0"
    "statuses" "~1.5.0"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-glob@^3.2.7", "fast-glob@^3.2.9":
  "integrity" "sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew=="
  "resolved" "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.11.tgz"
  "version" "3.2.11"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0", "fast-json-stable-stringify@^2.1.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6", "fast-levenshtein@~2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw=="
  "resolved" "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "reusify" "^1.0.4"

"faye-websocket@^0.11.3", "faye-websocket@0.11.4":
  "integrity" "sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g=="
  "resolved" "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "websocket-driver" ">=0.5.1"

"fb-watchman@^2.0.0":
  "integrity" "sha512-DkPJKQeY6kKwmuMretBhr7G6Vodr7bFwDYTXIkfG1gjvNpaxBTQV3PbXg6bR1c1UP4jPOX0jHUbbHANL9vRjVg=="
  "resolved" "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "bser" "2.1.1"

"fbjs-css-vars@^1.0.0":
  "integrity" "sha512-b2XGFAFdWZWg0phtAWLHCk836A1Xann+I+Dgd3Gk64MHKZO44FfoD1KxyvbSh0qZsIoXQGGlVztIY+oitJPpRQ=="
  "resolved" "https://registry.npmjs.org/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz"
  "version" "1.0.2"

"fbjs@^3.0.0":
  "integrity" "sha512-qv+boqYndjElAJHNN3NoM8XuwQZ1j2m3kEvTgdle8IDjr6oUbkEpvABWtj/rQl3vq4ew7dnElBxL4YJAwTVqQQ=="
  "resolved" "https://registry.npmjs.org/fbjs/-/fbjs-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "cross-fetch" "^3.0.4"
    "fbjs-css-vars" "^1.0.0"
    "loose-envify" "^1.0.0"
    "object-assign" "^4.1.0"
    "promise" "^7.1.1"
    "setimmediate" "^1.0.5"
    "ua-parser-js" "^0.7.30"

"file-entry-cache@^6.0.1":
  "integrity" "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg=="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"file-loader@^6.2.0":
  "integrity" "sha512-qo3glqyTa61Ytg4u73GultjHGjdRyig3tG6lPtyX/jOEJvHif9uB0/OCI2Kif6ctF3caQTW2G5gym21oAsI4pw=="
  "resolved" "https://registry.npmjs.org/file-loader/-/file-loader-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "loader-utils" "^2.0.0"
    "schema-utils" "^3.0.0"

"filelist@^1.0.1":
  "integrity" "sha512-z7O0IS8Plc39rTCq6i6iHxk43duYOn8uFJiWSewIq0Bww1RNybVHSCjahmcC87ZqAm4OTvFzlzeGu3XAzG1ctQ=="
  "resolved" "https://registry.npmjs.org/filelist/-/filelist-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minimatch" "^3.0.4"

"filesize@^8.0.6":
  "integrity" "sha512-sHvRqTiwdmcuzqet7iVwsbwF6UrV3wIgDf2SHNdY1Hgl8PC45HZg/0xtdw6U2izIV4lccnrY9ftl6wZFNdjYMg=="
  "resolved" "https://registry.npmjs.org/filesize/-/filesize-8.0.6.tgz"
  "version" "8.0.6"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@~1.1.2":
  "integrity" "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA=="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^3.3.1":
  "integrity" "sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig=="
  "resolved" "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-root@^1.1.0":
  "integrity" "sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng=="
  "resolved" "https://registry.npmjs.org/find-root/-/find-root-1.1.0.tgz"
  "version" "1.1.0"

"find-up@^2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^3.0.0":
  "integrity" "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.0.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"find-up@^5.0.0":
  "integrity" "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"firebase@^9.6.3":
  "integrity" "sha512-CMzv2LJGruZNtKI6pk1XLVaDC7ujIcq/S57wbC9XGllykIh86GLNPwVEWuCqCWmQDAZLyhi0t6tW/F2NX3HcPA=="
  "resolved" "https://registry.npmjs.org/firebase/-/firebase-9.6.3.tgz"
  "version" "9.6.3"
  dependencies:
    "@firebase/analytics" "0.7.5"
    "@firebase/analytics-compat" "0.1.6"
    "@firebase/app" "0.7.13"
    "@firebase/app-check" "0.5.3"
    "@firebase/app-check-compat" "0.2.3"
    "@firebase/app-compat" "0.1.14"
    "@firebase/app-types" "0.7.0"
    "@firebase/auth" "0.19.5"
    "@firebase/auth-compat" "0.2.5"
    "@firebase/database" "0.12.5"
    "@firebase/database-compat" "0.1.5"
    "@firebase/firestore" "3.4.3"
    "@firebase/firestore-compat" "0.1.12"
    "@firebase/functions" "0.7.7"
    "@firebase/functions-compat" "0.1.8"
    "@firebase/installations" "0.5.5"
    "@firebase/messaging" "0.9.6"
    "@firebase/messaging-compat" "0.1.6"
    "@firebase/performance" "0.5.5"
    "@firebase/performance-compat" "0.1.5"
    "@firebase/polyfill" "0.3.36"
    "@firebase/remote-config" "0.3.4"
    "@firebase/remote-config-compat" "0.1.5"
    "@firebase/storage" "0.9.1"
    "@firebase/storage-compat" "0.1.9"
    "@firebase/util" "1.4.3"

"flat-cache@^3.0.4":
  "integrity" "sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "flatted" "^3.1.0"
    "rimraf" "^3.0.2"

"flatted@^3.1.0":
  "integrity" "sha512-8/sOawo8tJ4QOBX8YlQBMxL8+RLZfxMQOif9o0KUKTNTjMYElWPE0r/m5VNFxTRd0NSw8qSy8dajrwX4RYI1Hw=="
  "resolved" "https://registry.npmjs.org/flatted/-/flatted-3.2.4.tgz"
  "version" "3.2.4"

"follow-redirects@^1.0.0":
  "integrity" "sha512-+hbxoLbFMbRKDwohX8GkTataGqO6Jb7jGwpAlwgy2bIz25XtRm7KEzJM76R1WiNT5SwZkX4Y75SwBolkpmE7iQ=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.7.tgz"
  "version" "1.14.7"

"fork-ts-checker-webpack-plugin@^6.5.0":
  "integrity" "sha512-cS178Y+xxtIjEUorcHddKS7yCMlrDPV31mt47blKKRfMd70Kxu5xruAFE2o9sDY6wVC5deuob/u/alD04YYHnw=="
  "resolved" "https://registry.npmjs.org/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@types/json-schema" "^7.0.5"
    "chalk" "^4.1.0"
    "chokidar" "^3.4.2"
    "cosmiconfig" "^6.0.0"
    "deepmerge" "^4.2.2"
    "fs-extra" "^9.0.0"
    "glob" "^7.1.6"
    "memfs" "^3.1.2"
    "minimatch" "^3.0.4"
    "schema-utils" "2.7.0"
    "semver" "^7.3.2"
    "tapable" "^1.0.0"

"form-data@^3.0.0":
  "integrity" "sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"forwarded@0.2.0":
  "integrity" "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="
  "resolved" "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"fraction.js@^4.1.2":
  "integrity" "sha512-o2RiJQ6DZaR/5+Si0qJUIy637QMRudSi9kU/FFzx9EZazrIdnBgpU+3sEWCxAVhH2RtxW2Oz+T4p2o8uOPVcgA=="
  "resolved" "https://registry.npmjs.org/fraction.js/-/fraction.js-4.1.2.tgz"
  "version" "4.1.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"fs-extra@^10.0.0":
  "integrity" "sha512-C5owb14u9eJwizKGdchcDUQeFtlSHHthBk8pbX9Vc1PFZrLombudjDnNns88aYslCyF6IY5SUw3Roz6xShcEIQ=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-10.0.0.tgz"
  "version" "10.0.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-extra@^9.0.0":
  "integrity" "sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "at-least-node" "^1.0.0"
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-extra@^9.0.1":
  "integrity" "sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "at-least-node" "^1.0.0"
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-monkey@1.0.3":
  "integrity" "sha512-cybjIfiiE+pTWicSCLFHSrXZ6EilF30oh91FDP9S2B051prEa7QWfrVTQm10/dDpswBDXZugPa1Ogu8Yh+HV0Q=="
  "resolved" "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.3.tgz"
  "version" "1.0.3"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^2.3.2", "fsevents@~2.3.2":
  "integrity" "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA=="
  "resolved" "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz"
  "version" "2.3.2"

"function-bind@^1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.0", "get-intrinsic@^1.1.1":
  "integrity" "sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q=="
  "resolved" "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"

"get-own-enumerable-property-symbols@^3.0.0":
  "integrity" "sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g=="
  "resolved" "https://registry.npmjs.org/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz"
  "version" "3.0.2"

"get-package-type@^0.1.0":
  "integrity" "sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q=="
  "resolved" "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  "version" "0.1.0"

"get-stream@^6.0.0":
  "integrity" "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"get-symbol-description@^1.0.0":
  "integrity" "sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw=="
  "resolved" "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.1"

"glob-parent@^5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.1", "glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-to-regexp@^0.4.1":
  "integrity" "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw=="
  "resolved" "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  "version" "0.4.1"

"glob@^7.1.1", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4", "glob@^7.1.6":
  "integrity" "sha512-lmLf6gtyrPq8tTjSmrO94wBeQbFR3HbLHbuyD69wuyQkImp2hWqMGB47OX65FBkPffO641IP9jWa1z4ivqG26Q=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"global-modules@^2.0.0":
  "integrity" "sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A=="
  "resolved" "https://registry.npmjs.org/global-modules/-/global-modules-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "global-prefix" "^3.0.0"

"global-prefix@^3.0.0":
  "integrity" "sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg=="
  "resolved" "https://registry.npmjs.org/global-prefix/-/global-prefix-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ini" "^1.3.5"
    "kind-of" "^6.0.2"
    "which" "^1.3.1"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.6.0":
  "integrity" "sha512-uS8X6lSKN2JumVoXrbUz+uG4BYG+eiawqm3qFcT7ammfbUHeCBoJMlHcec/S3krSk73/AE/f0szYFmgAA3kYZg=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-13.12.0.tgz"
  "version" "13.12.0"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^13.9.0":
  "integrity" "sha512-uS8X6lSKN2JumVoXrbUz+uG4BYG+eiawqm3qFcT7ammfbUHeCBoJMlHcec/S3krSk73/AE/f0szYFmgAA3kYZg=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-13.12.0.tgz"
  "version" "13.12.0"
  dependencies:
    "type-fest" "^0.20.2"

"globby@^11.0.1", "globby@^11.0.4":
  "integrity" "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g=="
  "resolved" "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
  "version" "11.1.0"
  dependencies:
    "array-union" "^2.1.0"
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.2.9"
    "ignore" "^5.2.0"
    "merge2" "^1.4.1"
    "slash" "^3.0.0"

"graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.0", "graceful-fs@^4.2.4", "graceful-fs@^4.2.6", "graceful-fs@^4.2.9":
  "integrity" "sha512-NtNxqUcXgpW2iMrfqSfR73Glt39K+BLwWsPs94yR63v45T0Wbej7eRmL5cWfwEgqXnmjQp3zaJTshdRW/qC2ZQ=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.9.tgz"
  "version" "4.2.9"

"gzip-size@^6.0.0":
  "integrity" "sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q=="
  "resolved" "https://registry.npmjs.org/gzip-size/-/gzip-size-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "duplexer" "^0.1.2"

"handle-thing@^2.0.0":
  "integrity" "sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg=="
  "resolved" "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"harmony-reflect@^1.4.6":
  "integrity" "sha512-HIp/n38R9kQjDEziXyDTuW3vvoxxyxjxFzXLrBr18uB47GnSt+G9D29fqrpM5ZkspMcPICud3XsBJQ4Y2URg8g=="
  "resolved" "https://registry.npmjs.org/harmony-reflect/-/harmony-reflect-1.6.2.tgz"
  "version" "1.6.2"

"has-bigints@^1.0.1":
  "integrity" "sha512-LSBS2LjbNBTf6287JEbEzvJgftkF5qFkmCo9hDRpAzKhUOlJ+hx8dd4USs00SgsUNwc4617J9ki5YtEClM2ffA=="
  "resolved" "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.1.tgz"
  "version" "1.0.1"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.1", "has-symbols@^1.0.2":
  "integrity" "sha512-chXa79rL/UC2KlX17jo3vRGz0azaWEx5tGqZg5pO3NUyEJVB17dMruQlzCCOfUvElghKcm5194+BCRvi2Rv/Gw=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.2.tgz"
  "version" "1.0.2"

"has-tostringtag@^1.0.0":
  "integrity" "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has@^1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"he@^1.2.0":
  "integrity" "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="
  "resolved" "https://registry.npmjs.org/he/-/he-1.2.0.tgz"
  "version" "1.2.0"

"history@^5.2.0":
  "integrity" "sha512-uPSF6lAJb3nSePJ43hN3eKj1dTWpN9gMod0ZssbFTIsen+WehTmEadgL+kg78xLJFdRfrrC//SavDzmRVdE+Ig=="
  "resolved" "https://registry.npmjs.org/history/-/history-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/runtime" "^7.7.6"

"hoist-non-react-statics@^3.3.0", "hoist-non-react-statics@^3.3.1", "hoist-non-react-statics@^3.3.2":
  "integrity" "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw=="
  "resolved" "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "react-is" "^16.7.0"

"hoopy@^0.1.4":
  "integrity" "sha512-HRcs+2mr52W0K+x8RzcLzuPPmVIKMSv97RGHy0Ea9y/mpcaK+xTrjICA04KAHi4GRzxliNqNJEFYWHghy3rSfQ=="
  "resolved" "https://registry.npmjs.org/hoopy/-/hoopy-0.1.4.tgz"
  "version" "0.1.4"

"hpack.js@^2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"html-encoding-sniffer@^2.0.1":
  "integrity" "sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ=="
  "resolved" "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "whatwg-encoding" "^1.0.5"

"html-entities@^2.1.0", "html-entities@^2.3.2":
  "integrity" "sha512-c3Ab/url5ksaT0WyleslpBEthOzWhrjQbg75y7XUsfSzi3Dgzt0l8w5e7DylRn15MTlMMD58dTfzddNS2kcAjQ=="
  "resolved" "https://registry.npmjs.org/html-entities/-/html-entities-2.3.2.tgz"
  "version" "2.3.2"

"html-escaper@^2.0.0":
  "integrity" "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg=="
  "resolved" "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz"
  "version" "2.0.2"

"html-minifier-terser@^6.0.2":
  "integrity" "sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw=="
  "resolved" "https://registry.npmjs.org/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "camel-case" "^4.1.2"
    "clean-css" "^5.2.2"
    "commander" "^8.3.0"
    "he" "^1.2.0"
    "param-case" "^3.0.4"
    "relateurl" "^0.2.7"
    "terser" "^5.10.0"

"html-webpack-plugin@^5.5.0":
  "integrity" "sha512-sy88PC2cRTVxvETRgUHFrL4No3UxvcH8G1NepGhqaTT+GXN2kTamqasot0inS5hXeg1cMbFDt27zzo9p35lZVw=="
  "resolved" "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    "html-minifier-terser" "^6.0.2"
    "lodash" "^4.17.21"
    "pretty-error" "^4.0.0"
    "tapable" "^2.0.0"

"htmlparser2@^6.1.0":
  "integrity" "sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A=="
  "resolved" "https://registry.npmjs.org/htmlparser2/-/htmlparser2-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.0.0"
    "domutils" "^2.5.2"
    "entities" "^2.0.0"

"http-deceiver@^1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@1.8.1":
  "integrity" "sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.1"

"http-parser-js@>=0.5.1":
  "integrity" "sha512-x+JVEkO2PoM8qqpbPbOL3cqHPwerep7OwzK7Ay+sMQjKzaKCqWvjoXm5tqMP9tXWWTnTzAjIhXg+J99XYuPhPA=="
  "resolved" "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.5.tgz"
  "version" "0.5.5"

"http-proxy-agent@^4.0.1":
  "integrity" "sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg=="
  "resolved" "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@tootallnate/once" "1"
    "agent-base" "6"
    "debug" "4"

"http-proxy-middleware@^2.0.0":
  "integrity" "sha512-cfaXRVoZxSed/BmkA7SwBVNI9Kj7HFltaE5rqYOub5kWzWZ+gofV2koVN1j2rMW7pEfSSlCHGJ31xmuyFyfLOg=="
  "resolved" "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/http-proxy" "^1.17.5"
    "http-proxy" "^1.18.1"
    "is-glob" "^4.0.1"
    "is-plain-obj" "^3.0.0"
    "micromatch" "^4.0.2"

"http-proxy@^1.18.1":
  "integrity" "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ=="
  "resolved" "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"https-proxy-agent@^5.0.0":
  "integrity" "sha512-EkYm5BcKUGiduxzSt3Eppko+PiNWNEpa4ySk9vTC6wDsQJW9rHSa+UhGNJoRYp7bz6Ht1eaRIa6QaJqO5rCFbA=="
  "resolved" "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "agent-base" "6"
    "debug" "4"

"human-signals@^2.1.0":
  "integrity" "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw=="
  "resolved" "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"hyphenate-style-name@^1.0.2", "hyphenate-style-name@^1.0.4":
  "integrity" "sha512-ygGZLjmXfPHj+ZWh6LwbC37l43MhfztxetbFCoYTM2VjkIUpeHgSNn7QIyVFj7YQ1Wl9Cbw5sholVJPzWvC2MQ=="
  "resolved" "https://registry.npmjs.org/hyphenate-style-name/-/hyphenate-style-name-1.0.4.tgz"
  "version" "1.0.4"

"iconv-lite@^0.6.3":
  "integrity" "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-utils@^5.0.0", "icss-utils@^5.1.0":
  "integrity" "sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA=="
  "resolved" "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz"
  "version" "5.1.0"

"idb@^6.1.4":
  "integrity" "sha512-IJtugpKkiVXQn5Y+LteyBCNk1N8xpGV3wWZk9EVtZWH8DYkjBn0bX1XnGP9RkyZF0sAcywa6unHqSWKe7q4LGw=="
  "resolved" "https://registry.npmjs.org/idb/-/idb-6.1.5.tgz"
  "version" "6.1.5"

"idb@3.0.2":
  "integrity" "sha512-+FLa/0sTXqyux0o6C+i2lOR0VoS60LU/jzUo5xjfY6+7sEEgy4Gz1O7yFBXvjd7N0NyIGWIRg8DcQSLEG+VSPw=="
  "resolved" "https://registry.npmjs.org/idb/-/idb-3.0.2.tgz"
  "version" "3.0.2"

"identity-obj-proxy@^3.0.0":
  "integrity" "sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ="
  "resolved" "https://registry.npmjs.org/identity-obj-proxy/-/identity-obj-proxy-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "harmony-reflect" "^1.4.6"

"ignore@^4.0.6":
  "integrity" "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^5.1.8", "ignore@^5.2.0":
  "integrity" "sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-5.2.0.tgz"
  "version" "5.2.0"

"immediate@~3.0.5":
  "integrity" "sha1-nbHb0Pr43m++D13V5Wu2BigN5ps="
  "resolved" "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  "version" "3.0.6"

"immer@^9.0.7":
  "integrity" "sha512-lk7UNmSbAukB5B6dh9fnh5D0bJTOFKxVg2cyJWTYrWRfhLrLMBquONcUs3aFq507hNoIZEDDh8lb8UtOizSMhA=="
  "resolved" "https://registry.npmjs.org/immer/-/immer-9.0.12.tgz"
  "version" "9.0.12"

"import-fresh@^3.0.0", "import-fresh@^3.1.0", "import-fresh@^3.2.1":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-local@^3.0.2":
  "integrity" "sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg=="
  "resolved" "https://registry.npmjs.org/import-local/-/import-local-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "pkg-dir" "^4.2.0"
    "resolve-cwd" "^3.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="
  "resolved" "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"ini@^1.3.5":
  "integrity" "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="
  "resolved" "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz"
  "version" "1.3.8"

"inline-style-prefixer@^6.0.0":
  "integrity" "sha512-AsqazZ8KcRzJ9YPN1wMH2aNM7lkWQ8tSPrW5uDk1ziYwiAPWSZnUsC7lfZq+BDqLqz0B4Pho5wscWcJzVvRzDQ=="
  "resolved" "https://registry.npmjs.org/inline-style-prefixer/-/inline-style-prefixer-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "css-in-js-utils" "^2.0.0"

"internal-slot@^1.0.3":
  "integrity" "sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA=="
  "resolved" "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "get-intrinsic" "^1.1.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"invariant@^2.2.4":
  "integrity" "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA=="
  "resolved" "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"ip@^1.1.0":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.npmjs.org/ip/-/ip-1.1.5.tgz"
  "version" "1.1.5"

"ipaddr.js@^2.0.1":
  "integrity" "sha512-1qTgH9NG+IIJ4yfKs2e6Pp1bZg8wbDbKHT21HrLIeYBTRLgMYKnMTPAuI3Lcs61nfx5h1xlXnbJtH1kX5/d/ng=="
  "resolved" "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.0.1.tgz"
  "version" "2.0.1"

"ipaddr.js@1.9.1":
  "integrity" "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="
  "resolved" "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-arguments@^1.0.4":
  "integrity" "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA=="
  "resolved" "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-bigint@^1.0.1":
  "integrity" "sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg=="
  "resolved" "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-bigints" "^1.0.1"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-boolean-object@^1.1.0":
  "integrity" "sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA=="
  "resolved" "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-callable@^1.1.4", "is-callable@^1.2.4":
  "integrity" "sha512-nsuwtxZfMX67Oryl9LCQ+upnC0Z0BgpwntpS89m1H/TLF0zNfzfLMV/9Wa/6MZsj0acpEjAO0KF1xT6ZdLl95w=="
  "resolved" "https://registry.npmjs.org/is-callable/-/is-callable-1.2.4.tgz"
  "version" "1.2.4"

"is-core-module@^2.2.0", "is-core-module@^2.8.0":
  "integrity" "sha512-SdNCUs284hr40hFTFP6l0IfZ/RSrMXF3qgoRHd3/79unUTvrFO/JoXwkGm+5J/Oe3E/b5GsnG330uUNgRpu1PA=="
  "resolved" "https://registry.npmjs.org/is-core-module/-/is-core-module-2.8.1.tgz"
  "version" "2.8.1"
  dependencies:
    "has" "^1.0.3"

"is-date-object@^1.0.1":
  "integrity" "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  "resolved" "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-docker@^2.0.0", "is-docker@^2.1.1":
  "integrity" "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ=="
  "resolved" "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-generator-fn@^2.0.0":
  "integrity" "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ=="
  "resolved" "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  "version" "2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-module@^1.0.0":
  "integrity" "sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE="
  "resolved" "https://registry.npmjs.org/is-module/-/is-module-1.0.0.tgz"
  "version" "1.0.0"

"is-negative-zero@^2.0.1":
  "integrity" "sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA=="
  "resolved" "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  "version" "2.0.2"

"is-number-object@^1.0.4":
  "integrity" "sha512-bEVOqiRcvo3zO1+G2lVMy+gkkEm9Yh7cDMRusKKu5ZJKPUYSJwICTKZrNKHA2EbSP0Tu0+6B/emsYNHZyn6K8g=="
  "resolved" "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^1.0.1":
  "integrity" "sha1-PkcprB9f3gJc19g6iW2rn09n2w8="
  "resolved" "https://registry.npmjs.org/is-obj/-/is-obj-1.0.1.tgz"
  "version" "1.0.1"

"is-path-cwd@^2.2.0":
  "integrity" "sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ=="
  "resolved" "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-2.2.0.tgz"
  "version" "2.2.0"

"is-path-inside@^3.0.2":
  "integrity" "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ=="
  "resolved" "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
  "version" "3.0.3"

"is-plain-obj@^3.0.0":
  "integrity" "sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA=="
  "resolved" "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  "version" "3.0.0"

"is-potential-custom-element-name@^1.0.1":
  "integrity" "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ=="
  "resolved" "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  "version" "1.0.1"

"is-regex@^1.0.4", "is-regex@^1.1.4":
  "integrity" "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-regexp@^1.0.0":
  "integrity" "sha1-/S2INUXEa6xaYz57mgnof6LLUGk="
  "resolved" "https://registry.npmjs.org/is-regexp/-/is-regexp-1.0.0.tgz"
  "version" "1.0.0"

"is-root@^2.1.0":
  "integrity" "sha512-AGOriNp96vNBd3HtU+RzFEc75FfR5ymiYv8E553I71SCeXBiMsVDUtdio1OEFvrPyLIQ9tVR5RxXIFe5PUFjMg=="
  "resolved" "https://registry.npmjs.org/is-root/-/is-root-2.1.0.tgz"
  "version" "2.1.0"

"is-shared-array-buffer@^1.0.1":
  "integrity" "sha512-IU0NmyknYZN0rChcKhRO1X8LYz5Isj/Fsqh8NJOSf+N/hCOTwy29F32Ik7a+QszE63IdvmwdTPDd6cZ5pg4cwA=="
  "resolved" "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.1.tgz"
  "version" "1.0.1"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-string@^1.0.5", "is-string@^1.0.7":
  "integrity" "sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg=="
  "resolved" "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg=="
  "resolved" "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typedarray@^1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-weakref@^1.0.1":
  "integrity" "sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ=="
  "resolved" "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-wsl@^2.2.0":
  "integrity" "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww=="
  "resolved" "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@~1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"istanbul-lib-coverage@^3.0.0", "istanbul-lib-coverage@^3.2.0":
  "integrity" "sha512-eOeJ5BHCmHYvQK7xt9GkdHuzuCGS1Y6g9Gvnx3Ym33fz/HpLRYxiS0wHNr+m/MBC8B647Xt608vCDEvhl9c6Mw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz"
  "version" "3.2.0"

"istanbul-lib-instrument@^5.0.4", "istanbul-lib-instrument@^5.1.0":
  "integrity" "sha512-czwUz525rkOFDJxfKK6mYfIs9zBKILyrZQxjz3ABhjQXhbhFsSbo1HW/BFcsDnfJYJWA6thRR5/TUY2qs5W99Q=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-coverage" "^3.2.0"
    "semver" "^6.3.0"

"istanbul-lib-report@^3.0.0":
  "integrity" "sha512-wcdi+uAKzfiGT2abPpKZ0hSU1rGQjUQnLvtY5MpQ7QCTahD3VODhcu4wcfY1YtkGaDD5yuydOLINXsfbus9ROw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "istanbul-lib-coverage" "^3.0.0"
    "make-dir" "^3.0.0"
    "supports-color" "^7.1.0"

"istanbul-lib-source-maps@^4.0.0":
  "integrity" "sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "debug" "^4.1.1"
    "istanbul-lib-coverage" "^3.0.0"
    "source-map" "^0.6.1"

"istanbul-reports@^3.1.3":
  "integrity" "sha512-x9LtDVtfm/t1GFiLl3NffC7hz+I1ragvgX1P/Lg1NlIagifZDKUkuuaAxH/qpwj2IuEfD8G2Bs/UKp+sZ/pKkg=="
  "resolved" "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "html-escaper" "^2.0.0"
    "istanbul-lib-report" "^3.0.0"

"jake@^10.6.1":
  "integrity" "sha512-eLpKyrfG3mzvGE2Du8VoPbeSkRry093+tyNjdYaBbJS9v17knImYGNXQCUV0gLxQtF82m3E8iRb/wdSQZLoq7A=="
  "resolved" "https://registry.npmjs.org/jake/-/jake-10.8.2.tgz"
  "version" "10.8.2"
  dependencies:
    "async" "0.9.x"
    "chalk" "^2.4.2"
    "filelist" "^1.0.1"
    "minimatch" "^3.0.4"

"jest-changed-files@^27.4.2":
  "integrity" "sha512-/9x8MjekuzUQoPjDHbBiXbNEBauhrPU2ct7m8TfCg69ywt1y/N+yYwGh3gCpnqUS3klYWDU/lSNgv+JhoD2k1A=="
  "resolved" "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.4.2.tgz"
  "version" "27.4.2"
  dependencies:
    "@jest/types" "^27.4.2"
    "execa" "^5.0.0"
    "throat" "^6.0.1"

"jest-circus@^27.4.6":
  "integrity" "sha512-UA7AI5HZrW4wRM72Ro80uRR2Fg+7nR0GESbSI/2M+ambbzVuA63mn5T1p3Z/wlhntzGpIG1xx78GP2YIkf6PhQ=="
  "resolved" "https://registry.npmjs.org/jest-circus/-/jest-circus-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/environment" "^27.4.6"
    "@jest/test-result" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "co" "^4.6.0"
    "dedent" "^0.7.0"
    "expect" "^27.4.6"
    "is-generator-fn" "^2.0.0"
    "jest-each" "^27.4.6"
    "jest-matcher-utils" "^27.4.6"
    "jest-message-util" "^27.4.6"
    "jest-runtime" "^27.4.6"
    "jest-snapshot" "^27.4.6"
    "jest-util" "^27.4.2"
    "pretty-format" "^27.4.6"
    "slash" "^3.0.0"
    "stack-utils" "^2.0.3"
    "throat" "^6.0.1"

"jest-cli@^27.4.7":
  "integrity" "sha512-zREYhvjjqe1KsGV15mdnxjThKNDgza1fhDT+iUsXWLCq3sxe9w5xnvyctcYVT5PcdLSjv7Y5dCwTS3FCF1tiuw=="
  "resolved" "https://registry.npmjs.org/jest-cli/-/jest-cli-27.4.7.tgz"
  "version" "27.4.7"
  dependencies:
    "@jest/core" "^27.4.7"
    "@jest/test-result" "^27.4.6"
    "@jest/types" "^27.4.2"
    "chalk" "^4.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "import-local" "^3.0.2"
    "jest-config" "^27.4.7"
    "jest-util" "^27.4.2"
    "jest-validate" "^27.4.6"
    "prompts" "^2.0.1"
    "yargs" "^16.2.0"

"jest-config@^27.4.7":
  "integrity" "sha512-xz/o/KJJEedHMrIY9v2ParIoYSrSVY6IVeE4z5Z3i101GoA5XgfbJz+1C8EYPsv7u7f39dS8F9v46BHDhn0vlw=="
  "resolved" "https://registry.npmjs.org/jest-config/-/jest-config-27.4.7.tgz"
  "version" "27.4.7"
  dependencies:
    "@babel/core" "^7.8.0"
    "@jest/test-sequencer" "^27.4.6"
    "@jest/types" "^27.4.2"
    "babel-jest" "^27.4.6"
    "chalk" "^4.0.0"
    "ci-info" "^3.2.0"
    "deepmerge" "^4.2.2"
    "glob" "^7.1.1"
    "graceful-fs" "^4.2.4"
    "jest-circus" "^27.4.6"
    "jest-environment-jsdom" "^27.4.6"
    "jest-environment-node" "^27.4.6"
    "jest-get-type" "^27.4.0"
    "jest-jasmine2" "^27.4.6"
    "jest-regex-util" "^27.4.0"
    "jest-resolve" "^27.4.6"
    "jest-runner" "^27.4.6"
    "jest-util" "^27.4.2"
    "jest-validate" "^27.4.6"
    "micromatch" "^4.0.4"
    "pretty-format" "^27.4.6"
    "slash" "^3.0.0"

"jest-diff@^27.0.0", "jest-diff@^27.4.6":
  "integrity" "sha512-zjaB0sh0Lb13VyPsd92V7HkqF6yKRH9vm33rwBt7rPYrpQvS1nCvlIy2pICbKta+ZjWngYLNn4cCK4nyZkjS/w=="
  "resolved" "https://registry.npmjs.org/jest-diff/-/jest-diff-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "chalk" "^4.0.0"
    "diff-sequences" "^27.4.0"
    "jest-get-type" "^27.4.0"
    "pretty-format" "^27.4.6"

"jest-docblock@^27.4.0":
  "integrity" "sha512-7TBazUdCKGV7svZ+gh7C8esAnweJoG+SvcF6Cjqj4l17zA2q1cMwx2JObSioubk317H+cjcHgP+7fTs60paulg=="
  "resolved" "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.4.0.tgz"
  "version" "27.4.0"
  dependencies:
    "detect-newline" "^3.0.0"

"jest-each@^27.4.6":
  "integrity" "sha512-n6QDq8y2Hsmn22tRkgAk+z6MCX7MeVlAzxmZDshfS2jLcaBlyhpF3tZSJLR+kXmh23GEvS0ojMR8i6ZeRvpQcA=="
  "resolved" "https://registry.npmjs.org/jest-each/-/jest-each-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "chalk" "^4.0.0"
    "jest-get-type" "^27.4.0"
    "jest-util" "^27.4.2"
    "pretty-format" "^27.4.6"

"jest-environment-jsdom@^27.4.6":
  "integrity" "sha512-o3dx5p/kHPbUlRvSNjypEcEtgs6LmvESMzgRFQE6c+Prwl2JLA4RZ7qAnxc5VM8kutsGRTB15jXeeSbJsKN9iA=="
  "resolved" "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/environment" "^27.4.6"
    "@jest/fake-timers" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "jest-mock" "^27.4.6"
    "jest-util" "^27.4.2"
    "jsdom" "^16.6.0"

"jest-environment-node@^27.4.6":
  "integrity" "sha512-yfHlZ9m+kzTKZV0hVfhVu6GuDxKAYeFHrfulmy7Jxwsq4V7+ZK7f+c0XP/tbVDMQW7E4neG2u147hFkuVz0MlQ=="
  "resolved" "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/environment" "^27.4.6"
    "@jest/fake-timers" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "jest-mock" "^27.4.6"
    "jest-util" "^27.4.2"

"jest-get-type@^27.4.0":
  "integrity" "sha512-tk9o+ld5TWq41DkK14L4wox4s2D9MtTpKaAVzXfr5CUKm5ZK2ExcaFE0qls2W71zE/6R2TxxrK9w2r6svAFDBQ=="
  "resolved" "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.4.0.tgz"
  "version" "27.4.0"

"jest-haste-map@^27.4.6":
  "integrity" "sha512-0tNpgxg7BKurZeFkIOvGCkbmOHbLFf4LUQOxrQSMjvrQaQe3l6E8x6jYC1NuWkGo5WDdbr8FEzUxV2+LWNawKQ=="
  "resolved" "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    "anymatch" "^3.0.3"
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-regex-util" "^27.4.0"
    "jest-serializer" "^27.4.0"
    "jest-util" "^27.4.2"
    "jest-worker" "^27.4.6"
    "micromatch" "^4.0.4"
    "walker" "^1.0.7"
  optionalDependencies:
    "fsevents" "^2.3.2"

"jest-jasmine2@^27.4.6":
  "integrity" "sha512-uAGNXF644I/whzhsf7/qf74gqy9OuhvJ0XYp8SDecX2ooGeaPnmJMjXjKt0mqh1Rl5dtRGxJgNrHlBQIBfS5Nw=="
  "resolved" "https://registry.npmjs.org/jest-jasmine2/-/jest-jasmine2-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/environment" "^27.4.6"
    "@jest/source-map" "^27.4.0"
    "@jest/test-result" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "co" "^4.6.0"
    "expect" "^27.4.6"
    "is-generator-fn" "^2.0.0"
    "jest-each" "^27.4.6"
    "jest-matcher-utils" "^27.4.6"
    "jest-message-util" "^27.4.6"
    "jest-runtime" "^27.4.6"
    "jest-snapshot" "^27.4.6"
    "jest-util" "^27.4.2"
    "pretty-format" "^27.4.6"
    "throat" "^6.0.1"

"jest-leak-detector@^27.4.6":
  "integrity" "sha512-kkaGixDf9R7CjHm2pOzfTxZTQQQ2gHTIWKY/JZSiYTc90bZp8kSZnUMS3uLAfwTZwc0tcMRoEX74e14LG1WapA=="
  "resolved" "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "jest-get-type" "^27.4.0"
    "pretty-format" "^27.4.6"

"jest-matcher-utils@^27.4.6":
  "integrity" "sha512-XD4PKT3Wn1LQnRAq7ZsTI0VRuEc9OrCPFiO1XL7bftTGmfNF0DcEwMHRgqiu7NGf8ZoZDREpGrCniDkjt79WbA=="
  "resolved" "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "chalk" "^4.0.0"
    "jest-diff" "^27.4.6"
    "jest-get-type" "^27.4.0"
    "pretty-format" "^27.4.6"

"jest-message-util@^27.4.6":
  "integrity" "sha512-0p5szriFU0U74czRSFjH6RyS7UYIAkn/ntwMuOwTGWrQIOh5NzXXrq72LOqIkJKKvFbPq+byZKuBz78fjBERBA=="
  "resolved" "https://registry.npmjs.org/jest-message-util/-/jest-message-util-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^27.4.2"
    "@types/stack-utils" "^2.0.0"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "micromatch" "^4.0.4"
    "pretty-format" "^27.4.6"
    "slash" "^3.0.0"
    "stack-utils" "^2.0.3"

"jest-mock@^27.4.6":
  "integrity" "sha512-kvojdYRkst8iVSZ1EJ+vc1RRD9llueBjKzXzeCytH3dMM7zvPV/ULcfI2nr0v0VUgm3Bjt3hBCQvOeaBz+ZTHw=="
  "resolved" "https://registry.npmjs.org/jest-mock/-/jest-mock-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "@types/node" "*"

"jest-pnp-resolver@^1.2.2":
  "integrity" "sha512-olV41bKSMm8BdnuMsewT4jqlZ8+3TCARAXjZGT9jcoSnrfUnRCqnMoF9XEeoWjbzObpqF9dRhHQj0Xb9QdF6/w=="
  "resolved" "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.2.tgz"
  "version" "1.2.2"

"jest-regex-util@^27.0.0", "jest-regex-util@^27.4.0":
  "integrity" "sha512-WeCpMpNnqJYMQoOjm1nTtsgbR4XHAk1u00qDoNBQoykM280+/TmgA5Qh5giC1ecy6a5d4hbSsHzpBtu5yvlbEg=="
  "resolved" "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.4.0.tgz"
  "version" "27.4.0"

"jest-resolve-dependencies@^27.4.6":
  "integrity" "sha512-W85uJZcFXEVZ7+MZqIPCscdjuctruNGXUZ3OHSXOfXR9ITgbUKeHj+uGcies+0SsvI5GtUfTw4dY7u9qjTvQOw=="
  "resolved" "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "jest-regex-util" "^27.4.0"
    "jest-snapshot" "^27.4.6"

"jest-resolve@*", "jest-resolve@^27.4.2", "jest-resolve@^27.4.6":
  "integrity" "sha512-SFfITVApqtirbITKFAO7jOVN45UgFzcRdQanOFzjnbd+CACDoyeX7206JyU92l4cRr73+Qy/TlW51+4vHGt+zw=="
  "resolved" "https://registry.npmjs.org/jest-resolve/-/jest-resolve-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^27.4.6"
    "jest-pnp-resolver" "^1.2.2"
    "jest-util" "^27.4.2"
    "jest-validate" "^27.4.6"
    "resolve" "^1.20.0"
    "resolve.exports" "^1.1.0"
    "slash" "^3.0.0"

"jest-runner@^27.4.6":
  "integrity" "sha512-IDeFt2SG4DzqalYBZRgbbPmpwV3X0DcntjezPBERvnhwKGWTW7C5pbbA5lVkmvgteeNfdd/23gwqv3aiilpYPg=="
  "resolved" "https://registry.npmjs.org/jest-runner/-/jest-runner-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/console" "^27.4.6"
    "@jest/environment" "^27.4.6"
    "@jest/test-result" "^27.4.6"
    "@jest/transform" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "emittery" "^0.8.1"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-docblock" "^27.4.0"
    "jest-environment-jsdom" "^27.4.6"
    "jest-environment-node" "^27.4.6"
    "jest-haste-map" "^27.4.6"
    "jest-leak-detector" "^27.4.6"
    "jest-message-util" "^27.4.6"
    "jest-resolve" "^27.4.6"
    "jest-runtime" "^27.4.6"
    "jest-util" "^27.4.2"
    "jest-worker" "^27.4.6"
    "source-map-support" "^0.5.6"
    "throat" "^6.0.1"

"jest-runtime@^27.4.6":
  "integrity" "sha512-eXYeoR/MbIpVDrjqy5d6cGCFOYBFFDeKaNWqTp0h6E74dK0zLHzASQXJpl5a2/40euBmKnprNLJ0Kh0LCndnWQ=="
  "resolved" "https://registry.npmjs.org/jest-runtime/-/jest-runtime-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/environment" "^27.4.6"
    "@jest/fake-timers" "^27.4.6"
    "@jest/globals" "^27.4.6"
    "@jest/source-map" "^27.4.0"
    "@jest/test-result" "^27.4.6"
    "@jest/transform" "^27.4.6"
    "@jest/types" "^27.4.2"
    "chalk" "^4.0.0"
    "cjs-module-lexer" "^1.0.0"
    "collect-v8-coverage" "^1.0.0"
    "execa" "^5.0.0"
    "glob" "^7.1.3"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^27.4.6"
    "jest-message-util" "^27.4.6"
    "jest-mock" "^27.4.6"
    "jest-regex-util" "^27.4.0"
    "jest-resolve" "^27.4.6"
    "jest-snapshot" "^27.4.6"
    "jest-util" "^27.4.2"
    "slash" "^3.0.0"
    "strip-bom" "^4.0.0"

"jest-serializer@^27.4.0":
  "integrity" "sha512-RDhpcn5f1JYTX2pvJAGDcnsNTnsV9bjYPU8xcV+xPwOXnUPOQwf4ZEuiU6G9H1UztH+OapMgu/ckEVwO87PwnQ=="
  "resolved" "https://registry.npmjs.org/jest-serializer/-/jest-serializer-27.4.0.tgz"
  "version" "27.4.0"
  dependencies:
    "@types/node" "*"
    "graceful-fs" "^4.2.4"

"jest-snapshot@^27.4.6":
  "integrity" "sha512-fafUCDLQfzuNP9IRcEqaFAMzEe7u5BF7mude51wyWv7VRex60WznZIC7DfKTgSIlJa8aFzYmXclmN328aqSDmQ=="
  "resolved" "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@babel/core" "^7.7.2"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.0.0"
    "@jest/transform" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.1.5"
    "babel-preset-current-node-syntax" "^1.0.0"
    "chalk" "^4.0.0"
    "expect" "^27.4.6"
    "graceful-fs" "^4.2.4"
    "jest-diff" "^27.4.6"
    "jest-get-type" "^27.4.0"
    "jest-haste-map" "^27.4.6"
    "jest-matcher-utils" "^27.4.6"
    "jest-message-util" "^27.4.6"
    "jest-util" "^27.4.2"
    "natural-compare" "^1.4.0"
    "pretty-format" "^27.4.6"
    "semver" "^7.3.2"

"jest-util@^27.4.2":
  "integrity" "sha512-YuxxpXU6nlMan9qyLuxHaMMOzXAl5aGZWCSzben5DhLHemYQxCc4YK+4L3ZrCutT8GPQ+ui9k5D8rUJoDioMnA=="
  "resolved" "https://registry.npmjs.org/jest-util/-/jest-util-27.4.2.tgz"
  "version" "27.4.2"
  dependencies:
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "ci-info" "^3.2.0"
    "graceful-fs" "^4.2.4"
    "picomatch" "^2.2.3"

"jest-validate@^27.4.6":
  "integrity" "sha512-872mEmCPVlBqbA5dToC57vA3yJaMRfIdpCoD3cyHWJOMx+SJwLNw0I71EkWs41oza/Er9Zno9XuTkRYCPDUJXQ=="
  "resolved" "https://registry.npmjs.org/jest-validate/-/jest-validate-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/types" "^27.4.2"
    "camelcase" "^6.2.0"
    "chalk" "^4.0.0"
    "jest-get-type" "^27.4.0"
    "leven" "^3.1.0"
    "pretty-format" "^27.4.6"

"jest-watch-typeahead@^1.0.0":
  "integrity" "sha512-jxoszalAb394WElmiJTFBMzie/RDCF+W7Q29n5LzOPtcoQoHWfdUtHFkbhgf5NwWe8uMOxvKb/g7ea7CshfkTw=="
  "resolved" "https://registry.npmjs.org/jest-watch-typeahead/-/jest-watch-typeahead-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ansi-escapes" "^4.3.1"
    "chalk" "^4.0.0"
    "jest-regex-util" "^27.0.0"
    "jest-watcher" "^27.0.0"
    "slash" "^4.0.0"
    "string-length" "^5.0.1"
    "strip-ansi" "^7.0.1"

"jest-watcher@^27.0.0", "jest-watcher@^27.4.6":
  "integrity" "sha512-yKQ20OMBiCDigbD0quhQKLkBO+ObGN79MO4nT7YaCuQ5SM+dkBNWE8cZX0FjU6czwMvWw6StWbe+Wv4jJPJ+fw=="
  "resolved" "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@jest/test-result" "^27.4.6"
    "@jest/types" "^27.4.2"
    "@types/node" "*"
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.0.0"
    "jest-util" "^27.4.2"
    "string-length" "^4.0.1"

"jest-worker@^26.2.1":
  "integrity" "sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^7.0.0"

"jest-worker@^27.0.2", "jest-worker@^27.3.1", "jest-worker@^27.4.1", "jest-worker@^27.4.6":
  "integrity" "sha512-gHWJF/6Xi5CTG5QCvROr6GcmpIqNYpDJyc8A1h/DyXqH1tD6SnRCM0d3U5msV31D2LB/U+E0M+W4oyvKV44oNw=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"jest@^27.0.0", "jest@^27.4.3":
  "integrity" "sha512-8heYvsx7nV/m8m24Vk26Y87g73Ba6ueUd0MWed/NXMhSZIm62U/llVbS0PJe1SHunbyXjJ/BqG1z9bFjGUIvTg=="
  "resolved" "https://registry.npmjs.org/jest/-/jest-27.4.7.tgz"
  "version" "27.4.7"
  dependencies:
    "@jest/core" "^27.4.7"
    "import-local" "^3.0.2"
    "jest-cli" "^27.4.7"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"js-yaml@^4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"jsdom@^16.6.0":
  "integrity" "sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw=="
  "resolved" "https://registry.npmjs.org/jsdom/-/jsdom-16.7.0.tgz"
  "version" "16.7.0"
  dependencies:
    "abab" "^2.0.5"
    "acorn" "^8.2.4"
    "acorn-globals" "^6.0.0"
    "cssom" "^0.4.4"
    "cssstyle" "^2.3.0"
    "data-urls" "^2.0.0"
    "decimal.js" "^10.2.1"
    "domexception" "^2.0.1"
    "escodegen" "^2.0.0"
    "form-data" "^3.0.0"
    "html-encoding-sniffer" "^2.0.1"
    "http-proxy-agent" "^4.0.1"
    "https-proxy-agent" "^5.0.0"
    "is-potential-custom-element-name" "^1.0.1"
    "nwsapi" "^2.2.0"
    "parse5" "6.0.1"
    "saxes" "^5.0.1"
    "symbol-tree" "^3.2.4"
    "tough-cookie" "^4.0.0"
    "w3c-hr-time" "^1.0.2"
    "w3c-xmlserializer" "^2.0.0"
    "webidl-conversions" "^6.1.0"
    "whatwg-encoding" "^1.0.5"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.5.0"
    "ws" "^7.4.6"
    "xml-name-validator" "^3.0.0"

"jsesc@^2.5.1":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.2":
  "integrity" "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="
  "resolved" "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-schema@^0.4.0":
  "integrity" "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="
  "resolved" "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz"
  "version" "0.4.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json5@^1.0.1":
  "integrity" "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2", "json5@^2.2.0":
  "integrity" "sha512-f+8cldu7X/y7RAJurMEJmdoKXGB/X550w2Nr3tTbezL6RwEE/iMcm+tZnXeoZtKuOq6ft8+CqzEkrIgx1fPoQA=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "minimist" "^1.2.5"

"jsonfile@^6.0.1":
  "integrity" "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ=="
  "resolved" "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "universalify" "^2.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonpointer@^5.0.0":
  "integrity" "sha512-PNYZIdMjVIvVgDSYKTT63Y+KZ6IZvGRNNWcxwD+GNnUz1MKPfv30J8ueCjdwcN0nDx2SlshgyB7Oy0epAzVRRg=="
  "resolved" "https://registry.npmjs.org/jsonpointer/-/jsonpointer-5.0.0.tgz"
  "version" "5.0.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0", "jsx-ast-utils@^3.2.1":
  "integrity" "sha512-uP5vu8xfy2F9A6LGC22KO7e2/vGTS1MhP+18f++ZNlf0Ohaxbc9nIEwHAsejlJKyzfZzU5UIhe5ItYkitcZnZA=="
  "resolved" "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "array-includes" "^3.1.3"
    "object.assign" "^4.1.2"

"jszip@^3.6.0":
  "integrity" "sha512-ghL0tz1XG9ZEmRMcEN2vt7xabrDdqHHeykgARpmZ0BiIctWxM47Vt63ZO2dnp4QYt/xJVLLy5Zv1l/xRdh2byg=="
  "resolved" "https://registry.npmjs.org/jszip/-/jszip-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "lie" "~3.3.0"
    "pako" "~1.0.2"
    "readable-stream" "~2.3.6"
    "set-immediate-shim" "~1.0.1"

"kind-of@^6.0.2":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"kleur@^3.0.3":
  "integrity" "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w=="
  "resolved" "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  "version" "3.0.3"

"klona@^2.0.4", "klona@^2.0.5":
  "integrity" "sha512-pJiBpiXMbt7dkzXe8Ghj/u4FfXOOa98fPW+bihOJ4SjnoijweJrNThJfd3ifXpXhREjpoF2mZVH1GfS9LV3kHQ=="
  "resolved" "https://registry.npmjs.org/klona/-/klona-2.0.5.tgz"
  "version" "2.0.5"

"language-subtag-registry@~0.3.2":
  "integrity" "sha512-L0IqwlIXjilBVVYKFT37X9Ih11Um5NEl9cbJIuU/SwP/zEEAbBPOnEeeuxVMf45ydWQRDQN3Nqc96OgbH1K+Pg=="
  "resolved" "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.21.tgz"
  "version" "0.3.21"

"language-tags@^1.0.5":
  "integrity" "sha1-0yHbxNowuovzAk4ED6XBRmH5GTo="
  "resolved" "https://registry.npmjs.org/language-tags/-/language-tags-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "language-subtag-registry" "~0.3.2"

"leven@^3.1.0":
  "integrity" "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A=="
  "resolved" "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz"
  "version" "3.1.0"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"lie@~3.3.0":
  "integrity" "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ=="
  "resolved" "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "immediate" "~3.0.5"

"lilconfig@^2.0.3", "lilconfig@^2.0.4":
  "integrity" "sha512-bfTIN7lEsiooCocSISTWXkiWJkRqtL9wYtYy+8EK3Y41qh3mpwPU0ycTOgjdY9ErwXCc8QyrQp82bdL0Xkm9yA=="
  "resolved" "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.4.tgz"
  "version" "2.0.4"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"loader-runner@^4.2.0":
  "integrity" "sha512-92+huvxMvYlMzMt0iIOukcwYBFpkYJdpl2xsZ7LrlayO7E8SOv+JJUEK17B/dJIHAOLMfh2dZZ/Y18WgmGtYNw=="
  "resolved" "https://registry.npmjs.org/loader-runner/-/loader-runner-4.2.0.tgz"
  "version" "4.2.0"

"loader-utils@^1.4.0":
  "integrity" "sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^2.0.0":
  "integrity" "sha512-TM57VeHptv569d/GKh6TAYdzKblwDNiumOdkFnejjD0XwTH87K90w3O7AiJRqdQoXygvi1VQTJTLGhJl7WqA7A=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"loader-utils@^3.2.0":
  "integrity" "sha512-HVl9ZqccQihZ7JM85dco1MvO9G+ONvxoGa9rkhzFsneGLKSUg1gJf9bWzhRhcvm2qChhWpebQhP44qxjKIUCaQ=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-3.2.0.tgz"
  "version" "3.2.0"

"locate-path@^2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "^2.0.0"
    "path-exists" "^3.0.0"

"locate-path@^3.0.0":
  "integrity" "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"locate-path@^6.0.0":
  "integrity" "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"lodash._reinterpolate@^3.0.0":
  "integrity" "sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0="
  "resolved" "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz"
  "version" "3.0.0"

"lodash.camelcase@^4.3.0":
  "integrity" "sha1-soqmKIorn8ZRA1x3EfZathkDMaY="
  "resolved" "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  "version" "4.3.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.frompairs@^4.0.1":
  "integrity" "sha1-vE5SB/onV8E25XNhTpZkUGsrG9I="
  "resolved" "https://registry.npmjs.org/lodash.frompairs/-/lodash.frompairs-4.0.1.tgz"
  "version" "4.0.1"

"lodash.isequal@^4.5.0":
  "integrity" "sha1-QVxEePK8wwEgwizhDtMib30+GOA="
  "resolved" "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz"
  "version" "4.5.0"

"lodash.isstring@^4.0.1":
  "integrity" "sha1-1SfftUVuynzJu5XV2ur4i6VKVFE="
  "resolved" "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  "version" "4.0.1"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.omit@^4.5.0":
  "integrity" "sha1-brGa5aHuHdnfC5aeZs4Lf6MLXmA="
  "resolved" "https://registry.npmjs.org/lodash.omit/-/lodash.omit-4.5.0.tgz"
  "version" "4.5.0"

"lodash.pick@^4.4.0":
  "integrity" "sha1-UvBWEP/53tQiYRRB7R/BI6AwAbM="
  "resolved" "https://registry.npmjs.org/lodash.pick/-/lodash.pick-4.4.0.tgz"
  "version" "4.4.0"

"lodash.sortby@^4.7.0":
  "integrity" "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg="
  "resolved" "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz"
  "version" "4.7.0"

"lodash.template@^4.5.0":
  "integrity" "sha512-84vYFxIkmidUiFxidA/KjjH9pAycqW+h980j7Fuz5qxRtO9pgB7MDFTdys1N7A5mcucRiDyEq4fusljItR1T/A=="
  "resolved" "https://registry.npmjs.org/lodash.template/-/lodash.template-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "lodash._reinterpolate" "^3.0.0"
    "lodash.templatesettings" "^4.0.0"

"lodash.templatesettings@^4.0.0":
  "integrity" "sha512-stgLz+i3Aa9mZgnjr/O+v9ruKZsPsndy7qPZOchbqk2cnTU1ZaldKK+v7m54WoKIyxiuMZTKT2H81F8BeAc3ZQ=="
  "resolved" "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "lodash._reinterpolate" "^3.0.0"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.20", "lodash@^4.17.21", "lodash@^4.7.0":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"long@^4.0.0":
  "integrity" "sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA=="
  "resolved" "https://registry.npmjs.org/long/-/long-4.0.0.tgz"
  "version" "4.0.0"

"loose-envify@^1.0.0", "loose-envify@^1.1.0", "loose-envify@^1.3.1", "loose-envify@^1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lower-case@^2.0.2":
  "integrity" "sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg=="
  "resolved" "https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "tslib" "^2.0.3"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"lz-string@^1.4.4":
  "integrity" "sha1-wNjq82BZ9wV5bh40SBHPTEmNOiY="
  "resolved" "https://registry.npmjs.org/lz-string/-/lz-string-1.4.4.tgz"
  "version" "1.4.4"

"magic-string@^0.25.0", "magic-string@^0.25.7":
  "integrity" "sha512-4CrMT5DOHTDk4HYDlzmwu4FVCcIYI8gauveasrdCu2IKIFOJ3f0v/8MDGJCDL9oD2ppz/Av1b0Nj345H9M+XIA=="
  "resolved" "https://registry.npmjs.org/magic-string/-/magic-string-0.25.7.tgz"
  "version" "0.25.7"
  dependencies:
    "sourcemap-codec" "^1.4.4"

"make-dir@^3.0.0", "make-dir@^3.0.2", "make-dir@^3.1.0":
  "integrity" "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"makeerror@1.0.12":
  "integrity" "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg=="
  "resolved" "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "tmpl" "1.0.5"

"mdn-data@2.0.14":
  "integrity" "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="
  "resolved" "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"mdn-data@2.0.4":
  "integrity" "sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA=="
  "resolved" "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.4.tgz"
  "version" "2.0.4"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memfs@^3.1.2", "memfs@^3.2.2":
  "integrity" "sha512-1c9VPVvW5P7I85c35zAdEr1TD5+F11IToIHIlrVIcflfnzPkJa0ZoYEoEdYDP8KgPFoSZ/opDrUsAoZWym3mtw=="
  "resolved" "https://registry.npmjs.org/memfs/-/memfs-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "fs-monkey" "1.0.3"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.3.0", "merge2@^1.4.1":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^4.0.2", "micromatch@^4.0.4":
  "integrity" "sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "braces" "^3.0.1"
    "picomatch" "^2.2.3"

"mime-db@>= 1.43.0 < 2", "mime-db@1.51.0":
  "integrity" "sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.51.0.tgz"
  "version" "1.51.0"

"mime-types@^2.1.12", "mime-types@^2.1.27", "mime-types@^2.1.31", "mime-types@~2.1.17", "mime-types@~2.1.24":
  "integrity" "sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.34.tgz"
  "version" "2.1.34"
  dependencies:
    "mime-db" "1.51.0"

"mime@1.6.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"min-indent@^1.0.0":
  "integrity" "sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg=="
  "resolved" "https://registry.npmjs.org/min-indent/-/min-indent-1.0.1.tgz"
  "version" "1.0.1"

"mini-css-extract-plugin@^2.4.5":
  "integrity" "sha512-Lwgq9qLNyBK6yNLgzssXnq4r2+mB9Mz3cJWlM8kseysHIvTicFhDNimFgY94jjqlwhNzLPsq8wv4X+vOHtMdYA=="
  "resolved" "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "schema-utils" "^4.0.0"

"minimalistic-assert@^1.0.0":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4", "minimatch@3.0.4":
  "integrity" "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.1.1", "minimist@^1.2.0", "minimist@^1.2.5":
  "integrity" "sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw=="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz"
  "version" "1.2.5"

"mkdirp@^0.5.5", "mkdirp@~0.5.1":
  "integrity" "sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"ms@^2.1.1", "ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://registry.npmjs.org/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g=="
  "resolved" "https://registry.npmjs.org/multicast-dns/-/multicast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"nanoid@^3.1.30":
  "integrity" "sha512-fmsZYa9lpn69Ad5eDn7FMcnnSR+8R34W9qJEijxYhTbfOWzr22n1QxCMzXLK+ODyW2973V3Fux959iQoUxzUIA=="
  "resolved" "https://registry.npmjs.org/nanoid/-/nanoid-3.2.0.tgz"
  "version" "3.2.0"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.2":
  "integrity" "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz"
  "version" "0.6.2"

"neo-async@^2.6.2":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"no-case@^3.0.4":
  "integrity" "sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg=="
  "resolved" "https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "lower-case" "^2.0.2"
    "tslib" "^2.0.3"

"node-fetch@2.6.1":
  "integrity" "sha512-V4aYg89jEoVRxRb2fJdAg8FHvI7cEyYdVAh94HH0UIK8oJxUfkjlDQN9RbMx+bEjP7+ggMiFRprSti032Oipxw=="
  "resolved" "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.1.tgz"
  "version" "2.6.1"

"node-fetch@2.6.5":
  "integrity" "sha512-mmlIVHJEu5rnIxgEgez6b9GgWXbkZj5YZ7fx+2r94a2E+Uirsp6HsPTPlomfdHtpt/B0cdKviwkoaM6pyvUOpQ=="
  "resolved" "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.5.tgz"
  "version" "2.6.5"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-forge@^1.2.0":
  "integrity" "sha512-Fcvtbb+zBcZXbTTVwqGA5W+MKBj56UjVRevvchv5XrcyXbmNdesfZL37nlcWOfpgHhgmxApw3tQbTr4CqNmX4w=="
  "resolved" "https://registry.npmjs.org/node-forge/-/node-forge-1.2.1.tgz"
  "version" "1.2.1"

"node-int64@^0.4.0":
  "integrity" "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs="
  "resolved" "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"
  "version" "0.4.0"

"node-releases@^2.0.1":
  "integrity" "sha512-CqyzN6z7Q6aMeF/ktcMVTzhAHCEpf8SOarwpzpf8pNBY2k5/oM34UHldUwp8VKI7uxct2HxSRdJjBaZeESzcxA=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-2.0.1.tgz"
  "version" "2.0.1"

"normalize-css-color@^1.0.2":
  "integrity" "sha1-Apkel8zOxmI/5XOvu/Deah8+n40="
  "resolved" "https://registry.npmjs.org/normalize-css-color/-/normalize-css-color-1.0.2.tgz"
  "version" "1.0.2"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^6.0.1":
  "integrity" "sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A=="
  "resolved" "https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz"
  "version" "6.1.0"

"npm-run-path@^4.0.1":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nth-check@^1.0.2":
  "integrity" "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg=="
  "resolved" "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"nth-check@^2.0.1":
  "integrity" "sha512-it1vE95zF6dTT9lBsYbxvqh0Soy4SPowchj0UBGj/V6cTPnXXtQOPUbhZ6CmGzAD/rW22LQK6E96pcdJXk4A4w=="
  "resolved" "https://registry.npmjs.org/nth-check/-/nth-check-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "boolbase" "^1.0.0"

"nwsapi@^2.2.0":
  "integrity" "sha512-h2AatdwYH+JHiZpv7pt/gSX1XoRGb7L/qSIeuqA6GwYoF9w1vP1cw42TO0aI2pNyshRK5893hNSl+1//vHK7hQ=="
  "resolved" "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.0.tgz"
  "version" "2.2.0"

"object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-hash@^2.2.0":
  "integrity" "sha512-gScRMn0bS5fH+IuwyIFgnh9zBdo4DV+6GhygmWM9HyNJSgS0hScp1f5vjtm7oIIOiT9trXrShAkLFSc2IqKNgw=="
  "resolved" "https://registry.npmjs.org/object-hash/-/object-hash-2.2.0.tgz"
  "version" "2.2.0"

"object-inspect@^1.11.0", "object-inspect@^1.9.0":
  "integrity" "sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g=="
  "resolved" "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.0.tgz"
  "version" "1.12.0"

"object-is@^1.0.1":
  "integrity" "sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw=="
  "resolved" "https://registry.npmjs.org/object-is/-/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object.assign@^4.1.0", "object.assign@^4.1.2":
  "integrity" "sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ=="
  "resolved" "https://registry.npmjs.org/object.assign/-/object.assign-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "has-symbols" "^1.0.1"
    "object-keys" "^1.1.1"

"object.entries@^1.1.5":
  "integrity" "sha512-TyxmjUoZggd4OrrU1W66FMDG6CuqJxsFvymeyXI51+vQLN67zYfZseptRge703kKQdo4uccgAKebXFcRCzk4+g=="
  "resolved" "https://registry.npmjs.org/object.entries/-/object.entries-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.fromentries@^2.0.5":
  "integrity" "sha512-CAyG5mWQRRiBU57Re4FKoTBjXfDoNwdFVH2Y1tS9PqCsfUTymAohOkEMSG3aRNKmv4lV3O7p1et7c187q6bynw=="
  "resolved" "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.getownpropertydescriptors@^2.1.0":
  "integrity" "sha512-VdDoCwvJI4QdC6ndjpqFmoL3/+HxffFBbcJzKi5hwLLqqx3mdbedRpfZDdK0SrOSauj8X4GzBvnDZl4vTN7dOw=="
  "resolved" "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.hasown@^1.1.0":
  "integrity" "sha512-MhjYRfj3GBlhSkDHo6QmvgjRLXQ2zndabdf3nX0yTyZK9rPfxb6uRpAac8HXNLy1GpqWtZ81Qh4v3uOls2sRAg=="
  "resolved" "https://registry.npmjs.org/object.hasown/-/object.hasown-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.values@^1.1.0", "object.values@^1.1.5":
  "integrity" "sha512-QUZRW0ilQ3PnPpbNtgdNV1PDbEqLIiSFB3l+EnGtBQ/8SUTLj1PZwtQHABZtLgwpJZTSZhuGLOGk57Drx2IvYg=="
  "resolved" "https://registry.npmjs.org/object.values/-/object.values-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg=="
  "resolved" "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz"
  "version" "1.1.2"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="
  "resolved" "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^5.1.2":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^8.0.9", "open@^8.4.0":
  "integrity" "sha512-XgFPPM+B28FtCCgSb9I+s9szOC1vZRSwgWsRUA5ylIxRTgKozqjOCrVOqGsYABPYK5qnfqClxZTFBa8PKt2v6Q=="
  "resolved" "https://registry.npmjs.org/open/-/open-8.4.0.tgz"
  "version" "8.4.0"
  dependencies:
    "define-lazy-prop" "^2.0.0"
    "is-docker" "^2.1.1"
    "is-wsl" "^2.2.0"

"optionator@^0.8.1":
  "integrity" "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"optionator@^0.9.1":
  "integrity" "sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.3"

"p-limit@^1.1.0":
  "integrity" "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-limit@^2.0.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-limit@^3.0.2":
  "integrity" "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-locate@^2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "^1.1.0"

"p-locate@^3.0.0":
  "integrity" "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-locate@^5.0.0":
  "integrity" "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"p-map@^4.0.0":
  "integrity" "sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ=="
  "resolved" "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@^4.5.0":
  "integrity" "sha512-e2xXGNhZOZ0lfgR9kL34iGlU8N/KO0xZnQxVEwdeOvpqNDQfdnxIYizvWtK8RglUa3bGqI8g0R/BdfzLMxRkiA=="
  "resolved" "https://registry.npmjs.org/p-retry/-/p-retry-4.6.1.tgz"
  "version" "4.6.1"
  dependencies:
    "@types/retry" "^0.12.0"
    "retry" "^0.13.1"

"p-try@^1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz"
  "version" "1.0.0"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~1.0.2":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"param-case@^3.0.4":
  "integrity" "sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A=="
  "resolved" "https://registry.npmjs.org/param-case/-/param-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "dot-case" "^3.0.4"
    "tslib" "^2.0.3"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-json@^5.0.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse5@6.0.1":
  "integrity" "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw=="
  "resolved" "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascal-case@^3.1.2":
  "integrity" "sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g=="
  "resolved" "https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.6", "path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"performance-now@^2.1.0":
  "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picocolors@^0.2.1":
  "integrity" "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz"
  "version" "0.2.1"

"picocolors@^1.0.0":
  "integrity" "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.2.2", "picomatch@^2.2.3":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pirates@^4.0.4":
  "integrity" "sha512-ZIrVPH+A52Dw84R0L3/VS9Op04PuQ2SEoJL6bkshmiTic/HldyW9Tf7oH5mhJZBK7NmDx27vSMrYEXPXclpDKw=="
  "resolved" "https://registry.npmjs.org/pirates/-/pirates-4.0.4.tgz"
  "version" "4.0.4"

"pkg-dir@^4.1.0", "pkg-dir@^4.2.0":
  "integrity" "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ=="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pkg-up@^3.1.0":
  "integrity" "sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA=="
  "resolved" "https://registry.npmjs.org/pkg-up/-/pkg-up-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "find-up" "^3.0.0"

"portfinder@^1.0.28":
  "integrity" "sha512-Se+2isanIcEqf2XMHjyUKskczxbPH7dQnlMjXX6+dybayyHvAf/TCgyMRlzf/B6QDhAEFOGes0pzRo3by4AbMA=="
  "resolved" "https://registry.npmjs.org/portfinder/-/portfinder-1.0.28.tgz"
  "version" "1.0.28"
  dependencies:
    "async" "^2.6.2"
    "debug" "^3.1.1"
    "mkdirp" "^0.5.5"

"postcss-attribute-case-insensitive@^5.0.0":
  "integrity" "sha512-b4g9eagFGq9T5SWX4+USfVyjIb3liPnjhHHRMP7FMB2kFVpYyfEscV0wP3eaXhKlcHKUut8lt5BGoeylWA/dBQ=="
  "resolved" "https://registry.npmjs.org/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "postcss-selector-parser" "^6.0.2"

"postcss-browser-comments@^4":
  "integrity" "sha512-X9X9/WN3KIvY9+hNERUqX9gncsgBA25XaeR+jshHz2j8+sYyHktHw1JdKuMjeLpGktXidqDhA7b/qm1mrBDmgg=="
  "resolved" "https://registry.npmjs.org/postcss-browser-comments/-/postcss-browser-comments-4.0.0.tgz"
  "version" "4.0.0"

"postcss-calc@^8.2.0":
  "integrity" "sha512-B5R0UeB4zLJvxNt1FVCaDZULdzsKLPc6FhjFJ+xwFiq7VG4i9cuaJLxVjNtExNK8ocm3n2o4unXXLiVX1SCqxA=="
  "resolved" "https://registry.npmjs.org/postcss-calc/-/postcss-calc-8.2.2.tgz"
  "version" "8.2.2"
  dependencies:
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.2"

"postcss-color-functional-notation@^4.2.1":
  "integrity" "sha512-62OBIXCjRXpQZcFOYIXwXBlpAVWrYk8ek1rcjvMING4Q2cf0ipyN9qT+BhHA6HmftGSEnFQu2qgKO3gMscl3Rw=="
  "resolved" "https://registry.npmjs.org/postcss-color-functional-notation/-/postcss-color-functional-notation-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-color-hex-alpha@^8.0.2":
  "integrity" "sha512-gyx8RgqSmGVK156NAdKcsfkY3KPGHhKqvHTL3hhveFrBBToguKFzhyiuk3cljH6L4fJ0Kv+JENuPXs1Wij27Zw=="
  "resolved" "https://registry.npmjs.org/postcss-color-hex-alpha/-/postcss-color-hex-alpha-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-color-rebeccapurple@^7.0.2":
  "integrity" "sha512-SFc3MaocHaQ6k3oZaFwH8io6MdypkUtEy/eXzXEB1vEQlO3S3oDc/FSZA8AsS04Z25RirQhlDlHLh3dn7XewWw=="
  "resolved" "https://registry.npmjs.org/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-colormin@^5.2.3":
  "integrity" "sha512-dra4xoAjub2wha6RUXAgadHEn2lGxbj8drhFcIGLOMn914Eu7DkPUurugDXgstwttCYkJtZ/+PkWRWdp3UHRIA=="
  "resolved" "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-5.2.3.tgz"
  "version" "5.2.3"
  dependencies:
    "browserslist" "^4.16.6"
    "caniuse-api" "^3.0.0"
    "colord" "^2.9.1"
    "postcss-value-parser" "^4.2.0"

"postcss-convert-values@^5.0.2":
  "integrity" "sha512-KQ04E2yadmfa1LqXm7UIDwW1ftxU/QWZmz6NKnHnUvJ3LEYbbcX6i329f/ig+WnEByHegulocXrECaZGLpL8Zg=="
  "resolved" "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.1.0"

"postcss-custom-media@^8.0.0":
  "integrity" "sha512-FvO2GzMUaTN0t1fBULDeIvxr5IvbDXcIatt6pnJghc736nqNgsGao5NT+5+WVLAQiTt6Cb3YUms0jiPaXhL//g=="
  "resolved" "https://registry.npmjs.org/postcss-custom-media/-/postcss-custom-media-8.0.0.tgz"
  "version" "8.0.0"

"postcss-custom-properties@^12.1.2":
  "integrity" "sha512-rtu3otIeY532PnEuuBrIIe+N+pcdbX/7JMZfrcL09wc78YayrHw5E8UkDfvnlOhEUrI4ptCuzXQfj+Or6spbGA=="
  "resolved" "https://registry.npmjs.org/postcss-custom-properties/-/postcss-custom-properties-12.1.3.tgz"
  "version" "12.1.3"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-custom-selectors@^6.0.0":
  "integrity" "sha512-/1iyBhz/W8jUepjGyu7V1OPcGbc636snN1yXEQCinb6Bwt7KxsiU7/bLQlp8GwAXzCh7cobBU5odNn/2zQWR8Q=="
  "resolved" "https://registry.npmjs.org/postcss-custom-selectors/-/postcss-custom-selectors-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "postcss-selector-parser" "^6.0.4"

"postcss-dir-pseudo-class@^6.0.3":
  "integrity" "sha512-qiPm+CNAlgXiMf0J5IbBBEXA9l/Q5HGsNGkL3znIwT2ZFRLGY9U2fTUpa4lqCUXQOxaLimpacHeQC80BD2qbDw=="
  "resolved" "https://registry.npmjs.org/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "postcss-selector-parser" "^6.0.8"

"postcss-discard-comments@^5.0.1":
  "integrity" "sha512-lgZBPTDvWrbAYY1v5GYEv8fEO/WhKOu/hmZqmCYfrpD6eyDWWzAOsl2rF29lpvziKO02Gc5GJQtlpkTmakwOWg=="
  "resolved" "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-5.0.1.tgz"
  "version" "5.0.1"

"postcss-discard-duplicates@^5.0.1":
  "integrity" "sha512-svx747PWHKOGpAXXQkCc4k/DsWo+6bc5LsVrAsw+OU+Ibi7klFZCyX54gjYzX4TH+f2uzXjRviLARxkMurA2bA=="
  "resolved" "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-5.0.1.tgz"
  "version" "5.0.1"

"postcss-discard-empty@^5.0.1":
  "integrity" "sha512-vfU8CxAQ6YpMxV2SvMcMIyF2LX1ZzWpy0lqHDsOdaKKLQVQGVP1pzhrI9JlsO65s66uQTfkQBKBD/A5gp9STFw=="
  "resolved" "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-5.0.1.tgz"
  "version" "5.0.1"

"postcss-discard-overridden@^5.0.2":
  "integrity" "sha512-+56BLP6NSSUuWUXjRgAQuho1p5xs/hU5Sw7+xt9S3JSg+7R6+WMGnJW7Hre/6tTuZ2xiXMB42ObkiZJ2hy/Pew=="
  "resolved" "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-5.0.2.tgz"
  "version" "5.0.2"

"postcss-double-position-gradients@^3.0.4":
  "integrity" "sha512-qz+s5vhKJlsHw8HjSs+HVk2QGFdRyC68KGRQGX3i+GcnUjhWhXQEmCXW6siOJkZ1giu0ddPwSO6I6JdVVVPoog=="
  "resolved" "https://registry.npmjs.org/postcss-double-position-gradients/-/postcss-double-position-gradients-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-env-function@^4.0.4":
  "integrity" "sha512-0ltahRTPtXSIlEZFv7zIvdEib7HN0ZbUQxrxIKn8KbiRyhALo854I/CggU5lyZe6ZBvSTJ6Al2vkZecI2OhneQ=="
  "resolved" "https://registry.npmjs.org/postcss-env-function/-/postcss-env-function-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-flexbugs-fixes@^5.0.2":
  "integrity" "sha512-18f9voByak7bTktR2QgDveglpn9DTbBWPUzSOe9g0N4WR/2eSt6Vrcbf0hmspvMI6YWGywz6B9f7jzpFNJJgnQ=="
  "resolved" "https://registry.npmjs.org/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-5.0.2.tgz"
  "version" "5.0.2"

"postcss-focus-visible@^6.0.3":
  "integrity" "sha512-ozOsg+L1U8S+rxSHnJJiET6dNLyADcPHhEarhhtCI9DBLGOPG/2i4ddVoFch9LzrBgb8uDaaRI4nuid2OM82ZA=="
  "resolved" "https://registry.npmjs.org/postcss-focus-visible/-/postcss-focus-visible-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "postcss-selector-parser" "^6.0.8"

"postcss-focus-within@^5.0.3":
  "integrity" "sha512-fk9y2uFS6/Kpp7/A9Hz9Z4rlFQ8+tzgBcQCXAFSrXFGAbKx+4ZZOmmfHuYjCOMegPWoz0pnC6fNzi8j7Xyqp5Q=="
  "resolved" "https://registry.npmjs.org/postcss-focus-within/-/postcss-focus-within-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "postcss-selector-parser" "^6.0.8"

"postcss-font-variant@^5.0.0":
  "integrity" "sha512-1fmkBaCALD72CK2a9i468mA/+tr9/1cBxRRMXOUaZqO43oWPR5imcyPjXwuv7PXbCid4ndlP5zWhidQVVa3hmA=="
  "resolved" "https://registry.npmjs.org/postcss-font-variant/-/postcss-font-variant-5.0.0.tgz"
  "version" "5.0.0"

"postcss-gap-properties@^3.0.2":
  "integrity" "sha512-EaMy/pbxtQnKDsnbEjdqlkCkROTQZzolcLKgIE+3b7EuJfJydH55cZeHfm+MtIezXRqhR80VKgaztO/vHq94Fw=="
  "resolved" "https://registry.npmjs.org/postcss-gap-properties/-/postcss-gap-properties-3.0.2.tgz"
  "version" "3.0.2"

"postcss-image-set-function@^4.0.4":
  "integrity" "sha512-BlEo9gSTj66lXjRNByvkMK9dEdEGFXRfGjKRi9fo8s0/P3oEk74cAoonl/utiM50E2OPVb/XSu+lWvdW4KtE/Q=="
  "resolved" "https://registry.npmjs.org/postcss-image-set-function/-/postcss-image-set-function-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-initial@^4.0.1":
  "integrity" "sha512-0ueD7rPqX8Pn1xJIjay0AZeIuDoF+V+VvMt/uOnn+4ezUKhZM/NokDeP6DwMNyIoYByuN/94IQnt5FEkaN59xQ=="
  "resolved" "https://registry.npmjs.org/postcss-initial/-/postcss-initial-4.0.1.tgz"
  "version" "4.0.1"

"postcss-js@^4.0.0":
  "integrity" "sha512-77QESFBwgX4irogGVPgQ5s07vLvFqWr228qZY+w6lW599cRlK/HmnlivnnVUxkjHnCu4J16PDMHcH+e+2HbvTQ=="
  "resolved" "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "camelcase-css" "^2.0.1"

"postcss-lab-function@^4.0.3":
  "integrity" "sha512-MH4tymWmefdZQ7uVG/4icfLjAQmH6o2NRYyVh2mKoB4RXJp9PjsyhZwhH4ouaCQHvg+qJVj3RzeAR1EQpIlXZA=="
  "resolved" "https://registry.npmjs.org/postcss-lab-function/-/postcss-lab-function-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-load-config@^3.1.0":
  "integrity" "sha512-c/9XYboIbSEUZpiD1UQD0IKiUe8n9WHYV7YFe7X7J+ZwCsEKkUJSFWjS9hBU1RR9THR7jMXst8sxiqP0jjo2mg=="
  "resolved" "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "lilconfig" "^2.0.4"
    "yaml" "^1.10.2"

"postcss-loader@^6.2.1":
  "integrity" "sha512-WbbYpmAaKcux/P66bZ40bpWsBucjx/TTgVVzRZ9yUO8yQfVBlameJ0ZGVaPfH64hNSBh63a+ICP5nqOpBA0w+Q=="
  "resolved" "https://registry.npmjs.org/postcss-loader/-/postcss-loader-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "cosmiconfig" "^7.0.0"
    "klona" "^2.0.5"
    "semver" "^7.3.5"

"postcss-logical@^5.0.3":
  "integrity" "sha512-P5NcHWYrif0vK8rgOy/T87vg0WRIj3HSknrvp1wzDbiBeoDPVmiVRmkown2eSQdpPveat/MC1ess5uhzZFVnqQ=="
  "resolved" "https://registry.npmjs.org/postcss-logical/-/postcss-logical-5.0.3.tgz"
  "version" "5.0.3"

"postcss-media-minmax@^5.0.0":
  "integrity" "sha512-yDUvFf9QdFZTuCUg0g0uNSHVlJ5X1lSzDZjPSFaiCWvjgsvu8vEVxtahPrLMinIDEEGnx6cBe6iqdx5YWz08wQ=="
  "resolved" "https://registry.npmjs.org/postcss-media-minmax/-/postcss-media-minmax-5.0.0.tgz"
  "version" "5.0.0"

"postcss-merge-longhand@^5.0.4":
  "integrity" "sha512-2lZrOVD+d81aoYkZDpWu6+3dTAAGkCKbV5DoRhnIR7KOULVrI/R7bcMjhrH9KTRy6iiHKqmtG+n/MMj1WmqHFw=="
  "resolved" "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "postcss-value-parser" "^4.1.0"
    "stylehacks" "^5.0.1"

"postcss-merge-rules@^5.0.4":
  "integrity" "sha512-yOj7bW3NxlQxaERBB0lEY1sH5y+RzevjbdH4DBJurjKERNpknRByFNdNe+V72i5pIZL12woM9uGdS5xbSB+kDQ=="
  "resolved" "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "browserslist" "^4.16.6"
    "caniuse-api" "^3.0.0"
    "cssnano-utils" "^3.0.0"
    "postcss-selector-parser" "^6.0.5"

"postcss-minify-font-values@^5.0.2":
  "integrity" "sha512-R6MJZryq28Cw0AmnyhXrM7naqJZZLoa1paBltIzh2wM7yb4D45TLur+eubTQ4jCmZU9SGeZdWsc5KcSoqTMeTg=="
  "resolved" "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-minify-gradients@^5.0.4":
  "integrity" "sha512-RVwZA7NC4R4J76u8X0Q0j+J7ItKUWAeBUJ8oEEZWmtv3Xoh19uNJaJwzNpsydQjk6PkuhRrK+YwwMf+c+68EYg=="
  "resolved" "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "colord" "^2.9.1"
    "cssnano-utils" "^3.0.0"
    "postcss-value-parser" "^4.2.0"

"postcss-minify-params@^5.0.3":
  "integrity" "sha512-NY92FUikE+wralaiVexFd5gwb7oJTIDhgTNeIw89i1Ymsgt4RWiPXfz3bg7hDy4NL6gepcThJwOYNtZO/eNi7Q=="
  "resolved" "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "browserslist" "^4.16.6"
    "cssnano-utils" "^3.0.0"
    "postcss-value-parser" "^4.2.0"

"postcss-minify-selectors@^5.1.1":
  "integrity" "sha512-TOzqOPXt91O2luJInaVPiivh90a2SIK5Nf1Ea7yEIM/5w+XA5BGrZGUSW8aEx9pJ/oNj7ZJBhjvigSiBV+bC1Q=="
  "resolved" "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "postcss-selector-parser" "^6.0.5"

"postcss-modules-extract-imports@^3.0.0":
  "integrity" "sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw=="
  "resolved" "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.0.0.tgz"
  "version" "3.0.0"

"postcss-modules-local-by-default@^4.0.0":
  "integrity" "sha512-sT7ihtmGSF9yhm6ggikHdV0hlziDTX7oFoXtuVWeDd3hHObNkcHRo9V3yg7vCAY7cONyxJC/XXCmmiHHcvX7bQ=="
  "resolved" "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "icss-utils" "^5.0.0"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.1.0"

"postcss-modules-scope@^3.0.0":
  "integrity" "sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg=="
  "resolved" "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss-selector-parser" "^6.0.4"

"postcss-modules-values@^4.0.0":
  "integrity" "sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ=="
  "resolved" "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "icss-utils" "^5.0.0"

"postcss-nested@5.0.6":
  "integrity" "sha512-rKqm2Fk0KbA8Vt3AdGN0FB9OBOMDVajMG6ZCf/GoHgdxUJ4sBFp0A/uMIRm+MJUdo33YXEtjqIz8u7DAp8B7DA=="
  "resolved" "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.6.tgz"
  "version" "5.0.6"
  dependencies:
    "postcss-selector-parser" "^6.0.6"

"postcss-nesting@^10.1.2":
  "integrity" "sha512-dJGmgmsvpzKoVMtDMQQG/T6FSqs6kDtUDirIfl4KnjMCiY9/ETX8jdKyCd20swSRAbUYkaBKV20pxkzxoOXLqQ=="
  "resolved" "https://registry.npmjs.org/postcss-nesting/-/postcss-nesting-10.1.2.tgz"
  "version" "10.1.2"
  dependencies:
    "postcss-selector-parser" "^6.0.8"

"postcss-normalize-charset@^5.0.1":
  "integrity" "sha512-6J40l6LNYnBdPSk+BHZ8SF+HAkS4q2twe5jnocgd+xWpz/mx/5Sa32m3W1AA8uE8XaXN+eg8trIlfu8V9x61eg=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-5.0.1.tgz"
  "version" "5.0.1"

"postcss-normalize-display-values@^5.0.2":
  "integrity" "sha512-RxXoJPUR0shSjkMMzgEZDjGPrgXUVYyWA/YwQRicb48H15OClPuaDR7tYokLAlGZ2tCSENEN5WxjgxSD5m4cUw=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-positions@^5.0.2":
  "integrity" "sha512-tqghWFVDp2btqFg1gYob1etPNxXLNh3uVeWgZE2AQGh6b2F8AK2Gj36v5Vhyh+APwIzNjmt6jwZ9pTBP+/OM8g=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-repeat-style@^5.0.2":
  "integrity" "sha512-/rIZn8X9bBzC7KvY4iKUhXUGW3MmbXwfPF23jC9wT9xTi7kAvgj8sEgwxjixBmoL6MVa4WOgxNz2hAR6wTK8tw=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-string@^5.0.2":
  "integrity" "sha512-zaI1yzwL+a/FkIzUWMQoH25YwCYxi917J4pYm1nRXtdgiCdnlTkx5eRzqWEC64HtRa06WCJ9TIutpb6GmW4gFw=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-timing-functions@^5.0.2":
  "integrity" "sha512-Ao0PP6MoYsRU1LxeVUW740ioknvdIUmfr6uAA3xWlQJ9s69/Tupy8qwhuKG3xWfl+KvLMAP9p2WXF9cwuk/7Bg=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-unicode@^5.0.2":
  "integrity" "sha512-3y/V+vjZ19HNcTizeqwrbZSUsE69ZMRHfiiyLAJb7C7hJtYmM4Gsbajy7gKagu97E8q5rlS9k8FhojA8cpGhWw=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "browserslist" "^4.16.6"
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-url@^5.0.4":
  "integrity" "sha512-cNj3RzK2pgQQyNp7dzq0dqpUpQ/wYtdDZM3DepPmFjCmYIfceuD9VIAcOdvrNetjIU65g1B4uwdP/Krf6AFdXg=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "normalize-url" "^6.0.1"
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-whitespace@^5.0.2":
  "integrity" "sha512-CXBx+9fVlzSgbk0IXA/dcZn9lXixnQRndnsPC5ht3HxlQ1bVh77KQDL1GffJx1LTzzfae8ftMulsjYmO2yegxA=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize@^10.0.1":
  "integrity" "sha512-+5w18/rDev5mqERcG3W5GZNMJa1eoYYNGo8gB7tEwaos0ajk3ZXAI4mHGcNT47NE+ZnZD1pEpUOFLvltIwmeJA=="
  "resolved" "https://registry.npmjs.org/postcss-normalize/-/postcss-normalize-10.0.1.tgz"
  "version" "10.0.1"
  dependencies:
    "@csstools/normalize.css" "*"
    "postcss-browser-comments" "^4"
    "sanitize.css" "*"

"postcss-ordered-values@^5.0.3":
  "integrity" "sha512-T9pDS+P9bWeFvqivXd5ACzQmrCmHjv3ZP+djn8E1UZY7iK79pFSm7i3WbKw2VSmFmdbMm8sQ12OPcNpzBo3Z2w=="
  "resolved" "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "cssnano-utils" "^3.0.0"
    "postcss-value-parser" "^4.2.0"

"postcss-overflow-shorthand@^3.0.2":
  "integrity" "sha512-odBMVt6PTX7jOE9UNvmnLrFzA9pXS44Jd5shFGGtSHY80QCuJF+14McSy0iavZggRZ9Oj//C9vOKQmexvyEJMg=="
  "resolved" "https://registry.npmjs.org/postcss-overflow-shorthand/-/postcss-overflow-shorthand-3.0.2.tgz"
  "version" "3.0.2"

"postcss-page-break@^3.0.4":
  "integrity" "sha512-1JGu8oCjVXLa9q9rFTo4MbeeA5FMe00/9C7lN4va606Rdb+HkxXtXsmEDrIraQ11fGz/WvKWa8gMuCKkrXpTsQ=="
  "resolved" "https://registry.npmjs.org/postcss-page-break/-/postcss-page-break-3.0.4.tgz"
  "version" "3.0.4"

"postcss-place@^7.0.3":
  "integrity" "sha512-tDQ3m+GYoOar+KoQgj+pwPAvGHAp/Sby6vrFiyrELrMKQJ4AejL0NcS0mm296OKKYA2SRg9ism/hlT/OLhBrdQ=="
  "resolved" "https://registry.npmjs.org/postcss-place/-/postcss-place-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-preset-env@^7.0.1":
  "integrity" "sha512-Ok0DhLfwrcNGrBn8sNdy1uZqWRk/9FId0GiQ39W4ILop5GHtjJs8bu1MY9isPwHInpVEPWjb4CEcEaSbBLpfwA=="
  "resolved" "https://registry.npmjs.org/postcss-preset-env/-/postcss-preset-env-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "autoprefixer" "^10.4.2"
    "browserslist" "^4.19.1"
    "caniuse-lite" "^1.0.30001299"
    "css-blank-pseudo" "^3.0.2"
    "css-has-pseudo" "^3.0.3"
    "css-prefers-color-scheme" "^6.0.2"
    "cssdb" "^5.0.0"
    "postcss-attribute-case-insensitive" "^5.0.0"
    "postcss-color-functional-notation" "^4.2.1"
    "postcss-color-hex-alpha" "^8.0.2"
    "postcss-color-rebeccapurple" "^7.0.2"
    "postcss-custom-media" "^8.0.0"
    "postcss-custom-properties" "^12.1.2"
    "postcss-custom-selectors" "^6.0.0"
    "postcss-dir-pseudo-class" "^6.0.3"
    "postcss-double-position-gradients" "^3.0.4"
    "postcss-env-function" "^4.0.4"
    "postcss-focus-visible" "^6.0.3"
    "postcss-focus-within" "^5.0.3"
    "postcss-font-variant" "^5.0.0"
    "postcss-gap-properties" "^3.0.2"
    "postcss-image-set-function" "^4.0.4"
    "postcss-initial" "^4.0.1"
    "postcss-lab-function" "^4.0.3"
    "postcss-logical" "^5.0.3"
    "postcss-media-minmax" "^5.0.0"
    "postcss-nesting" "^10.1.2"
    "postcss-overflow-shorthand" "^3.0.2"
    "postcss-page-break" "^3.0.4"
    "postcss-place" "^7.0.3"
    "postcss-pseudo-class-any-link" "^7.0.2"
    "postcss-replace-overflow-wrap" "^4.0.0"
    "postcss-selector-not" "^5.0.0"

"postcss-pseudo-class-any-link@^7.0.2":
  "integrity" "sha512-CG35J1COUH7OOBgpw5O+0koOLUd5N4vUGKUqSAuIe4GiuLHWU96Pqp+UPC8QITTd12zYAFx76pV7qWT/0Aj/TA=="
  "resolved" "https://registry.npmjs.org/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "postcss-selector-parser" "^6.0.8"

"postcss-reduce-initial@^5.0.2":
  "integrity" "sha512-v/kbAAQ+S1V5v9TJvbGkV98V2ERPdU6XvMcKMjqAlYiJ2NtsHGlKYLPjWWcXlaTKNxooId7BGxeraK8qXvzKtw=="
  "resolved" "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "browserslist" "^4.16.6"
    "caniuse-api" "^3.0.0"

"postcss-reduce-transforms@^5.0.2":
  "integrity" "sha512-25HeDeFsgiPSUx69jJXZn8I06tMxLQJJNF5h7i9gsUg8iP4KOOJ8EX8fj3seeoLt3SLU2YDD6UPnDYVGUO7DEA=="
  "resolved" "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-replace-overflow-wrap@^4.0.0":
  "integrity" "sha512-KmF7SBPphT4gPPcKZc7aDkweHiKEEO8cla/GjcBK+ckKxiZslIu3C4GCRW3DNfL0o7yW7kMQu9xlZ1kXRXLXtw=="
  "resolved" "https://registry.npmjs.org/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-4.0.0.tgz"
  "version" "4.0.0"

"postcss-selector-not@^5.0.0":
  "integrity" "sha512-/2K3A4TCP9orP4TNS7u3tGdRFVKqz/E6pX3aGnriPG0jU78of8wsUcqE4QAhWEU0d+WnMSF93Ah3F//vUtK+iQ=="
  "resolved" "https://registry.npmjs.org/postcss-selector-not/-/postcss-selector-not-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "balanced-match" "^1.0.0"

"postcss-selector-parser@^6.0.2", "postcss-selector-parser@^6.0.4", "postcss-selector-parser@^6.0.5", "postcss-selector-parser@^6.0.6", "postcss-selector-parser@^6.0.8":
  "integrity" "sha512-D5PG53d209Z1Uhcc0qAZ5U3t5HagH3cxu+WLZ22jt3gLUpXM4eXXfiO14jiDWST3NNooX/E8wISfOhZ9eIjGTQ=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.8.tgz"
  "version" "6.0.8"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-svgo@^5.0.3":
  "integrity" "sha512-41XZUA1wNDAZrQ3XgWREL/M2zSw8LJPvb5ZWivljBsUQAGoEKMYm6okHsTjJxKYI4M75RQEH4KYlEM52VwdXVA=="
  "resolved" "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "postcss-value-parser" "^4.1.0"
    "svgo" "^2.7.0"

"postcss-unique-selectors@^5.0.2":
  "integrity" "sha512-w3zBVlrtZm7loQWRPVC0yjUwwpty7OM6DnEHkxcSQXO1bMS3RJ+JUS5LFMSDZHJcvGsRwhZinCWVqn8Kej4EDA=="
  "resolved" "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "postcss-selector-parser" "^6.0.5"

"postcss-value-parser@^4.0.2", "postcss-value-parser@^4.1.0", "postcss-value-parser@^4.2.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^7.0.0 || ^8.0.1", "postcss@^8", "postcss@^8.0.0", "postcss@^8.0.2", "postcss@^8.0.3", "postcss@^8.0.9", "postcss@^8.1.0", "postcss@^8.1.2", "postcss@^8.1.4", "postcss@^8.2.14", "postcss@^8.2.15", "postcss@^8.2.2", "postcss@^8.3", "postcss@^8.3.3", "postcss@^8.3.5", "postcss@^8.4", "postcss@^8.4.4", "postcss@>= 8", "postcss@>=8":
  "integrity" "sha512-jBDboWM8qpaqwkMwItqTQTiFikhs/67OYVvblFFTM7MrZjt6yMKd6r2kgXizEbTTljacm4NldIlZnhbjr84QYg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-8.4.5.tgz"
  "version" "8.4.5"
  dependencies:
    "nanoid" "^3.1.30"
    "picocolors" "^1.0.0"
    "source-map-js" "^1.0.1"

"postcss@^7.0.35":
  "integrity" "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz"
  "version" "7.0.39"
  dependencies:
    "picocolors" "^0.2.1"
    "source-map" "^0.6.1"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"pretty-bytes@^5.3.0", "pretty-bytes@^5.4.1":
  "integrity" "sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg=="
  "resolved" "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz"
  "version" "5.6.0"

"pretty-error@^4.0.0":
  "integrity" "sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw=="
  "resolved" "https://registry.npmjs.org/pretty-error/-/pretty-error-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "lodash" "^4.17.20"
    "renderkid" "^3.0.0"

"pretty-format@^27.0.0", "pretty-format@^27.0.2", "pretty-format@^27.4.6":
  "integrity" "sha512-NblstegA1y/RJW2VyML+3LlpFjzx62cUrtBIKIWDXEDkjNeleA7Od7nrzcs/VLQvAeV4CgSYhrN39DRN88Qi/g=="
  "resolved" "https://registry.npmjs.org/pretty-format/-/pretty-format-27.4.6.tgz"
  "version" "27.4.6"
  dependencies:
    "ansi-regex" "^5.0.1"
    "ansi-styles" "^5.0.0"
    "react-is" "^17.0.1"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"promise-polyfill@8.1.3":
  "integrity" "sha512-MG5r82wBzh7pSKDRa9y+vllNHz3e3d4CNj1PQE4BQYxLme0gKYYBm9YENq+UkEikyZ0XbiGWxYlVw3Rl9O/U8g=="
  "resolved" "https://registry.npmjs.org/promise-polyfill/-/promise-polyfill-8.1.3.tgz"
  "version" "8.1.3"

"promise@^7.1.1":
  "integrity" "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg=="
  "resolved" "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "asap" "~2.0.3"

"promise@^8.1.0":
  "integrity" "sha512-W04AqnILOL/sPRXziNicCjSNRruLAuIHEOVBazepu0545DDNGYHz7ar9ZgZ1fMU8/MA4mVxp5rkBWRi6OXIy3Q=="
  "resolved" "https://registry.npmjs.org/promise/-/promise-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "asap" "~2.0.6"

"prompts@^2.0.1", "prompts@^2.4.2":
  "integrity" "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q=="
  "resolved" "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "kleur" "^3.0.3"
    "sisteransi" "^1.0.5"

"prop-types-extra@^1.1.0":
  "integrity" "sha512-59+AHNnHYCdiC+vMwY52WmvP5dM3QLeoumYuEyceQDi9aEhtwN9zIQ2ZNo25sMyXnbh32h+P1ezDsUpUH3JAew=="
  "resolved" "https://registry.npmjs.org/prop-types-extra/-/prop-types-extra-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "react-is" "^16.3.2"
    "warning" "^4.0.0"

"prop-types@^15.6.0", "prop-types@^15.6.2", "prop-types@^15.7.2", "prop-types@^15.8.1":
  "integrity" "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg=="
  "resolved" "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  "version" "15.8.1"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.13.1"

"protobufjs@^6.10.0":
  "integrity" "sha512-4BQJoPooKJl2G9j3XftkIXjoC9C0Av2NOrWmbLWT1vH32GcSUHjM0Arra6UfTsVyfMAuFzaLucXn1sadxJydAw=="
  "resolved" "https://registry.npmjs.org/protobufjs/-/protobufjs-6.11.2.tgz"
  "version" "6.11.2"
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/long" "^4.0.1"
    "@types/node" ">=13.7.0"
    "long" "^4.0.0"

"proxy-addr@~2.0.7":
  "integrity" "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg=="
  "resolved" "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"psl@^1.1.33":
  "integrity" "sha512-RIdOzyoavK+hA18OGGWDqUTsCLhtA7IcZ/6NCs4fFJaHBDab+pDDmDIByWFRQJq2Cd7r1OoQxBGKOaztq+hjIQ=="
  "resolved" "https://registry.npmjs.org/psl/-/psl-1.8.0.tgz"
  "version" "1.8.0"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  "version" "2.1.1"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://registry.npmjs.org/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"qs@6.9.6":
  "integrity" "sha512-TIRk4aqYLNoJUbd+g2lEdz5kLWIuTMRagAXxl78Q0RiVjAOugHmeKNGdd3cwo/ktpf9aL9epCfFqWDEKysUlLQ=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.9.6.tgz"
  "version" "6.9.6"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"quick-lru@^5.1.1":
  "integrity" "sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA=="
  "resolved" "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz"
  "version" "5.1.1"

"raf@^3.4.1":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"randombytes@^2.1.0":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.4.2":
  "integrity" "sha512-RPMAFUJP19WIet/99ngh6Iv8fzAbqum4Li7AD6DtGaW2RpMB/11xDoalPiJMTbu6I3hkbMVkATvZrqb9EEqeeQ=="
  "resolved" "https://registry.npmjs.org/raw-body/-/raw-body-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "bytes" "3.1.1"
    "http-errors" "1.8.1"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"react-app-polyfill@^3.0.0":
  "integrity" "sha512-sZ41cxiU5llIB003yxxQBYrARBqe0repqPTTYBTmMqTz9szeBbE37BehCE891NZsmdZqqP+xWKdT3eo3vOzN8w=="
  "resolved" "https://registry.npmjs.org/react-app-polyfill/-/react-app-polyfill-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "core-js" "^3.19.2"
    "object-assign" "^4.1.1"
    "promise" "^8.1.0"
    "raf" "^3.4.1"
    "regenerator-runtime" "^0.13.9"
    "whatwg-fetch" "^3.6.2"

"react-app-rewired@^2.1.11":
  "integrity" "sha512-zRIqJUPsCoPnfYtea3xgPbKR42vx0NoH5oo8R8FELXqzkjJHa39V6zD8CAdkLJoYL8V3JScWHAfKMZOzi1Ydmw=="
  "resolved" "https://registry.npmjs.org/react-app-rewired/-/react-app-rewired-2.1.11.tgz"
  "version" "2.1.11"
  dependencies:
    "semver" "^5.6.0"

"react-bootstrap@^2.1.1":
  "integrity" "sha512-Igagk6LziNW/HgBlMVx+QiwPVt/oqrZ7tiBKgv31VYc/56kJEU0Y+BCJS6hrQP6QmmIpdVtX8TRaanv9xsmW5A=="
  "resolved" "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "@babel/runtime" "^7.14.0"
    "@restart/hooks" "^0.4.5"
    "@restart/ui" "^0.2.5"
    "@types/invariant" "^2.2.33"
    "@types/prop-types" "^15.7.3"
    "@types/react" ">=16.14.8"
    "@types/react-transition-group" "^4.4.1"
    "@types/warning" "^3.0.0"
    "classnames" "^2.3.1"
    "dom-helpers" "^5.2.1"
    "invariant" "^2.2.4"
    "prop-types" "^15.7.2"
    "prop-types-extra" "^1.1.0"
    "react-transition-group" "^4.4.1"
    "uncontrollable" "^7.2.1"
    "warning" "^4.0.3"

"react-dev-utils@^12.0.0":
  "integrity" "sha512-xBQkitdxozPxt1YZ9O1097EJiVpwHr9FoAuEVURCKV0Av8NBERovJauzP7bo1ThvuhZ4shsQ1AJiu4vQpoT1AQ=="
  "resolved" "https://registry.npmjs.org/react-dev-utils/-/react-dev-utils-12.0.0.tgz"
  "version" "12.0.0"
  dependencies:
    "@babel/code-frame" "^7.16.0"
    "address" "^1.1.2"
    "browserslist" "^4.18.1"
    "chalk" "^4.1.2"
    "cross-spawn" "^7.0.3"
    "detect-port-alt" "^1.1.6"
    "escape-string-regexp" "^4.0.0"
    "filesize" "^8.0.6"
    "find-up" "^5.0.0"
    "fork-ts-checker-webpack-plugin" "^6.5.0"
    "global-modules" "^2.0.0"
    "globby" "^11.0.4"
    "gzip-size" "^6.0.0"
    "immer" "^9.0.7"
    "is-root" "^2.1.0"
    "loader-utils" "^3.2.0"
    "open" "^8.4.0"
    "pkg-up" "^3.1.0"
    "prompts" "^2.4.2"
    "react-error-overlay" "^6.0.10"
    "recursive-readdir" "^2.2.2"
    "shell-quote" "^1.7.3"
    "strip-ansi" "^6.0.1"
    "text-table" "^0.2.0"

"react-dom@*", "react-dom@^17.0.0 || ^18.0.0", "react-dom@^17.0.2", "react-dom@>=16.14.0", "react-dom@>=16.6.0", "react-dom@>=16.8", "react-dom@>=17.0.1":
  "integrity" "sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA=="
  "resolved" "https://registry.npmjs.org/react-dom/-/react-dom-17.0.2.tgz"
  "version" "17.0.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "scheduler" "^0.20.2"

"react-error-overlay@^6.0.10":
  "integrity" "sha512-mKR90fX7Pm5seCOfz8q9F+66VCc1PGsWSBxKbITjfKVQHMNF2zudxHnMdJiB1fRCb+XsbQV9sO9DCkgsMQgBIA=="
  "resolved" "https://registry.npmjs.org/react-error-overlay/-/react-error-overlay-6.0.10.tgz"
  "version" "6.0.10"

"react-is@^16.13.1":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^16.3.2":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^16.7.0":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^17.0.1", "react-is@^17.0.2":
  "integrity" "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  "version" "17.0.2"

"react-lifecycles-compat@^3.0.4":
  "integrity" "sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA=="
  "resolved" "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz"
  "version" "3.0.4"

"react-native-vector-icons@^9.0.0":
  "integrity" "sha512-qKX5d5/TafHmI4B7UOSZCL3BAGh7ZfF30iLwRgxBkKfZl2lKSuHp8Ottj9OyWh9b5fFtlg+LtyvZrns3g2kh+w=="
  "resolved" "https://registry.npmjs.org/react-native-vector-icons/-/react-native-vector-icons-9.0.0.tgz"
  "version" "9.0.0"
  dependencies:
    "lodash.frompairs" "^4.0.1"
    "lodash.isequal" "^4.5.0"
    "lodash.isstring" "^4.0.1"
    "lodash.omit" "^4.5.0"
    "lodash.pick" "^4.4.0"
    "lodash.template" "^4.5.0"
    "prop-types" "^15.7.2"
    "yargs" "^16.1.1"

"react-native-web@^0.17.5":
  "integrity" "sha512-pdieIZi2/YeVTaOuIaeaLC6FMkk8h3pvPNruedH+6cWiE15Oz6BGjJ+5IafdXEyiipb1Y0+QuecW81fa3DVM4g=="
  "resolved" "https://registry.npmjs.org/react-native-web/-/react-native-web-0.17.5.tgz"
  "version" "0.17.5"
  dependencies:
    "array-find-index" "^1.0.2"
    "create-react-class" "^15.7.0"
    "fbjs" "^3.0.0"
    "hyphenate-style-name" "^1.0.4"
    "inline-style-prefixer" "^6.0.0"
    "normalize-css-color" "^1.0.2"
    "prop-types" "^15.6.0"

"react-redux@^7.2.1 || ^8.0.0-beta", "react-redux@^7.2.6":
  "integrity" "sha512-10RPdsz0UUrRL1NZE0ejTkucnclYSgXp5q+tB5SWx2qeG2ZJQJyymgAhwKy73yiL/13btfB6fPr+rgbMAaZIAQ=="
  "resolved" "https://registry.npmjs.org/react-redux/-/react-redux-7.2.6.tgz"
  "version" "7.2.6"
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/react-redux" "^7.1.20"
    "hoist-non-react-statics" "^3.3.2"
    "loose-envify" "^1.4.0"
    "prop-types" "^15.7.2"
    "react-is" "^17.0.2"

"react-refresh@^0.11.0", "react-refresh@>=0.10.0 <1.0.0":
  "integrity" "sha512-F27qZr8uUqwhWZboondsPx8tnC3Ct3SxZA3V5WyEvujRyyNv0VYPhoBg1gZ8/MV5tubQp76Trw8lTv9hzRBa+A=="
  "resolved" "https://registry.npmjs.org/react-refresh/-/react-refresh-0.11.0.tgz"
  "version" "0.11.0"

"react-router-dom@^6.2.1":
  "integrity" "sha512-I6Zax+/TH/cZMDpj3/4Fl2eaNdcvoxxHoH1tYOREsQ22OKDYofGebrNm6CTPUcvLvZm63NL/vzCYdjf9CUhqmA=="
  "resolved" "https://registry.npmjs.org/react-router-dom/-/react-router-dom-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "history" "^5.2.0"
    "react-router" "6.2.1"

"react-router@6.2.1":
  "integrity" "sha512-2fG0udBtxou9lXtK97eJeET2ki5//UWfQSl1rlJ7quwe6jrktK9FCCc8dQb5QY6jAv3jua8bBQRhhDOM/kVRsg=="
  "resolved" "https://registry.npmjs.org/react-router/-/react-router-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "history" "^5.2.0"

"react-scripts@>=2.1.3", "react-scripts@5.0.0":
  "integrity" "sha512-3i0L2CyIlROz7mxETEdfif6Sfhh9Lfpzi10CtcGs1emDQStmZfWjJbAIMtRD0opVUjQuFWqHZyRZ9PPzKCFxWg=="
  "resolved" "https://registry.npmjs.org/react-scripts/-/react-scripts-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@babel/core" "^7.16.0"
    "@pmmmwh/react-refresh-webpack-plugin" "^0.5.3"
    "@svgr/webpack" "^5.5.0"
    "babel-jest" "^27.4.2"
    "babel-loader" "^8.2.3"
    "babel-plugin-named-asset-import" "^0.3.8"
    "babel-preset-react-app" "^10.0.1"
    "bfj" "^7.0.2"
    "browserslist" "^4.18.1"
    "camelcase" "^6.2.1"
    "case-sensitive-paths-webpack-plugin" "^2.4.0"
    "css-loader" "^6.5.1"
    "css-minimizer-webpack-plugin" "^3.2.0"
    "dotenv" "^10.0.0"
    "dotenv-expand" "^5.1.0"
    "eslint" "^8.3.0"
    "eslint-config-react-app" "^7.0.0"
    "eslint-webpack-plugin" "^3.1.1"
    "file-loader" "^6.2.0"
    "fs-extra" "^10.0.0"
    "html-webpack-plugin" "^5.5.0"
    "identity-obj-proxy" "^3.0.0"
    "jest" "^27.4.3"
    "jest-resolve" "^27.4.2"
    "jest-watch-typeahead" "^1.0.0"
    "mini-css-extract-plugin" "^2.4.5"
    "postcss" "^8.4.4"
    "postcss-flexbugs-fixes" "^5.0.2"
    "postcss-loader" "^6.2.1"
    "postcss-normalize" "^10.0.1"
    "postcss-preset-env" "^7.0.1"
    "prompts" "^2.4.2"
    "react-app-polyfill" "^3.0.0"
    "react-dev-utils" "^12.0.0"
    "react-refresh" "^0.11.0"
    "resolve" "^1.20.0"
    "resolve-url-loader" "^4.0.0"
    "sass-loader" "^12.3.0"
    "semver" "^7.3.5"
    "source-map-loader" "^3.0.0"
    "style-loader" "^3.3.1"
    "tailwindcss" "^3.0.2"
    "terser-webpack-plugin" "^5.2.5"
    "webpack" "^5.64.4"
    "webpack-dev-server" "^4.6.0"
    "webpack-manifest-plugin" "^4.0.2"
    "workbox-webpack-plugin" "^6.4.1"
  optionalDependencies:
    "fsevents" "^2.3.2"

"react-transition-group@^4.4.1", "react-transition-group@^4.4.2":
  "integrity" "sha512-/RNYfRAMlZwDSr6z4zNKV6xu53/e2BuaBbGhbyYIXTrmgu/bGHzmqOs7mJSJBHy9Ud+ApHx3QjrkKSp1pxvlFg=="
  "resolved" "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.2.tgz"
  "version" "4.4.2"
  dependencies:
    "@babel/runtime" "^7.5.5"
    "dom-helpers" "^5.0.1"
    "loose-envify" "^1.4.0"
    "prop-types" "^15.6.2"

"react@*", "react@^16.8.0 || ^17.0.0-rc.1", "react@^16.8.3 || ^17", "react@^16.9.0 || ^17.0.0 || 18.0.0-beta", "react@^17.0.0 || ^18.0.0", "react@^17.0.2", "react@>= 16", "react@>=0.14.0", "react@>=15.0.0", "react@>=16.14.0", "react@>=16.6.0", "react@>=16.8", "react@>=16.8.0", "react@>=17.0.1", "react@17.0.2":
  "integrity" "sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA=="
  "resolved" "https://registry.npmjs.org/react/-/react-17.0.2.tgz"
  "version" "17.0.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"readable-stream@^2.0.1":
  "integrity" "sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@~2.3.6":
  "integrity" "sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"recursive-readdir@^2.2.2":
  "integrity" "sha512-nRCcW9Sj7NuZwa2XvH9co8NPeXUBhZP7CRKJtU+cS6PW9FpCIFoI5ib0NT1ZrbNuPoRy0ylyCaUL8Gih4LSyFg=="
  "resolved" "https://registry.npmjs.org/recursive-readdir/-/recursive-readdir-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "minimatch" "3.0.4"

"redent@^3.0.0":
  "integrity" "sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg=="
  "resolved" "https://registry.npmjs.org/redent/-/redent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "indent-string" "^4.0.0"
    "strip-indent" "^3.0.0"

"redux-thunk@^2.4.1":
  "integrity" "sha512-OOYGNY5Jy2TWvTL1KgAlVy6dcx3siPJ1wTq741EPyUKfn6W6nChdICjZwCd0p8AZBs5kWpZlbkXW2nE/zjUa+Q=="
  "resolved" "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.4.1.tgz"
  "version" "2.4.1"

"redux@^4", "redux@^4.0.0", "redux@^4.1.2":
  "integrity" "sha512-SH8PglcebESbd/shgf6mii6EIoRM0zrQyjcuQ+ojmfxjTtE0z9Y8pa62iA/OJ58qjP6j27uyW4kUF4jl/jd6sw=="
  "resolved" "https://registry.npmjs.org/redux/-/redux-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "@babel/runtime" "^7.9.2"

"regenerate-unicode-properties@^9.0.0":
  "integrity" "sha512-3E12UeNSPfjrgwjkR81m5J7Aw/T55Tu7nUyZVQYCKEOs+2dkxEY+DpPtZzO4YruuiPb7NkYLVcyJC4+zCbk5pA=="
  "resolved" "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-9.0.0.tgz"
  "version" "9.0.0"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.13.4", "regenerator-runtime@^0.13.9":
  "integrity" "sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  "version" "0.13.9"

"regenerator-transform@^0.14.2":
  "integrity" "sha512-eOf6vka5IO151Jfsw2NO9WpGX58W6wWmefK3I1zEGr0lOD0u8rwPaNqQL1aRxUaxLeKO3ArNh3VYg1KbaD+FFw=="
  "resolved" "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-parser@^2.2.11":
  "integrity" "sha512-jbD/FT0+9MBU2XAZluI7w2OBs1RBi6p9M83nkoZayQXXU9e8Robt69FcZc7wU4eJD/YFTjn1JdCk3rbMJajz8Q=="
  "resolved" "https://registry.npmjs.org/regex-parser/-/regex-parser-2.2.11.tgz"
  "version" "2.2.11"

"regexp.prototype.flags@^1.2.0", "regexp.prototype.flags@^1.3.1":
  "integrity" "sha512-pMR7hBVUUGI7PMA37m2ofIdQCsomVnas+Jn5UPGAHQ+/LlwKm/aTLJHdasmHRzlfeZwHiAOaRSo2rbBDm3nNUQ=="
  "resolved" "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"regexpp@^3.2.0":
  "integrity" "sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg=="
  "resolved" "https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^4.7.1":
  "integrity" "sha512-1F6bYsoYiz6is+oz70NWur2Vlh9KWtswuRuzJOfeYUrfPX2o8n74AnUVaOGDbUqVGO9fNHu48/pjJO4sNVwsOg=="
  "resolved" "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.8.0.tgz"
  "version" "4.8.0"
  dependencies:
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^9.0.0"
    "regjsgen" "^0.5.2"
    "regjsparser" "^0.7.0"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.0.0"

"regjsgen@^0.5.2":
  "integrity" "sha512-OFFT3MfrH90xIW8OOSyUrk6QHD5E9JOTeGodiJeBS3J6IwlgzJMNE/1bZklWz5oTg+9dCMyEetclvCVXOPoN3A=="
  "resolved" "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.2.tgz"
  "version" "0.5.2"

"regjsparser@^0.7.0":
  "integrity" "sha512-A4pcaORqmNMDVwUjWoTzuhwMGpP+NykpfqAsEgI1FSH/EzC7lrN5TMd+kN8YCovX+jMpu8eaqXgXPCa0g8FQNQ=="
  "resolved" "https://registry.npmjs.org/regjsparser/-/regjsparser-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@^0.2.7":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"renderkid@^3.0.0":
  "integrity" "sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg=="
  "resolved" "https://registry.npmjs.org/renderkid/-/renderkid-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "css-select" "^4.1.3"
    "dom-converter" "^0.2.0"
    "htmlparser2" "^6.1.0"
    "lodash" "^4.17.21"
    "strip-ansi" "^6.0.1"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"reselect@^4.1.5":
  "integrity" "sha512-uVdlz8J7OO+ASpBYoz1Zypgx0KasCY20H+N8JD13oUMtPvSHQuscrHop4KbXrbsBcdB9Ds7lVK7eRkBIfO43vQ=="
  "resolved" "https://registry.npmjs.org/reselect/-/reselect-4.1.5.tgz"
  "version" "4.1.5"

"resolve-cwd@^3.0.0":
  "integrity" "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg=="
  "resolved" "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "resolve-from" "^5.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0":
  "integrity" "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-url-loader@^4.0.0":
  "integrity" "sha512-05VEMczVREcbtT7Bz+C+96eUO5HDNvdthIiMB34t7FcF8ehcu4wC0sSgPUubs3XW2Q3CNLJk/BJrCU9wVRymiA=="
  "resolved" "https://registry.npmjs.org/resolve-url-loader/-/resolve-url-loader-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "adjust-sourcemap-loader" "^4.0.0"
    "convert-source-map" "^1.7.0"
    "loader-utils" "^2.0.0"
    "postcss" "^7.0.35"
    "source-map" "0.6.1"

"resolve.exports@^1.1.0":
  "integrity" "sha512-J1l+Zxxp4XK3LUDZ9m60LRJF/mAe4z6a4xyabPHk7pvK5t35dACV32iIjJDFeWZFfZlO29w6SZ67knR0tHzJtQ=="
  "resolved" "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.1.0.tgz"
  "version" "1.1.0"

"resolve@^1.12.0", "resolve@^1.14.2", "resolve@^1.19.0", "resolve@^1.20.0", "resolve@^1.21.0":
  "integrity" "sha512-3wCbTpk5WJlyE4mSOtDLhqQmGFi0/TD9VPwmiolnk8U0wRgMEktqCXd3vy5buTO3tljvalNvKrjHEfrd2WpEKA=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.21.0.tgz"
  "version" "1.21.0"
  dependencies:
    "is-core-module" "^2.8.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"resolve@^2.0.0-next.3":
  "integrity" "sha512-W8LucSynKUIDu9ylraa7ueVZ7hc0uAgJBxVsQSKOXOyle8a93qXhcz+XAXZ8bIq2d6i4Ehddn6Evt+0/UwKk6Q=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.3.tgz"
  "version" "2.0.0-next.3"
  dependencies:
    "is-core-module" "^2.2.0"
    "path-parse" "^1.0.6"

"retry@^0.13.1":
  "integrity" "sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg=="
  "resolved" "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz"
  "version" "0.13.1"

"reusify@^1.0.4":
  "integrity" "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="
  "resolved" "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rimraf@^3.0.0", "rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rollup-plugin-terser@^7.0.0":
  "integrity" "sha512-w3iIaU4OxcF52UUXiZNsNeuXIMDvFrr+ZXK6bFZ0Q60qyVfq4uLptoS4bbq3paG3x216eQllFZX7zt6TIImguQ=="
  "resolved" "https://registry.npmjs.org/rollup-plugin-terser/-/rollup-plugin-terser-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "jest-worker" "^26.2.1"
    "serialize-javascript" "^4.0.0"
    "terser" "^5.0.0"

"rollup@^1.20.0 || ^2.0.0", "rollup@^1.20.0||^2.0.0", "rollup@^2.0.0", "rollup@^2.43.1":
  "integrity" "sha512-+c+lbw1lexBKSMb1yxGDVfJ+vchJH3qLbmavR+awDinTDA2C5Ug9u7lkOzj62SCu0PKUExsW36tpgW7Fmpn3yQ=="
  "resolved" "https://registry.npmjs.org/rollup/-/rollup-2.64.0.tgz"
  "version" "2.64.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@>=5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@~5.2.0":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@5.2.1":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sanitize.css@*":
  "integrity" "sha512-ZRwKbh/eQ6w9vmTjkuG0Ioi3HBwPFce0O+v//ve+aOq1oeCy7jMV2qzzAlpsNuqpqCBjjriM1lbtZbF/Q8jVyA=="
  "resolved" "https://registry.npmjs.org/sanitize.css/-/sanitize.css-13.0.0.tgz"
  "version" "13.0.0"

"sass-loader@^12.3.0":
  "integrity" "sha512-7xN+8khDIzym1oL9XyS6zP6Ges+Bo2B2xbPrjdMHEYyV3AQYhd/wXeru++3ODHF0zMjYmVadblSKrPrjEkL8mg=="
  "resolved" "https://registry.npmjs.org/sass-loader/-/sass-loader-12.4.0.tgz"
  "version" "12.4.0"
  dependencies:
    "klona" "^2.0.4"
    "neo-async" "^2.6.2"

"sax@~1.2.4":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"saxes@^5.0.1":
  "integrity" "sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw=="
  "resolved" "https://registry.npmjs.org/saxes/-/saxes-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "xmlchars" "^2.2.0"

"scheduler@^0.20.2":
  "integrity" "sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz"
  "version" "0.20.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"schema-utils@^2.6.5":
  "integrity" "sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.0.0", "schema-utils@^3.1.0", "schema-utils@^3.1.1":
  "integrity" "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^4.0.0":
  "integrity" "sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.8.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.0.0"

"schema-utils@2.7.0":
  "integrity" "sha512-0ilKFI6QQF5nxDZLFn2dMjvc4hjg/Wkg7rHd3jK6/A4a1Hl9VFdQWvgB1UMGoU94pad1P/8N7fMcEnLnSiju8A=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "@types/json-schema" "^7.0.4"
    "ajv" "^6.12.2"
    "ajv-keywords" "^3.4.1"

"select-hose@^2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selenium-webdriver@^4.0.0-beta.2", "selenium-webdriver@4.0.0-rc-1":
  "integrity" "sha512-bcrwFPRax8fifRP60p7xkWDGSJJoMkPAzufMlk5K2NyLPht/YZzR2WcIk1+3gR8VOCLlst1P2PI+MXACaFzpIw=="
  "resolved" "https://registry.npmjs.org/selenium-webdriver/-/selenium-webdriver-4.0.0-rc-1.tgz"
  "version" "4.0.0-rc-1"
  dependencies:
    "jszip" "^3.6.0"
    "rimraf" "^3.0.2"
    "tmp" "^0.2.1"
    "ws" ">=7.4.6"

"selfsigned@^2.0.0":
  "integrity" "sha512-cUdFiCbKoa1mZ6osuJs2uDHrs0k0oprsKveFiiaBKCNq3SYyb5gs2HxhQyDNLCmL51ZZThqi4YNDpCK6GOP1iQ=="
  "resolved" "https://registry.npmjs.org/selfsigned/-/selfsigned-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "node-forge" "^1.2.0"

"semver@^5.6.0":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.1":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.2":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.3.0":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^7.3.2", "semver@^7.3.5":
  "integrity" "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@7.0.0":
  "integrity" "sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.0.0.tgz"
  "version" "7.0.0"

"send@0.17.2":
  "integrity" "sha512-UJYB6wFSJE3G00nEivR5rgWp8c2xXvJ3OPWPhmuteU0IKj8nKbG3DrjiOmLwpnHGYWAVwA69zmTm++YG0Hmwww=="
  "resolved" "https://registry.npmjs.org/send/-/send-0.17.2.tgz"
  "version" "0.17.2"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "1.8.1"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.1"
    "statuses" "~1.5.0"

"serialize-javascript@^4.0.0":
  "integrity" "sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw=="
  "resolved" "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "randombytes" "^2.1.0"

"serialize-javascript@^6.0.0":
  "integrity" "sha512-Qr3TosvguFt8ePWqsvRfrKyQXIiW+nGbYpy8XK24NQHE83caxWt+mIymTT19DGFbNWNLfEwsrkSmN64lVWB9ag=="
  "resolved" "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@^1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.14.2":
  "integrity" "sha512-+TMNA9AFxUEGuC0z2mevogSnn9MXKb4fa7ngeRMJaaGv8vTwnIEkKi+QGvPt33HSnf8pRS+WGM0EbMtCJLKMBQ=="
  "resolved" "https://registry.npmjs.org/serve-static/-/serve-static-1.14.2.tgz"
  "version" "1.14.2"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.17.2"

"set-immediate-shim@~1.0.1":
  "integrity" "sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E="
  "resolved" "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz"
  "version" "1.0.1"

"setimmediate@^1.0.5":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.2.0":
  "integrity" "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.7.3":
  "integrity" "sha512-Vpfqwm4EnqGdlsBFNmHhxhElJYrdfcxPThu+ryKS5J8L/fhAwLazFZtq+S+TWZ9ANj2piSQLGj6NQg+lKPmxrw=="
  "resolved" "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.3.tgz"
  "version" "1.7.3"

"side-channel@^1.0.4":
  "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  "resolved" "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.2", "signal-exit@^3.0.3":
  "integrity" "sha512-sDl4qMFpijcGw22U5w63KmD3cZJfBuFlVNbVMKje2keoKML7X2UzWbc4XrmEbDwg0NXJc3yv4/ox7b+JWb57kQ=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.6.tgz"
  "version" "3.0.6"

"sisteransi@^1.0.5":
  "integrity" "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg=="
  "resolved" "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  "version" "1.0.5"

"slash@^3.0.0":
  "integrity" "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q=="
  "resolved" "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  "version" "3.0.0"

"slash@^4.0.0":
  "integrity" "sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew=="
  "resolved" "https://registry.npmjs.org/slash/-/slash-4.0.0.tgz"
  "version" "4.0.0"

"sockjs@^0.3.21":
  "integrity" "sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ=="
  "resolved" "https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz"
  "version" "0.3.24"
  dependencies:
    "faye-websocket" "^0.11.3"
    "uuid" "^8.3.2"
    "websocket-driver" "^0.7.4"

"source-list-map@^2.0.0", "source-list-map@^2.0.1":
  "integrity" "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw=="
  "resolved" "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-js@^1.0.1":
  "integrity" "sha512-4+TN2b3tqOCd/kaGRJ/sTYA0tR0mdXx26ipdolxcwtJVqEnqNYvlCAt1q3ypy4QMlYus+Zh34RNtYLoq2oQ4IA=="
  "resolved" "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.1.tgz"
  "version" "1.0.1"

"source-map-loader@^3.0.0":
  "integrity" "sha512-Vp1UsfyPvgujKQzi4pyDiTOnE3E4H+yHvkVRN3c/9PJmQS4CQJExvcDvaX/D+RV+xQben9HJ56jMJS3CgUeWyA=="
  "resolved" "https://registry.npmjs.org/source-map-loader/-/source-map-loader-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "abab" "^2.0.5"
    "iconv-lite" "^0.6.3"
    "source-map-js" "^1.0.1"

"source-map-resolve@^0.6.0":
  "integrity" "sha512-KXBr9d/fO/bWo97NXsPIAW1bFSBOuCnjbNTBMO7N59hsv5i9yzRDfcYwwt0l04+VqnKC+EwzvJZIP/qkuMgR/w=="
  "resolved" "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"

"source-map-support@^0.5.6", "source-map-support@~0.5.20":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw=="
  "resolved" "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.0", "source-map@^0.5.7":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.6.1", "source-map@0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz"
  "version" "0.7.3"

"source-map@^0.8.0-beta.0":
  "integrity" "sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.8.0-beta.0.tgz"
  "version" "0.8.0-beta.0"
  dependencies:
    "whatwg-url" "^7.0.0"

"source-map@~0.6.0":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.7.2":
  "integrity" "sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz"
  "version" "0.7.3"

"sourcemap-codec@^1.4.4":
  "integrity" "sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA=="
  "resolved" "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  "version" "1.4.8"

"spdy-transport@^3.0.0":
  "integrity" "sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw=="
  "resolved" "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA=="
  "resolved" "https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"stable@^0.1.8":
  "integrity" "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w=="
  "resolved" "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz"
  "version" "0.1.8"

"stack-utils@^2.0.3":
  "integrity" "sha512-xrQcmYhOsn/1kX+Vraq+7j4oE2j/6BFscZ0etmYg81xuM8Gq0022Pxb8+IqgOFUIaxHs0KaSb7T1+OegiNrNFA=="
  "resolved" "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "escape-string-regexp" "^2.0.0"

"stackframe@^1.1.1":
  "integrity" "sha512-GrdeshiRmS1YLMYgzF16olf2jJ/IzxXY9lhKOskuVziubpTYcYqyOwYeJKzQkwy7uN0fYSsbsC4RQaXf9LCrYA=="
  "resolved" "https://registry.npmjs.org/stackframe/-/stackframe-1.2.0.tgz"
  "version" "1.2.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  "version" "1.5.0"

"string_decoder@^1.1.1":
  "integrity" "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-length@^4.0.1":
  "integrity" "sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ=="
  "resolved" "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "char-regex" "^1.0.2"
    "strip-ansi" "^6.0.0"

"string-length@^5.0.1":
  "integrity" "sha512-9Ep08KAMUn0OadnVaBuRdE2l615CQ508kr0XMadjClfYpdCyvrbFp6Taebo8yyxokQ4viUd/xPPUA4FGgUa0ow=="
  "resolved" "https://registry.npmjs.org/string-length/-/string-length-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "char-regex" "^2.0.0"
    "strip-ansi" "^7.0.1"

"string-natural-compare@^3.0.1":
  "integrity" "sha512-n3sPwynL1nwKi3WJ6AIsClwBMa0zTi54fn2oLU6ndfTSIO05xaznjSf15PcBZU6FNWbmN5Q6cxT4V5hGvB4taw=="
  "resolved" "https://registry.npmjs.org/string-natural-compare/-/string-natural-compare-3.0.1.tgz"
  "version" "3.0.1"

"string-width@^4.1.0", "string-width@^4.2.0":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string.prototype.matchall@^4.0.6":
  "integrity" "sha512-6WgDX8HmQqvEd7J+G6VtAahhsQIssiZ8zl7zKh1VDMFyL3hRTJP4FTNA3RbIp2TOQ9AYNDcc7e3fH0Qbup+DBg=="
  "resolved" "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.6.tgz"
  "version" "4.0.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"
    "get-intrinsic" "^1.1.1"
    "has-symbols" "^1.0.2"
    "internal-slot" "^1.0.3"
    "regexp.prototype.flags" "^1.3.1"
    "side-channel" "^1.0.4"

"string.prototype.trimend@^1.0.4":
  "integrity" "sha512-y9xCjw1P23Awk8EvTpcyL2NIr1j7wJ39f+k6lvRnSMz+mz9CGz9NYPelDk42kOz6+ql8xjfK8oYzy3jAP5QU5A=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"string.prototype.trimstart@^1.0.4":
  "integrity" "sha512-jh6e984OBfvxS50tdY2nRZnoC5/mLFKOREQfw8t5yytkoUsJRNxvI/E39qu1sD0OtWI3OC0XgKSmcWwziwYuZw=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"stringify-object@^3.3.0":
  "integrity" "sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw=="
  "resolved" "https://registry.npmjs.org/stringify-object/-/stringify-object-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "get-own-enumerable-property-symbols" "^3.0.0"
    "is-obj" "^1.0.1"
    "is-regexp" "^1.0.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^7.0.0":
  "integrity" "sha512-cXNxvT8dFNRVfhVME3JAe98mkXDYN2O1l7jmcwMnOslDeESg1rF/OZMtK0nRAhiari1unG5cD4jG3rapUAkLbw=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "ansi-regex" "^6.0.1"

"strip-ansi@^7.0.1":
  "integrity" "sha512-cXNxvT8dFNRVfhVME3JAe98mkXDYN2O1l7jmcwMnOslDeESg1rF/OZMtK0nRAhiari1unG5cD4jG3rapUAkLbw=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "ansi-regex" "^6.0.1"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-bom@^4.0.0":
  "integrity" "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w=="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz"
  "version" "4.0.0"

"strip-comments@^2.0.1":
  "integrity" "sha512-ZprKx+bBLXv067WTCALv8SSz5l2+XhpYCsVtSqlMnkAXMWDq+/ekVbl1ghqP9rUHTzv6sm/DwCOiYutU/yp1fw=="
  "resolved" "https://registry.npmjs.org/strip-comments/-/strip-comments-2.0.1.tgz"
  "version" "2.0.1"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^3.0.0":
  "integrity" "sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ=="
  "resolved" "https://registry.npmjs.org/strip-indent/-/strip-indent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "min-indent" "^1.0.0"

"strip-json-comments@^3.1.0", "strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"style-loader@^3.3.1":
  "integrity" "sha512-GPcQ+LDJbrcxHORTRes6Jy2sfvK2kS6hpSfI/fXhPt+spVzxF6LJ1dHLN9zIGmVaaP044YKaIatFaufENRiDoQ=="
  "resolved" "https://registry.npmjs.org/style-loader/-/style-loader-3.3.1.tgz"
  "version" "3.3.1"

"stylehacks@^5.0.1":
  "integrity" "sha512-Es0rVnHIqbWzveU1b24kbw92HsebBepxfcqe5iix7t9j0PQqhs0IxXVXv0pY2Bxa08CgMkzD6OWql7kbGOuEdA=="
  "resolved" "https://registry.npmjs.org/stylehacks/-/stylehacks-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "browserslist" "^4.16.0"
    "postcss-selector-parser" "^6.0.4"

"stylis@4.0.13":
  "integrity" "sha512-xGPXiFVl4YED9Jh7Euv2V220mriG9u4B2TA6Ybjc1catrstKD2PpIdU3U0RKpkVBC2EhmL/F0sPCr9vrFTNRag=="
  "resolved" "https://registry.npmjs.org/stylis/-/stylis-4.0.13.tgz"
  "version" "4.0.13"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.0.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.0.0":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"supports-hyperlinks@^2.0.0":
  "integrity" "sha512-6sXEzV5+I5j8Bmq9/vUphGRM/RJNT9SCURJLjwfOg51heRtguGWDzcaBlgAzKhQa0EVNpPEKzQuBwZ8S8WaCeQ=="
  "resolved" "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "has-flag" "^4.0.0"
    "supports-color" "^7.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-parser@^2.0.2":
  "integrity" "sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ=="
  "resolved" "https://registry.npmjs.org/svg-parser/-/svg-parser-2.0.4.tgz"
  "version" "2.0.4"

"svgo@^1.2.2":
  "integrity" "sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw=="
  "resolved" "https://registry.npmjs.org/svgo/-/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"svgo@^2.7.0":
  "integrity" "sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg=="
  "resolved" "https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "@trysound/sax" "0.2.0"
    "commander" "^7.2.0"
    "css-select" "^4.1.3"
    "css-tree" "^1.1.3"
    "csso" "^4.2.0"
    "picocolors" "^1.0.0"
    "stable" "^0.1.8"

"symbol-tree@^3.2.4":
  "integrity" "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw=="
  "resolved" "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"tailwindcss@^3.0.2":
  "integrity" "sha512-bT2iy7FtjwgsXik4ZoJnHXR+SRCiGR1W95fVqpLZebr64m4ahwUwRbIAc5w5+2fzr1YF4Ct2eI7dojMRRl8sVQ=="
  "resolved" "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.0.15.tgz"
  "version" "3.0.15"
  dependencies:
    "arg" "^5.0.1"
    "chalk" "^4.1.2"
    "chokidar" "^3.5.2"
    "color-name" "^1.1.4"
    "cosmiconfig" "^7.0.1"
    "detective" "^5.2.0"
    "didyoumean" "^1.2.2"
    "dlv" "^1.1.3"
    "fast-glob" "^3.2.7"
    "glob-parent" "^6.0.2"
    "is-glob" "^4.0.3"
    "normalize-path" "^3.0.0"
    "object-hash" "^2.2.0"
    "postcss-js" "^4.0.0"
    "postcss-load-config" "^3.1.0"
    "postcss-nested" "5.0.6"
    "postcss-selector-parser" "^6.0.8"
    "postcss-value-parser" "^4.2.0"
    "quick-lru" "^5.1.1"
    "resolve" "^1.21.0"

"tapable@^1.0.0":
  "integrity" "sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz"
  "version" "1.1.3"

"tapable@^2.0.0", "tapable@^2.1.1", "tapable@^2.2.0":
  "integrity" "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
  "version" "2.2.1"

"temp-dir@^2.0.0":
  "integrity" "sha512-aoBAniQmmwtcKp/7BzsH8Cxzv8OL736p7v1ihGb5e9DJ9kTwGWHrQrVB5+lfVDzfGrdRzXch+ig7LHaY1JTOrg=="
  "resolved" "https://registry.npmjs.org/temp-dir/-/temp-dir-2.0.0.tgz"
  "version" "2.0.0"

"tempy@^0.6.0":
  "integrity" "sha512-G13vtMYPT/J8A4X2SjdtBTphZlrp1gKv6hZiOjw14RCWg6GbHuQBGtjlx75xLbYV/wEc0D7G5K4rxKP/cXk8Bw=="
  "resolved" "https://registry.npmjs.org/tempy/-/tempy-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "is-stream" "^2.0.0"
    "temp-dir" "^2.0.0"
    "type-fest" "^0.16.0"
    "unique-string" "^2.0.0"

"terminal-link@^2.0.0":
  "integrity" "sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ=="
  "resolved" "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "supports-hyperlinks" "^2.0.0"

"terser-webpack-plugin@^5.1.3", "terser-webpack-plugin@^5.2.5":
  "integrity" "sha512-LPIisi3Ol4chwAaPP8toUJ3L4qCM1G0wao7L3qNv57Drezxj6+VEyySpPw4B1HSO2Eg/hDY/MNF5XihCAoqnsQ=="
  "resolved" "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "jest-worker" "^27.4.1"
    "schema-utils" "^3.1.1"
    "serialize-javascript" "^6.0.0"
    "source-map" "^0.6.1"
    "terser" "^5.7.2"

"terser@^5.0.0", "terser@^5.10.0", "terser@^5.7.2":
  "integrity" "sha512-AMmF99DMfEDiRJfxfY5jj5wNH/bYO09cniSqhfoyxc8sFoYIgkJy86G04UoZU5VjlpnplVu0K6Tx6E9b5+DlHA=="
  "resolved" "https://registry.npmjs.org/terser/-/terser-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "commander" "^2.20.0"
    "source-map" "~0.7.2"
    "source-map-support" "~0.5.20"

"test-exclude@^6.0.0":
  "integrity" "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w=="
  "resolved" "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    "glob" "^7.1.4"
    "minimatch" "^3.0.4"

"text-table@^0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"throat@^6.0.1":
  "integrity" "sha512-8hmiGIJMDlwjg7dlJ4yKGLK8EsYqKgPWbG3b4wjJddKNwc7N7Dpn08Df4szr/sZdMVeOstrdYSsqzX6BYbcB+w=="
  "resolved" "https://registry.npmjs.org/throat/-/throat-6.0.1.tgz"
  "version" "6.0.1"

"thunky@^1.0.2":
  "integrity" "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA=="
  "resolved" "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz"
  "version" "1.1.0"

"timsort@^0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://registry.npmjs.org/timsort/-/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tmp@^0.2.1":
  "integrity" "sha512-76SUhtfqR2Ijn+xllcI5P1oyannHNHByD80W1q447gU3mp9G9PSpGdWmjUOHRDPiHYacIk66W7ubDTuPF3BEtQ=="
  "resolved" "https://registry.npmjs.org/tmp/-/tmp-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "rimraf" "^3.0.0"

"tmpl@1.0.5":
  "integrity" "sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw=="
  "resolved" "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz"
  "version" "1.0.5"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"toidentifier@1.0.1":
  "integrity" "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="
  "resolved" "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"tough-cookie@^4.0.0":
  "integrity" "sha512-tHdtEpQCMrc1YLrMaqXXcj6AxhYi/xgit6mZu1+EDWUn+qhUf8wMQoFIy9NXuq23zAwtcB0t/MjACGR18pcRbg=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "psl" "^1.1.33"
    "punycode" "^2.1.1"
    "universalify" "^0.1.2"

"tr46@^1.0.1":
  "integrity" "sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "punycode" "^2.1.0"

"tr46@^2.1.0":
  "integrity" "sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw=="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "punycode" "^2.1.1"

"tr46@~0.0.3":
  "integrity" "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  "version" "0.0.3"

"tryer@^1.0.1":
  "integrity" "sha512-c3zayb8/kWWpycWYg87P71E1S1ZL6b6IJxfb5fvsUgsf0S2MVGaDhDXXjDMpdCpfWXqptc+4mXwmiy1ypXqRAA=="
  "resolved" "https://registry.npmjs.org/tryer/-/tryer-1.0.1.tgz"
  "version" "1.0.1"

"tsconfig-paths@^3.12.0":
  "integrity" "sha512-e5adrnOYT6zqVnWqZu7i/BQ3BnhzvGbjEjejFXO20lKIKpwTaupkCPgEfv4GZK1IBciJUEhYs3J3p75FdaTFVg=="
  "resolved" "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "@types/json5" "^0.0.29"
    "json5" "^1.0.1"
    "minimist" "^1.2.0"
    "strip-bom" "^3.0.0"

"tslib@^1.8.1":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^2.0.3", "tslib@^2.1.0":
  "integrity" "sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.3.1.tgz"
  "version" "2.3.1"

"tsutils@^3.21.0":
  "integrity" "sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA=="
  "resolved" "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz"
  "version" "3.21.0"
  dependencies:
    "tslib" "^1.8.1"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-detect@4.0.8":
  "integrity" "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g=="
  "resolved" "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz"
  "version" "4.0.8"

"type-fest@^0.16.0":
  "integrity" "sha512-eaBzG6MxNzEn9kiwvtre90cXaNLkmadMWa1zQMs3XORCXNbsH/OewwbxC5ia9dCxIxnTAsSxXJaa/p5y8DlvJg=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.16.0.tgz"
  "version" "0.16.0"

"type-fest@^0.20.2":
  "integrity" "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^0.21.3", "type-fest@>=0.17.0 <3.0.0":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-is@~1.6.18":
  "integrity" "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="
  "resolved" "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"typedarray-to-buffer@^3.1.5":
  "integrity" "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q=="
  "resolved" "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "is-typedarray" "^1.0.0"

"typescript@^3.2.1 || ^4", "typescript@>= 2.7", "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta":
  "integrity" "sha512-VgYs2A2QIRuGphtzFV7aQJduJ2gyfTljngLzjpfW9FoYZF6xuw1W0vW9ghCKLfcWrCFxK81CSGRAvS1pn4fIUg=="
  "resolved" "https://registry.npmjs.org/typescript/-/typescript-4.5.4.tgz"
  "version" "4.5.4"

"ua-parser-js@^0.7.30":
  "integrity" "sha512-qLK/Xe9E2uzmYI3qLeOmI0tEOt+TBBQyUIAh4aAgU05FVYzeZrKUdkAZfBNVGRaHVgV0TDkdEngJSw/SyQchkQ=="
  "resolved" "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.31.tgz"
  "version" "0.7.31"

"unbox-primitive@^1.0.1":
  "integrity" "sha512-tZU/3NqK3dA5gpE1KtyiJUrEB0lxnGkMFHptJ7q6ewdZ8s12QrODwNbhIJStmJkd1QDXa1NRA8aF2A1zk/Ypyw=="
  "resolved" "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has-bigints" "^1.0.1"
    "has-symbols" "^1.0.2"
    "which-boxed-primitive" "^1.0.2"

"uncontrollable@^7.2.1":
  "integrity" "sha512-svtcfoTADIB0nT9nltgjujTi7BzVmwjZClOmskKu/E8FW9BXzg9os8OLr4f8Dlnk0rYWJIWr4wv9eKUXiQvQwQ=="
  "resolved" "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.2.1.tgz"
  "version" "7.2.1"
  dependencies:
    "@babel/runtime" "^7.6.3"
    "@types/react" ">=16.9.11"
    "invariant" "^2.2.4"
    "react-lifecycles-compat" "^3.0.4"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ=="
  "resolved" "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.0.0":
  "integrity" "sha512-7Yhkc0Ye+t4PNYzOGKedDhXbYIBe1XEQYQxOPyhcXNMJ0WCABqqj6ckydd6pWRZTHV4GuCPKdBAUiMc60tsKVw=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha512-5Zfuy9q/DFr4tfO7ZPeVXb1aPoeQSdeFMLpYuFebehDAhbuevLs5yxSZmIFN1tP5F9Wl4IpJrYojg85/zgyZHQ=="
  "resolved" "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unique-string@^2.0.0":
  "integrity" "sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg=="
  "resolved" "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "crypto-random-string" "^2.0.0"

"universalify@^0.1.2":
  "integrity" "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  "version" "0.1.2"

"universalify@^2.0.0":
  "integrity" "sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-2.0.0.tgz"
  "version" "2.0.0"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@~1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="
  "resolved" "https://registry.npmjs.org/unquote/-/unquote-1.1.1.tgz"
  "version" "1.1.1"

"upath@^1.2.0":
  "integrity" "sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg=="
  "resolved" "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz"
  "version" "1.2.0"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@~1.0.0":
  "integrity" "sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA=="
  "resolved" "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.2"
    "has-symbols" "^1.0.1"
    "object.getownpropertydescriptors" "^2.1.0"

"utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^8.3.2":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v8-compile-cache@^2.0.3":
  "integrity" "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA=="
  "resolved" "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"v8-to-istanbul@^8.1.0":
  "integrity" "sha512-FGtKtv3xIpR6BYhvgH8MI/y78oT7d8Au3ww4QIxymrCtZEh5b8gCw2siywE+puhEmuWKDtmfrvF5UlB298ut3w=="
  "resolved" "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    "convert-source-map" "^1.6.0"
    "source-map" "^0.7.3"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"w3c-hr-time@^1.0.2":
  "integrity" "sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ=="
  "resolved" "https://registry.npmjs.org/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "browser-process-hrtime" "^1.0.0"

"w3c-xmlserializer@^2.0.0":
  "integrity" "sha512-4tzD0mF8iSiMiNs30BiLO3EpfGLZUT2MSX/G+o7ZywDzliWQ3OPtTZ0PTC3B3ca1UAf4cJMHB+2Bf56EriJuRA=="
  "resolved" "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "xml-name-validator" "^3.0.0"

"walker@^1.0.7":
  "integrity" "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ=="
  "resolved" "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "makeerror" "1.0.12"

"warning@^4.0.0", "warning@^4.0.3":
  "integrity" "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w=="
  "resolved" "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "loose-envify" "^1.0.0"

"watchpack@^2.3.1":
  "integrity" "sha512-x0t0JuydIo8qCNctdDrn1OzH/qDzk2+rdCOC3YzumZ42fiMqmQ7T3xQurykYMhYfHaPHTp4ZxAx2NfUo1K6QaA=="
  "resolved" "https://registry.npmjs.org/watchpack/-/watchpack-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.1.2"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA=="
  "resolved" "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"web-vitals@^2.1.3":
  "integrity" "sha512-+ijpniAzcnQicXaXIN0/eHQAiV/jMt1oHGHTmz7VdAJPPkzzDhmoYPSpLgJTuFtUh+jCjxCoeTZPg7Ic+g8o7w=="
  "resolved" "https://registry.npmjs.org/web-vitals/-/web-vitals-2.1.3.tgz"
  "version" "2.1.3"

"webidl-conversions@^3.0.0":
  "integrity" "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  "version" "3.0.1"

"webidl-conversions@^4.0.2":
  "integrity" "sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz"
  "version" "4.0.2"

"webidl-conversions@^5.0.0":
  "integrity" "sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz"
  "version" "5.0.0"

"webidl-conversions@^6.1.0":
  "integrity" "sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-6.1.0.tgz"
  "version" "6.1.0"

"webpack-dev-middleware@^5.3.0":
  "integrity" "sha512-MouJz+rXAm9B1OTOYaJnn6rtD/lWZPy2ufQCH3BPs8Rloh/Du6Jze4p7AeLYHkVi0giJnYLaSGDC7S+GM9arhg=="
  "resolved" "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "colorette" "^2.0.10"
    "memfs" "^3.2.2"
    "mime-types" "^2.1.31"
    "range-parser" "^1.2.1"
    "schema-utils" "^4.0.0"

"webpack-dev-server@^4.6.0", "webpack-dev-server@3.x || 4.x":
  "integrity" "sha512-mlxq2AsIw2ag016nixkzUkdyOE8ST2GTy34uKSABp1c4nhjZvH90D5ZRR+UOLSsG4Z3TFahAi72a3ymRtfRm+Q=="
  "resolved" "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-4.7.3.tgz"
  "version" "4.7.3"
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/serve-index" "^1.9.1"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.2.2"
    "ansi-html-community" "^0.0.8"
    "bonjour" "^3.5.0"
    "chokidar" "^3.5.2"
    "colorette" "^2.0.10"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^1.6.0"
    "default-gateway" "^6.0.3"
    "del" "^6.0.0"
    "express" "^4.17.1"
    "graceful-fs" "^4.2.6"
    "html-entities" "^2.3.2"
    "http-proxy-middleware" "^2.0.0"
    "ipaddr.js" "^2.0.1"
    "open" "^8.0.9"
    "p-retry" "^4.5.0"
    "portfinder" "^1.0.28"
    "schema-utils" "^4.0.0"
    "selfsigned" "^2.0.0"
    "serve-index" "^1.9.1"
    "sockjs" "^0.3.21"
    "spdy" "^4.0.2"
    "strip-ansi" "^7.0.0"
    "webpack-dev-middleware" "^5.3.0"
    "ws" "^8.1.0"

"webpack-manifest-plugin@^4.0.2":
  "integrity" "sha512-YXUAwxtfKIJIKkhg03MKuiFAD72PlrqCiwdwO4VEXdRO5V0ORCNwaOwAZawPZalCbmH9kBDmXnNeQOw+BIEiow=="
  "resolved" "https://registry.npmjs.org/webpack-manifest-plugin/-/webpack-manifest-plugin-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "tapable" "^2.0.0"
    "webpack-sources" "^2.2.0"

"webpack-sources@^1.4.3":
  "integrity" "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack-sources@^2.2.0":
  "integrity" "sha512-y9EI9AO42JjEcrTJFOYmVywVZdKVUfOvDUPsJea5GIr1JOEGFVqwlY2K098fFoIjOkDzHn2AjRvM8dsBZu+gCA=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "source-list-map" "^2.0.1"
    "source-map" "^0.6.1"

"webpack-sources@^3.2.2":
  "integrity" "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  "version" "3.2.3"

"webpack@^4.0.0 || ^5.0.0", "webpack@^4.37.0 || ^5.0.0", "webpack@^4.4.0 || ^5.9.0", "webpack@^4.44.2 || ^5.47.0", "webpack@^5.0.0", "webpack@^5.1.0", "webpack@^5.20.0", "webpack@^5.64.4", "webpack@>= 4", "webpack@>=2", "webpack@>=4.43.0 <6.0.0":
  "integrity" "sha512-NJNtGT7IKpGzdW7Iwpn/09OXz9inIkeIQ/ibY6B+MdV1x6+uReqz/5z1L89ezWnpPDWpXF0TY5PCYKQdWVn8Vg=="
  "resolved" "https://registry.npmjs.org/webpack/-/webpack-5.66.0.tgz"
  "version" "5.66.0"
  dependencies:
    "@types/eslint-scope" "^3.7.0"
    "@types/estree" "^0.0.50"
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/wasm-edit" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "acorn" "^8.4.1"
    "acorn-import-assertions" "^1.7.6"
    "browserslist" "^4.14.5"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^5.8.3"
    "es-module-lexer" "^0.9.0"
    "eslint-scope" "5.1.1"
    "events" "^3.2.0"
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.2.9"
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^4.2.0"
    "mime-types" "^2.1.27"
    "neo-async" "^2.6.2"
    "schema-utils" "^3.1.0"
    "tapable" "^2.1.1"
    "terser-webpack-plugin" "^5.1.3"
    "watchpack" "^2.3.1"
    "webpack-sources" "^3.2.2"

"websocket-driver@^0.7.4", "websocket-driver@>=0.5.1":
  "integrity" "sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg=="
  "resolved" "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg=="
  "resolved" "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"whatwg-encoding@^1.0.5":
  "integrity" "sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw=="
  "resolved" "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "iconv-lite" "0.4.24"

"whatwg-fetch@^3.6.2":
  "integrity" "sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA=="
  "resolved" "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.2.tgz"
  "version" "3.6.2"

"whatwg-fetch@2.0.4":
  "integrity" "sha512-dcQ1GWpOD/eEQ97k66aiEVpNnapVj90/+R+SXTPYGHpYBBypfKJEQjLrvMZ7YXbKm21gXd4NcuxUTjiv1YtLng=="
  "resolved" "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-2.0.4.tgz"
  "version" "2.0.4"

"whatwg-mimetype@^2.3.0":
  "integrity" "sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g=="
  "resolved" "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz"
  "version" "2.3.0"

"whatwg-url@^5.0.0":
  "integrity" "sha1-lmRU6HZUYuN2RNNib2dCzotwll0="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "tr46" "~0.0.3"
    "webidl-conversions" "^3.0.0"

"whatwg-url@^7.0.0":
  "integrity" "sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "lodash.sortby" "^4.7.0"
    "tr46" "^1.0.1"
    "webidl-conversions" "^4.0.2"

"whatwg-url@^8.0.0", "whatwg-url@^8.5.0":
  "integrity" "sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz"
  "version" "8.7.0"
  dependencies:
    "lodash" "^4.7.0"
    "tr46" "^2.1.0"
    "webidl-conversions" "^6.1.0"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg=="
  "resolved" "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which@^1.3.1":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"word-wrap@^1.2.3", "word-wrap@~1.2.3":
  "integrity" "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ=="
  "resolved" "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"workbox-background-sync@6.4.2":
  "integrity" "sha512-P7c8uG5X2k+DMICH9xeSA9eUlCOjHHYoB42Rq+RtUpuwBxUOflAXR1zdsMWj81LopE4gjKXlTw7BFd1BDAHo7g=="
  "resolved" "https://registry.npmjs.org/workbox-background-sync/-/workbox-background-sync-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "idb" "^6.1.4"
    "workbox-core" "6.4.2"

"workbox-broadcast-update@6.4.2":
  "integrity" "sha512-qnBwQyE0+PWFFc/n4ISXINE49m44gbEreJUYt2ldGH3+CNrLmJ1egJOOyUqqu9R4Eb7QrXcmB34ClXG7S37LbA=="
  "resolved" "https://registry.npmjs.org/workbox-broadcast-update/-/workbox-broadcast-update-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-core" "6.4.2"

"workbox-build@6.4.2":
  "integrity" "sha512-WMdYLhDIsuzViOTXDH+tJ1GijkFp5khSYolnxR/11zmfhNDtuo7jof72xPGFy+KRpsz6tug39RhivCj77qqO0w=="
  "resolved" "https://registry.npmjs.org/workbox-build/-/workbox-build-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "@apideck/better-ajv-errors" "^0.3.1"
    "@babel/core" "^7.11.1"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.2"
    "@rollup/plugin-babel" "^5.2.0"
    "@rollup/plugin-node-resolve" "^11.2.1"
    "@rollup/plugin-replace" "^2.4.1"
    "@surma/rollup-plugin-off-main-thread" "^2.2.3"
    "ajv" "^8.6.0"
    "common-tags" "^1.8.0"
    "fast-json-stable-stringify" "^2.1.0"
    "fs-extra" "^9.0.1"
    "glob" "^7.1.6"
    "lodash" "^4.17.20"
    "pretty-bytes" "^5.3.0"
    "rollup" "^2.43.1"
    "rollup-plugin-terser" "^7.0.0"
    "source-map" "^0.8.0-beta.0"
    "source-map-url" "^0.4.0"
    "stringify-object" "^3.3.0"
    "strip-comments" "^2.0.1"
    "tempy" "^0.6.0"
    "upath" "^1.2.0"
    "workbox-background-sync" "6.4.2"
    "workbox-broadcast-update" "6.4.2"
    "workbox-cacheable-response" "6.4.2"
    "workbox-core" "6.4.2"
    "workbox-expiration" "6.4.2"
    "workbox-google-analytics" "6.4.2"
    "workbox-navigation-preload" "6.4.2"
    "workbox-precaching" "6.4.2"
    "workbox-range-requests" "6.4.2"
    "workbox-recipes" "6.4.2"
    "workbox-routing" "6.4.2"
    "workbox-strategies" "6.4.2"
    "workbox-streams" "6.4.2"
    "workbox-sw" "6.4.2"
    "workbox-window" "6.4.2"

"workbox-cacheable-response@6.4.2":
  "integrity" "sha512-9FE1W/cKffk1AJzImxgEN0ceWpyz1tqNjZVtA3/LAvYL3AC5SbIkhc7ZCO82WmO9IjTfu8Vut2X/C7ViMSF7TA=="
  "resolved" "https://registry.npmjs.org/workbox-cacheable-response/-/workbox-cacheable-response-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-core" "6.4.2"

"workbox-core@6.4.2":
  "integrity" "sha512-1U6cdEYPcajRXiboSlpJx6U7TvhIKbxRRerfepAJu2hniKwJ3DHILjpU/zx3yvzSBCWcNJDoFalf7Vgd7ey/rw=="
  "resolved" "https://registry.npmjs.org/workbox-core/-/workbox-core-6.4.2.tgz"
  "version" "6.4.2"

"workbox-expiration@6.4.2":
  "integrity" "sha512-0hbpBj0tDnW+DZOUmwZqntB/8xrXOgO34i7s00Si/VlFJvvpRKg1leXdHHU8ykoSBd6+F2KDcMP3swoCi5guLw=="
  "resolved" "https://registry.npmjs.org/workbox-expiration/-/workbox-expiration-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "idb" "^6.1.4"
    "workbox-core" "6.4.2"

"workbox-google-analytics@6.4.2":
  "integrity" "sha512-u+gxs3jXovPb1oul4CTBOb+T9fS1oZG+ZE6AzS7l40vnyfJV79DaLBvlpEZfXGv3CjMdV1sT/ltdOrKzo7HcGw=="
  "resolved" "https://registry.npmjs.org/workbox-google-analytics/-/workbox-google-analytics-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-background-sync" "6.4.2"
    "workbox-core" "6.4.2"
    "workbox-routing" "6.4.2"
    "workbox-strategies" "6.4.2"

"workbox-navigation-preload@6.4.2":
  "integrity" "sha512-viyejlCtlKsbJCBHwhSBbWc57MwPXvUrc8P7d+87AxBGPU+JuWkT6nvBANgVgFz6FUhCvRC8aYt+B1helo166g=="
  "resolved" "https://registry.npmjs.org/workbox-navigation-preload/-/workbox-navigation-preload-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-core" "6.4.2"

"workbox-precaching@6.4.2":
  "integrity" "sha512-CZ6uwFN/2wb4noHVlALL7UqPFbLfez/9S2GAzGAb0Sk876ul9ukRKPJJ6gtsxfE2HSTwqwuyNVa6xWyeyJ1XSA=="
  "resolved" "https://registry.npmjs.org/workbox-precaching/-/workbox-precaching-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-core" "6.4.2"
    "workbox-routing" "6.4.2"
    "workbox-strategies" "6.4.2"

"workbox-range-requests@6.4.2":
  "integrity" "sha512-SowF3z69hr3Po/w7+xarWfzxJX/3Fo0uSG72Zg4g5FWWnHpq2zPvgbWerBZIa81zpJVUdYpMa3akJJsv+LaO1Q=="
  "resolved" "https://registry.npmjs.org/workbox-range-requests/-/workbox-range-requests-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-core" "6.4.2"

"workbox-recipes@6.4.2":
  "integrity" "sha512-/oVxlZFpAjFVbY+3PoGEXe8qyvtmqMrTdWhbOfbwokNFtUZ/JCtanDKgwDv9x3AebqGAoJRvQNSru0F4nG+gWA=="
  "resolved" "https://registry.npmjs.org/workbox-recipes/-/workbox-recipes-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-cacheable-response" "6.4.2"
    "workbox-core" "6.4.2"
    "workbox-expiration" "6.4.2"
    "workbox-precaching" "6.4.2"
    "workbox-routing" "6.4.2"
    "workbox-strategies" "6.4.2"

"workbox-routing@6.4.2":
  "integrity" "sha512-0ss/n9PAcHjTy4Ad7l2puuod4WtsnRYu9BrmHcu6Dk4PgWeJo1t5VnGufPxNtcuyPGQ3OdnMdlmhMJ57sSrrSw=="
  "resolved" "https://registry.npmjs.org/workbox-routing/-/workbox-routing-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-core" "6.4.2"

"workbox-strategies@6.4.2":
  "integrity" "sha512-YXh9E9dZGEO1EiPC3jPe2CbztO5WT8Ruj8wiYZM56XqEJp5YlGTtqRjghV+JovWOqkWdR+amJpV31KPWQUvn1Q=="
  "resolved" "https://registry.npmjs.org/workbox-strategies/-/workbox-strategies-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-core" "6.4.2"

"workbox-streams@6.4.2":
  "integrity" "sha512-ROEGlZHGVEgpa5bOZefiJEVsi5PsFjJG9Xd+wnDbApsCO9xq9rYFopF+IRq9tChyYzhBnyk2hJxbQVWphz3sog=="
  "resolved" "https://registry.npmjs.org/workbox-streams/-/workbox-streams-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "workbox-core" "6.4.2"
    "workbox-routing" "6.4.2"

"workbox-sw@6.4.2":
  "integrity" "sha512-A2qdu9TLktfIM5NE/8+yYwfWu+JgDaCkbo5ikrky2c7r9v2X6DcJ+zSLphNHHLwM/0eVk5XVf1mC5HGhYpMhhg=="
  "resolved" "https://registry.npmjs.org/workbox-sw/-/workbox-sw-6.4.2.tgz"
  "version" "6.4.2"

"workbox-webpack-plugin@^6.4.1":
  "integrity" "sha512-CiEwM6kaJRkx1cP5xHksn13abTzUqMHiMMlp5Eh/v4wRcedgDTyv6Uo8+Hg9MurRbHDosO5suaPyF9uwVr4/CQ=="
  "resolved" "https://registry.npmjs.org/workbox-webpack-plugin/-/workbox-webpack-plugin-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "fast-json-stable-stringify" "^2.1.0"
    "pretty-bytes" "^5.4.1"
    "source-map-url" "^0.4.0"
    "upath" "^1.2.0"
    "webpack-sources" "^1.4.3"
    "workbox-build" "6.4.2"

"workbox-window@6.4.2":
  "integrity" "sha512-KVyRKmrJg7iB+uym/B/CnEUEFG9CvnTU1Bq5xpXHbtgD9l+ShDekSl1wYpqw/O0JfeeQVOFb8CiNfvnwWwqnWQ=="
  "resolved" "https://registry.npmjs.org/workbox-window/-/workbox-window-6.4.2.tgz"
  "version" "6.4.2"
  dependencies:
    "@types/trusted-types" "^2.0.2"
    "workbox-core" "6.4.2"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^3.0.0":
  "integrity" "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q=="
  "resolved" "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "imurmurhash" "^0.1.4"
    "is-typedarray" "^1.0.0"
    "signal-exit" "^3.0.2"
    "typedarray-to-buffer" "^3.1.5"

"ws@^7.4.6", "ws@>=7.4.6":
  "integrity" "sha512-6GLgCqo2cy2A2rjCNFlxQS6ZljG/coZfZXclldI8FB/1G3CCI36Zd8xy2HrFVACi8tfk5XrgLQEk+P0Tnz9UcA=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-7.5.6.tgz"
  "version" "7.5.6"

"ws@^8.1.0":
  "integrity" "sha512-Kbk4Nxyq7/ZWqr/tarI9yIt/+iNNFOjBXEWgTb4ydaNHBNGgvf2QHbS9fdfsndfjFlFwEd4Al+mw83YkaD10ZA=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-8.4.2.tgz"
  "version" "8.4.2"

"xml-name-validator@^3.0.0":
  "integrity" "sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw=="
  "resolved" "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz"
  "version" "3.0.0"

"xmlchars@^2.2.0":
  "integrity" "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw=="
  "resolved" "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"xtend@^4.0.2":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml@^1.10.0", "yaml@^1.10.2", "yaml@^1.7.2":
  "integrity" "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg=="
  "resolved" "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  "version" "1.10.2"

"yargs-parser@^20.2.2":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@^16.1.1", "yargs@^16.2.0":
  "integrity" "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yocto-queue@^0.1.0":
  "integrity" "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
  "resolved" "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"
