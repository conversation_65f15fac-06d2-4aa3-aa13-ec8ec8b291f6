{"name": "tkoh_elogbook-public", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/icons-material": "^5.8.2", "@mui/material": "^5.8.2", "@reduxjs/toolkit": "^1.7.1", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "bootstrap": "^5.1.3", "crypto-hash": "^2.0.1", "firebase": "^9.6.3", "lodash": "^4.17.21", "react": "^17.0.2", "react-bootstrap": "^2.1.1", "react-dom": "^17.0.2", "react-native-vector-icons": "^9.0.0", "react-native-web": "^0.17.5", "react-redux": "^7.2.6", "react-router-dom": "^6.2.1", "react-scripts": "5.0.0", "web-vitals": "^2.1.3"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test --env=jsdom", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-app-rewired": "^2.1.11"}}